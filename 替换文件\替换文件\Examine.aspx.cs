﻿using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Data.SqlClient;

public partial class Examine : System.Web.UI.Page
{
    //数据库连接字符串
    const string ConnStr = " Data Source=" + InputParams.DataBaseServer + "; uid=" + InputParams.userName + "; pwd=" + InputParams.password + "; DATABASE=" + InputParams.dbName;

    /// <summary>
    /// 页面加载
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>

    protected void Page_Load(object sender, EventArgs e)
    {
        btnConfirm.Attributes.Add("onclick", "return CheckForm(this);");

        if (!IsPostBack)
        {
            string strTaskID = Request.QueryString["taskID"].ToString();
            lblTaskID.Text = strTaskID;

            string strSql = "SELECT Description, Report,TaskName  FROM Tasks WHERE TaskID=" + int.Parse(strTaskID); ;
            SqlConnection conn = new SqlConnection(ConnStr);
            conn.Open();
            SqlCommand comm = new SqlCommand(strSql, conn);
            SqlDataReader dr = comm.ExecuteReader();
            if (dr.Read())
            {
                lblTaskInfo.Text = dr[0].ToString();
                lblReport.Text = dr[1].ToString();
                lblTaskName.Text = dr[2].ToString();
            }
            else
            {
                lblTaskInfo.Text = "";
                lblReport.Text = "";
                lblTaskName.Text = "";

            }

            dr.Close();
            conn.Close();
        }
    }


    /// <summary>
    /// 返回
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void hlkReturn_Click(object sender, EventArgs e)
    {
        if (Request.QueryString["info"] != null)
        {
            if (Request.QueryString["info"].ToString() == "SeeReport")
            {
                Response.Redirect("SeeReport.aspx");
            }
        }
        else
        {
            Response.Redirect("Default.aspx?id=2");
        }
    }


    /// <summary>
    /// 提交审核
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void btnConfirm_Click(object sender, EventArgs e)
    {
        int taskID = int.Parse(lblTaskID.Text.Trim().ToString());
        int iState = 6;//已审核，待关闭
        if (ddlIsPass.SelectedValue == "0")
        {
            iState = 0;//执行中
        }
        try
        {

            SqlConnection conn = new SqlConnection(ConnStr);
            if (conn.State.ToString() == "Closed")
            {
                conn.Open();
            }
            string strExamine = Body.Value.Trim();
            if (strExamine != "")
            {
                strExamine = strExamine.Replace("</P>", "");
                strExamine = strExamine.Replace("<P>", "");
                strExamine = strExamine.Replace("\r\n", "<br/>");
            }
            string strSql = " UPDATE Tasks SET Examine='" + strExamine + "', State=" + iState + "  WHERE TaskID=" + int.Parse(lblTaskID.Text.Trim().ToString());
            SqlCommand comm = new SqlCommand(strSql, conn);
            int tag = comm.ExecuteNonQuery();

            if (conn.State.ToString() == "Open")
            {
                conn.Close();
            }

            //if (tag == 1)
            //{
            //    Response.Redirect("Default.aspx?id=2");
            //}
            if (Request.QueryString["info"] != null)
            {
                if (Request.QueryString["info"].ToString() == "SeeReport")
                {
                    Response.Redirect("SeeReport.aspx");
                }
            }
            else
            {
                Response.Redirect("Default.aspx?id=2");
            }
            //else
            //{
            //    ClientScript.RegisterClientScriptBlock(this.GetType(), "info", "<script language=\"javascript\" type=\"text/javascript\">alert(\"审核提交失败！\");window.location.href='Default.aspx?id=2';</script>");

            //}
        }
        catch (SqlException ex)
        {
            Response.Write(ex.ToString());
        }
    }
}


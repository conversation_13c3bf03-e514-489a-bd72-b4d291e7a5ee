﻿using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Data.SqlClient;

public partial class SeeTask : System.Web.UI.Page
{
    string pKey = "id";
    const string ConnStr = " Data Source=" + InputParams.DataBaseServer + "; uid=" + InputParams.userName + "; pwd=" + InputParams.password + "; DATABASE=" + InputParams.dbName;

    public EnumField[][] EnumField()
    {
        EnumField[] enum0 ={new EnumField(0, "非目标客户"), new EnumField(1, "号码待确认"), new EnumField(2, "号码确认，无联系人"), 
                             new EnumField(3, "已有联系人"), new EnumField(4, "已发资料"), new EnumField(5, "已拜访过"),
                             new EnumField(6, "已看过供应商"), new EnumField(7, "试吃过,未成功"),
                             new EnumField(8, "正在试用中"), new EnumField(9, "正式合作中"),
                             new EnumField(10, "已流失")
                            };

        EnumField[] enum1 ={ new EnumField(0, " "), new EnumField(1, "★"), new EnumField(2, "★★"), new EnumField(3, " ★★★"), new EnumField(4, "★★★★"), new EnumField(5, "★★★★★") };
        EnumField[] enum2 ={ new EnumField(0, "盒饭"), new EnumField(1, "桶饭"), new EnumField(2, "食堂"), new EnumField(3, "个人解决") };

        EnumField[][] efTs ={ enum0,enum1,enum2 };
        return efTs;
    }

    public Table[] Tables()
    {
        string tblName1 = "FollowClient";

        Field fld0 = new Field(0, "firm_name", "公司名称", 0, EnumFieldType.charType, 0, 0, "", 0, "");
        Field fld2 = new Field(2, "telephone", "电话", 0, EnumFieldType.charType, 0, 0, "", 0, "");
        Field fld1 = new Field(1, "linkman", "联系人", 0, EnumFieldType.charType, 0, 0, "", 0, "");
        Field fld3 = new Field(3, "email", "Email", 0, EnumFieldType.charType, 0, 0, "", 0, "");
        Field fld4 = new Field(4, "userAddress", "地址", 0, EnumFieldType.charType, 0, 0, "", 0, "");
        Field fld5 = new Field(5, "is_unify_reservation", "订餐类型", 0, EnumFieldType.enumType, 0, 2, "", 0, "");
        Field fld6 = new Field(6, "is_telephone_client", "跟单客户", 0, EnumFieldType.boolType, 0, 0, "", 0, "");
        Field fld7 = new Field(7, "is_azw_client", "爱味知客户", 0, EnumFieldType.boolType, 0, 0, "", 0, "");
        Field fld8 = new Field(8, "firm_number", "公司人数", 0, EnumFieldType.numberType, 0, 0, "", 0, "");
        Field fld9 = new Field(9, "dinner_level", "用餐标准", 0, EnumFieldType.numberType, 0, 0, "", 0, "");
        Field fld10 = new Field(10, "interviewee_sex", "受访者性别", 0, EnumFieldType.boolType, 0, 0, "", 0, "");
        Field fld11 = new Field(11, "area", "区域", 0, EnumFieldType.enumType, 0, 0, "", 0, "");
        Field fld12 = new Field(12, "last_follow_time", "最后跟踪时间", 0, EnumFieldType.dateType, 0, 0, "", 0, "");
        Field fld13 = new Field(13, "client_state", "客户状态", 0, EnumFieldType.enumType, 0, 0, "", 0, "");
        Field fld14 = new Field(14, "presented", "赠送的投资", 0, EnumFieldType.numberType, 0, 0, "", 0, "");
        Field fld15 = new Field(15, "Type", "客户意向指数", 0, EnumFieldType.enumType, 0, 1, "", 0, "");
        Field fld16 = new Field(16, "userAddress", "地址", 0, EnumFieldType.charType, 0, 0, "", 0, "");
        Field fld17 = new Field(17, "next_follow_time", "预计下次回访时间", 0, EnumFieldType.dateType, 0, 0, "", 0, "");
        Field fld18 = new Field(18, "creat_time", "创建时间", 0, EnumFieldType.dateType, 0, 0, "", 0, "");
        Field fld19 = new Field(19, "investigate", "备注", 0, EnumFieldType.charType, 0, 0, "", 0, "");
        Field fld20 = new Field(20, "follow_number", "追加记录的次数", 0, EnumFieldType.numberType, 0, 0, "", 0, "");
        Field[] flds ={ fld0, fld1, fld2, fld3, fld4, fld5, fld6, fld7, fld8, fld9, fld10, fld11, fld12, fld13, fld14, fld15, fld16, fld17, fld18, fld19, fld20 };
        Join jon1 = new Join();
        Table table1 = new Table(tblName1, jon1, flds);

        Table[] tables ={ table1 };
        return tables; ;
    }

    protected void Page_Load(object sender, EventArgs e)
    {
        int TaskID = int.Parse(Request.QueryString["TaskID"].ToString());
        string strSql = GetSqlStr(TaskID);
        GenerateSeeMould(strSql);

        if (Request.QueryString["message"] != null)
        {
            ClientScript.RegisterClientScriptBlock(this.GetType(), "info", "<script language=\"javascript\" type=\"text/javascript\">alert(\"添加成功！\");window.location.href='See.aspx';</script>");

        }
    }

    protected void hlkReturn_Click(object sender, EventArgs e)
    {
        Response.Write("<script language:javascript>javascript:window.close();</script>"); 

        //Response.Redirect("See.aspx?id=2");
    }

    /// <summary>
    /// 生成查看模板
    /// </summary>
    protected void GenerateSeeMould(string strSql)
    {
        int flag = 0;
        placeHolder1.Controls.Clear();

        SqlConnection conn = new SqlConnection(ConnStr);
        conn.Open();
        SqlDataAdapter da = new SqlDataAdapter(strSql, conn);
        DataSet ds = new DataSet();
        da.Fill(ds);
        DataTable dt = ds.Tables[0];
        DataRow dr = dt.Rows[0];

        EnumField[][] efTs = EnumField();
        Table[] tables = Tables();

        #region forTable
        int colIndex = -1;
        for (int tableIndex = 0; tableIndex < tables.Length; tableIndex++)
        {
            #region forField
            for (int fieldIndex = 0; fieldIndex < tables[tableIndex].fields.Length; fieldIndex++)
            {
                colIndex++;
                Literal ltlBrs = new Literal();
                ltlBrs.Text = "<br/>";
                placeHolder1.Controls.Add(ltlBrs);


                Label lbl = new Label();
                lbl.Text = tables[tableIndex].fields[fieldIndex].fieldShowName + "：";
                lbl.ForeColor = System.Drawing.ColorTranslator.FromHtml(InputParams.lblFieldShowNameColor);
                placeHolder1.Controls.Add(lbl);

                #region switch
                string strFldName = tables[tableIndex].fields[fieldIndex].fieldName;

                switch (tables[tableIndex].fields[fieldIndex].fieldType)
                {

                    case EnumFieldType.numberType:
                        if (tables[tableIndex].fields[fieldIndex].fieldName == "PlanSpendTime")
                        {
                            int spendTime = int.Parse(dr[colIndex].ToString());
                            //以分钟为单位
                            //int spendYear = spendTime / (24 * 60 * 365);
                            //int spendMonth = (spendTime % (24 * 60 * 365)) / (24 * 60 * 30);
                            //int spendDay = (((spendTime % (24 * 60 * 365)) % (24 * 60 * 30)) / (24 * 60));
                            //int spendHour = (((spendTime % (24 * 60 * 365)) % (24 * 60 * 30)) % (24 * 60)) / 60;
                            //int spendMinute = ((((spendTime % (24 * 60 * 365)) % (24 * 60 * 30)) % (24 * 60))) % 60;

                            int spendHour = spendTime / 60;
                            int spendMinute = spendTime % 60;

                            string strSpendTime = "";
                            //if (spendYear != 0)
                            //{
                            //    strSpendTime += spendYear + "年";
                            //}
                            //if (spendMonth != 0)
                            //{
                            //    strSpendTime += spendMonth + "个月";

                            //}
                            //if (spendDay != 0)
                            //{
                            //    strSpendTime += spendDay + "天";

                            //}
                            if (spendHour != 0)
                            {
                                strSpendTime += spendHour + "小时";

                            }
                            if (spendMinute != 0)
                            {
                                strSpendTime += spendMinute + "分钟";
                            }

                            Literal ltlN = new Literal();
                            ltlN.Text = strSpendTime;
                            placeHolder1.Controls.Add(ltlN);

                        }
                        else
                        {
                            Literal ltlN = new Literal();
                            ltlN.Text = dr[colIndex].ToString();
                            placeHolder1.Controls.Add(ltlN);
                        }
                        continue;

                    case EnumFieldType.boolType:

                        string text = "";
                        if (flag!= 2)
                        {
                            if (int.Parse(dr[colIndex].ToString()) == 1)
                                text = "是";
                            else
                                text = "否";
                            flag++;

                        }
                        else
                        {
                            if (int.Parse(dr[colIndex].ToString()) == 1)
                                text = "男";
                            else
                                text = "女";
                            flag = 1;
                        }

                        Literal ltlB = new Literal();
                        ltlB.Text = text;
                        placeHolder1.Controls.Add(ltlB);
                        continue;

                    //case EnumFieldType.picType:

                    //    continue;

                    case EnumFieldType.enumType:
                        int enumTag = tables[tableIndex].fields[fieldIndex].enumTag;
                        int enumItem = int.Parse(dr[colIndex].ToString());
                        Literal ltlE = new Literal();

                        if (tables[tableIndex].fields[fieldIndex].fieldName == "area")
                            ltlE.Text = (new Common()).GetTown(enumItem);
                        else
                        ltlE.Text = efTs[enumTag][enumItem].itemDetail;
                        placeHolder1.Controls.Add(ltlE);
                        continue;


                    case EnumFieldType.longcharType:

                        if (dr[colIndex].ToString() != "")
                        {
                            Literal ltlBr1 = new Literal();
                            ltlBr1.Text = "<br/>";
                            placeHolder1.Controls.Add(ltlBr1);


                            Label lblD1 = new Label();
                            lblD1.Text = "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" + dr[colIndex].ToString();
                            lblD1.ForeColor = System.Drawing.ColorTranslator.FromHtml("#0000FF");
                            //lblD1.BackColor = System.Drawing.ColorTranslator.FromHtml("#F6FFFF");
                            //lblD1.Height = 50;
                            lblD1.Width = 700;
                            placeHolder1.Controls.Add(lblD1);

                            ////生成换行符
                            //Literal ltlBr2 = new Literal();
                            //ltlBr2.Text = "<br/>";
                            //placeHolder1.Controls.Add(ltlBr2);
                        }
                        continue;

                    //case EnumFieldType.dateType:
                    //    continue;
                    //case EnumFieldType.charType:
                    //    continue;

                    default:
                        if ((dr[colIndex] != null) && (dr[colIndex].ToString() != ""))
                        {
                            Literal ltlT = new Literal();
                            ltlT.Text = dr[colIndex].ToString();
                            placeHolder1.Controls.Add(ltlT);
                        }
                        continue;

                }

                #endregion switch
            }
            #endregion forField
        }
        #endregion  forTable

        //添加追加记录

        Literal ltlRecord = new Literal();

        ltlRecord.Text = "<br> <p style='color:#871F78;'>追加记录：</p><br>" + (new Common()).GetRecord(int.Parse(Request.QueryString["TaskID"].ToString()));

        placeHolder1.Controls.Add(ltlRecord);

    }

    /// <summary>
    /// 获得Sql字符串
    /// </summary>
    /// <returns></returns>
    protected string GetSqlStr(int TaskID)
    {
        EnumField[][] efTs = EnumField();
        Table[] tables = Tables();

        string strSelect = "SELECT  ";
        string strFrom = " FROM ";
        string strWhere = " WHERE " + pKey + "=" + TaskID;

        #region forTable
        for (int tableIndex = 0; tableIndex < tables.Length; tableIndex++)
        {
            string strTblName = tables[tableIndex].tableName;

            if (tableIndex == 0)
            {
                strFrom += strTblName;
            }
            else
            {
                strFrom += "," + strTblName;
            }
            if (tables[tableIndex].join.joinField != null)
            {
                strWhere += " AND " + strTblName + "." + tables[tableIndex].join.joinField + "=" + tables[tableIndex].join.joinRTable + "." + tables[tableIndex].join.joinRField;
            }

            #region forField
            for (int fieldIndex = 0; fieldIndex < tables[tableIndex].fields.Length; fieldIndex++)
            {
                if (!((tableIndex == 0) && (fieldIndex == 0)))
                {
                    strSelect += ",";
                }
                strSelect += tables[tableIndex].tableName + "." + tables[tableIndex].fields[fieldIndex].fieldName;
            }
            #endregion forField
        }
        #endregion forTable
        string strSql = strSelect + strFrom + strWhere;
        return strSql;
    }
}

﻿using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Data.SqlClient;
using System.Collections.Generic;
using System.Text;
/// <summary>
/// 用来定义一些公用的方法
/// </summary>
public class Common
{

    static readonly string connStr = InputParams.connectionStr;

    public const int SORT_ID = 7;


    /// <summary>
    /// 构造函数
    /// </summary>
	public Common()
    {
        //
        // TODO: 在此处添加构造函数逻辑
        //
    }

    //判断是否是移动端
    public bool isMobile()
    {
        var context = HttpContext.Current;
        if (context == null)
            return false;

        var userAgent = context.Request.UserAgent?.ToLower() ?? string.Empty;
        string[] mobileDevices = { "iphone", "android", "windows phone", "mobile" };
        for (int i = 0; i < mobileDevices.Length; i++)
        {
            if (userAgent.Contains(mobileDevices[i]))
                return true;
        }

        return false;
    }


    /// <summary>
    /// 通过一个表和表的id属性名与id值,以及属性名,获取指定的属性值,返回为字符型
    /// </summary>
    /// <param name="task_id"></param>
    /// <returns></returns>
    public string GetAttributeFromTable(string idName, int id, string table, string attributeName)
    {
        SqlConnection conn = new SqlConnection(connStr);

        string Name = "";


        if (conn.State.ToString() == "Closed")
        {
            conn.Open();
        }

        string strSql = "select " + attributeName + " from " + table + " where " + idName + "=" + id;
        SqlCommand comm = new SqlCommand(strSql, conn);
        SqlDataReader dr = comm.ExecuteReader();
        if (dr.Read())
        {

            Name = dr[attributeName].ToString();

        }
        dr.Close();
        if (conn.State.ToString() == "Open")
        {
            conn.Close();
        }

        return Name;

    }


    /// <summary>
    /// 获得用户的密码
    /// </summary>
    /// <param name="task_id"></param>
    /// <returns></returns>
    public string GetTown(int id)
    {
        SqlConnection conn = new SqlConnection(connStr);

        string Name = "";


        if (conn.State.ToString() == "Closed")
        {
            conn.Open();
        }

        string strSql = "select name from ArTown where town_id =" + id;
        SqlCommand comm = new SqlCommand(strSql, conn);
        SqlDataReader dr = comm.ExecuteReader();
        if (dr.Read())
        {

            Name = dr["name"].ToString();

        }
        dr.Close();
        if (conn.State.ToString() == "Open")
        {
            conn.Close();
        }

        return Name;

    }



    /// <summary>
    /// 获得用户的密码
    /// </summary>
    /// <param name="task_id"></param>
    /// <returns></returns>
    /// 

    public string GetRecord(int id)
    {
        SqlConnection conn = new SqlConnection(connStr);

        string record = "";

        if (conn.State.ToString() == "Closed")
        {
            conn.Open();
        }

        string strSql = "select description,follow_time,follow_executor from FollowRecord where follow_client_id =" + id + " order by follow_time DESC";
        SqlCommand comm = new SqlCommand(strSql, conn);
        SqlDataReader dr = comm.ExecuteReader();
        

        if (isMobile())
        {
            record += "<div id ='records' class='section hidden'><h1>跟踪记录</h1>";
            // 移动端：卡片列表
            while (dr.Read())
            {
                string nickname = UserID_to_Nickname(int.Parse(dr["follow_executor"].ToString()));
                string time = dr["follow_time"].ToString();
                string desc = dr["description"].ToString();

                record += "<div style='border-radius: 16px; box-shadow: 0 4px 6px -1px rgba(0,0,0,0.1), 0 2px 4px -1px rgba(0,0,0,0.06); padding: 16px; background: white; margin-bottom: 16px;'>";
                record += "<div class='record' style='display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;'>";
                record += $"<span style='font-weight: bold;'>{nickname}</span>";
                record += $"<span style='font-size: 14px; color: #6b7280;'>{time}</span>";
                record += "</div>";
                record += $"<div class='record' style='color: #374151;'>{desc}</div>";
                record += "</div>";
            }
            record += "</div>";
        }
        else
        {
            // 桌面端：表格
            record += "<table border='1' style='text-align:center; width:100%; border-collapse: collapse;'>";
            record += "<tr style='background-color: #f0f0f0;'>";
            record += "<th style='width:450px; padding: 8px;'>内容</th>";
            record += "<th style='padding: 8px;'>跟踪时间</th>";
            record += "<th style='padding: 8px;'>跟踪者</th>";
            record += "</tr>";

            while (dr.Read())
            {
                record += "<tr>";
                record += "<td style='width:450px; padding: 8px; text-align:left;'>" + dr["description"].ToString() + "</td>";
                record += "<td style='padding: 8px;'>" + dr["follow_time"].ToString() + "</td>";
                record += "<td style='padding: 8px;'>" + UserID_to_Nickname(int.Parse(dr["follow_executor"].ToString())) + "</td>";
                record += "</tr>";
            }
            record += "</table>";
        }

        dr.Close();
        if (conn.State.ToString() == "Open")
        {
            conn.Close();
        }

        return record;
    }
   // public string GetRecord(int id)
   // {
   //     SqlConnection conn = new SqlConnection(connStr);

   //     string record = "";
   //     if (isMobile())
   //     {
   //         record += "<table >"; /*style = 'text-align:left;' */
   //     }
   //     else {
   //         record += "<table border='1' style='text-align:center;'><tr><td style='width:450px;'>内容</td><td>跟踪时间</td><td>跟踪者</td>";
   //         //record += "<table border='1' style='text-align:center;'><tr>< td style = 'width:450px;' > 内容 </ td >< td > 跟踪时间 </ td >< td > 跟踪者 </ td ></tr>";
   //     }
        

   //     if (conn.State.ToString() == "Closed")
   //     {
   //         conn.Open();
   //     }

   //     string strSql = "select description,follow_time,follow_executor from FollowRecord where follow_client_id =" + id + " order by follow_time DESC";
   //     SqlCommand comm = new SqlCommand(strSql, conn);
   //     SqlDataReader dr = comm.ExecuteReader();
   //     bool isFirstRow = true;
   //     while (dr.Read())
   //     {


   //         if (isMobile())
   //         {
   //             if (isFirstRow)
   //             {
   //                 //Console.WriteLine("处理第一条特殊逻辑...");
   //                 record += "<tr><td style='display: flex;justify-content: space-between;font-size:16px;color:gray;padding-top: 5px;width: 97vw;box-sizing:border-box; '>"
   //                     + "<span>" + UserID_to_Nickname(int.Parse(dr["follow_executor"].ToString())) + "</span>"
   //                     + "<span>" + dr["follow_time"].ToString() + "</span>" + "</td></tr>" +
   //                     "<tr><td style='font-size:16px;border-bottom: 1px solid #cccccc; padding-bottom: 8px;'>"
   //                     + dr["description"].ToString() + "</td></tr>";


   //                 isFirstRow = false;
   //             }
   //             else {
   //                 record += "<tr><td style='display: flex;justify-content: space-between;font-size:16px;color:gray;padding-top: 18px;width: 97vw;box-sizing:border-box; '>"
   //                     + "<span>" + UserID_to_Nickname(int.Parse(dr["follow_executor"].ToString())) + "</span>"
   //                     + "<span>" + dr["follow_time"].ToString() + "</span>" + "</td></tr>" +
   //                     "<tr><td style='font-size:16px;border-bottom: 1px solid #cccccc; padding-bottom: 8px;'>"
   //                     + dr["description"].ToString() + "</td></tr>";
   //             }
   //             //"<tr><td style='font-size:17px;color:gray;'>" + dr["follow_time"].ToString() + "</td></tr>";
   //             //+ "<tr><td style='width:450px;>"+"————————————————————"+"</td></tr>";
   //         }
   //         else
   //         {
			//	record += "<tr><td style='width:450px'>" + dr["description"].ToString() + "</td><td>" + dr["follow_time"].ToString() + "</td><td>" + UserID_to_Nickname(int.Parse(dr["follow_executor"].ToString())) + "</td></tr>";
			//}
   //         //record += "<tr><td style='width:450px'>" + dr["description"].ToString() + "</td><td>" + dr["follow_time"].ToString() + "</td><td>" + UserID_to_Nickname(int.Parse(dr["follow_executor"].ToString())) + "</td></tr>";
   //         //record += "<tr><td style='width:450px'>" + UserID_to_Nickname(int.Parse(dr["follow_executor"].ToString())) + "</td></tr>" +
   //         //    "<tr><td style='width:450px'>" + dr["description"].ToString() + "</td></tr>"+ "<tr><td>" + dr["follow_time"].ToString() + "</td></tr>";
   //     }
   //     dr.Close();
   //     if (conn.State.ToString() == "Open")
   //     {
   //         conn.Close();
   //     }

   //     record += "</table>";

   //     return record;

   // }

    /// <summary>
    /// 获得用户的密码
    /// </summary>
    /// <param name="task_id"></param>
    /// <returns></returns>
    public string GetPassword(int user_id)
    {
        SqlConnection conn = new SqlConnection(connStr);

        string password = "";


        if (conn.State.ToString() == "Closed")
        {
            conn.Open();
        }

        string strSql = "select password from Users where user_id =" + user_id;
        SqlCommand comm = new SqlCommand(strSql, conn);
        SqlDataReader dr = comm.ExecuteReader();
        if (dr.Read())
        {

            password = dr["password"].ToString();

        }
        dr.Close();
        if (conn.State.ToString() == "Open")
        {
            conn.Close();
        }



        return password;

    }
    /// <summary>
    /// 根据用户的ID而获得用户姓名
    /// </summary>
    /// <param name="task_id"></param>
    /// <returns></returns>
    public string GetNickname(int user_id)
    {
        SqlConnection conn = new SqlConnection(connStr);

        string name = "";


        if (conn.State.ToString() == "Closed")
        {
            conn.Open();
        }

        string strSql = "select nickname from Users where Users.user_id =" + user_id;
        SqlCommand comm = new SqlCommand(strSql, conn);
        SqlDataReader dr = comm.ExecuteReader();
        if (dr.Read())
        {

            name = dr["nickname"].ToString();

        }
        dr.Close();
        if (conn.State.ToString() == "Open")
        {
            conn.Close();
        }

        return name;

    }



    /// <summary>
    /// 根据加盟商的而获得添加加盟人的姓名
    /// </summary>
    /// <param name="task_id"></param>
    /// <returns></returns>
    public string GetCooperateUserName(int record_id)
    {
        SqlConnection conn = new SqlConnection(connStr);

        string name = "";


        if (conn.State.ToString() == "Closed")
        {
            conn.Open();
        }

        string strSql = "select nickname from Users,Cooperation where Users.user_id =Cooperation.executor_id and Cooperation.id=" + record_id;
        SqlCommand comm = new SqlCommand(strSql, conn);
        SqlDataReader dr = comm.ExecuteReader();
        if (dr.Read())
        {

            name = dr["nickname"].ToString();

        }
        dr.Close();
        if (conn.State.ToString() == "Open")
        {
            conn.Close();
        }



        return name;

    }


    /// <summary>
    /// 根据记录的ID而获得用户姓名
    /// </summary>
    /// <param name="task_id"></param>
    /// <returns></returns>
    public string GetUserName(int record_id)
    {
        SqlConnection conn = new SqlConnection(connStr);

        string name = "";


        if (conn.State.ToString() == "Closed")
        {
            conn.Open();
        }

        string strSql = "select nickname from Users,FollowClient where Users.user_id =FollowClient.executor_id and FollowClient.id=" + record_id;
        SqlCommand comm = new SqlCommand(strSql, conn);
        SqlDataReader dr = comm.ExecuteReader();
        if (dr.Read())
        {

            name = dr["nickname"].ToString();

        }
        dr.Close();
        if (conn.State.ToString() == "Open")
        {
            conn.Close();
        }



        return name;

    }

    /// <summary>
    /// 根据推荐记录的ID而获得用户姓名
    /// </summary>
    /// <param name="task_id"></param>
    /// <returns></returns>
    public string GetExecotorNameFormRecommend(int id)
    {
        SqlConnection conn = new SqlConnection(connStr);

        string name = "";


        if (conn.State.ToString() == "Closed")
        {
            conn.Open();
        }

        string strSql = "select nickname from Users,RecommendList where Users.user_id =RecommendList.executor_id and RecommendList.id=" + id;
        SqlCommand comm = new SqlCommand(strSql, conn);
        SqlDataReader dr = comm.ExecuteReader();
        if (dr.Read())
        {

            name = dr["nickname"].ToString();

        }
        dr.Close();
        if (conn.State.ToString() == "Open")
        {
            conn.Close();
        }



        return name;

    }

    /// <summary>
    /// 根据记录的ID而获得区域ID
    /// </summary>
    /// <param name="task_id"></param>
    /// <returns></returns>
    public string GetAreaID(int record_id)
    {
        SqlConnection conn = new SqlConnection(connStr);

        string area = "";


        if (conn.State.ToString() == "Closed")
        {
            conn.Open();
        }

        string strSql = "select area from FollowClient where FollowClient.id=" + record_id;
        SqlCommand comm = new SqlCommand(strSql, conn);
        SqlDataReader dr = comm.ExecuteReader();
        if (dr.Read())
        {

            area = dr["area"].ToString();

        }
        dr.Close();
        if (conn.State.ToString() == "Open")
        {
            conn.Close();
        }



        return area;

    }
    /// <summary>
    /// 根据记录的ID而获得执行者ID
    /// </summary>
    /// <param name="task_id"></param>
    /// <returns></returns>
    public string GetExecutorID(int record_id)
    {
        SqlConnection conn = new SqlConnection(connStr);

        string executor_id = "";


        if (conn.State.ToString() == "Closed")
        {
            conn.Open();
        }

        string strSql = "select executor_id from FollowClient where FollowClient.id=" + record_id;
        SqlCommand comm = new SqlCommand(strSql, conn);
        SqlDataReader dr = comm.ExecuteReader();
        if (dr.Read())
        {

            executor_id = dr["executor_id"].ToString();

        }
        dr.Close();
        if (conn.State.ToString() == "Open")
        {
            conn.Close();
        }



        return executor_id;

    }

    // 用户id昵称映射缓存
    private static Dictionary<int, string> userNicknameCache = new Dictionary<int, string>();
    /// <summary>
    /// 根据用户的ID而获得用户姓名
    /// </summary>
    /// <param name="task_id"></param>
    /// <returns></returns>
    public string UserID_to_Nickname(int user_id)
    {
        if (userNicknameCache.ContainsKey(user_id))
        {
            //Logger.Log($"命中缓存:{user_id} -> {userNicknameCache[user_id]}");
            return userNicknameCache[user_id];
        }

        SqlConnection conn = new SqlConnection(connStr);
        string name = "";

        if (conn.State.ToString() == "Closed")
        {
            conn.Open();
        }

        string strSql = "select nickname from Users where Users.user_id=" + user_id;
        SqlCommand comm = new SqlCommand(strSql, conn);
        SqlDataReader dr = comm.ExecuteReader();
        if (dr.Read())
        {
            name = dr["nickname"].ToString();
            userNicknameCache[user_id] = name;  // 缓存昵称
        }
        dr.Close();
        if (conn.State.ToString() == "Open")
        {
            conn.Close();
        }

        return name;

    }
    /// <summary>
    /// 根据加盟商的ID而获得加盟商名称
    /// </summary>
    /// <param name="task_id"></param>
    /// <returns></returns>
    public string CooperateID_to_Firmname(int id)
    {
        SqlConnection conn = new SqlConnection(connStr);

        string name = "";


        if (conn.State.ToString() == "Closed")
        {
            conn.Open();
        }

        string strSql = "select firm_name from Cooperation where id=" + id;
        SqlCommand comm = new SqlCommand(strSql, conn);
        SqlDataReader dr = comm.ExecuteReader();
        if (dr.Read())
        {

            name = dr["firm_name"].ToString();

        }
        dr.Close();
        if (conn.State.ToString() == "Open")
        {
            conn.Close();
        }



        return name;

    }

    /// <summary>
    /// 根据客户的ID而获得客户名称
    /// </summary>
    /// <param name="task_id"></param>
    /// <returns></returns>
    public string ClientID_to_Firmname(int id)
    {
        SqlConnection conn = new SqlConnection(connStr);

        string name = "";


        if (conn.State.ToString() == "Closed")
        {
            conn.Open();
        }

        string strSql = "select firm_name from FollowClient where id=" + id;
        SqlCommand comm = new SqlCommand(strSql, conn);
        SqlDataReader dr = comm.ExecuteReader();
        if (dr.Read())
        {

            name = dr["firm_name"].ToString();

        }
        dr.Close();
        if (conn.State.ToString() == "Open")
        {
            conn.Close();
        }



        return name;

    }
    /// <summary>
    /// 确认订单
    /// </summary>
    /// <param name="id"></param>
    /// <param name="mode"></param>
    public void UpdateShoping(string[] id, string mode, string remark)
    {

        string[] shoping_id = id;



        //SqlConnection conn = new SqlConnection(connStr);

        //string decription = "";
        //string shopingsID = "";
        //string mail_mode = "";
        //int price = 0;

        //if (conn.State.ToString() == "Closed")
        //{
        //    conn.Open();
        //}

        //string strSql = "update Images  set state=2 where id =" + id;
        //SqlCommand comm = new SqlCommand(strSql, conn);
        //comm.ExecuteNonQuery();

        //if (conn.State.ToString() == "Open")
        //{
        //    conn.Close();
        //}











    }
    /// <summary>
    /// 设置图片的状态
    /// </summary>
    /// <param name="task_id"></param>
    /// <returns></returns>
    public void setState(int id)
    {
        SqlConnection conn = new SqlConnection(connStr);

        string src = "";


        if (conn.State.ToString() == "Closed")
        {
            conn.Open();
        }

        string strSql = "update Shoping set state=2 where id =" + id;
        SqlCommand comm = new SqlCommand(strSql, conn);
        comm.ExecuteNonQuery();

        if (conn.State.ToString() == "Open")
        {
            conn.Close();
        }

    }

    /// <summary>
    /// 获得图片的路径
    /// </summary>
    /// <param name="task_id"></param>
    /// <returns></returns>
    public string GetPicPath(int group_id)
    {
        SqlConnection conn = new SqlConnection(connStr);

        string src = "";


        if (conn.State.ToString() == "Closed")
        {
            conn.Open();
        }

        string strSql = "select src,name,topic from Images where is_group =" + group_id + " order by id DESC";
        SqlCommand comm = new SqlCommand(strSql, conn);
        SqlDataReader dr = comm.ExecuteReader();
        if (dr.Read())
        {

            src = dr["src"].ToString();

        }
        dr.Close();
        if (conn.State.ToString() == "Open")
        {
            conn.Close();
        }



        return src;

    }

    /// <summary>
    /// 设置用户的密码
    /// </summary>
    /// <param name="task_id"></param>
    /// <returns></returns>
    public void SetPassword(int user_id, string password)
    {
        SqlConnection conn = new SqlConnection(connStr);

        if (conn.State.ToString() == "Closed")
        {
            conn.Open();
        }

        string strSql = "update Users set password='" + password + "' where user_id =" + user_id;
        SqlCommand comm = new SqlCommand(strSql, conn);
        comm.ExecuteNonQuery();

        if (conn.State.ToString() == "Open")
        {
            conn.Close();
        }

    }


    /// <summary>
    /// 把任务ID转换为相应的用户名
    /// </summary>
    /// <param name="task_id"></param>
    /// <returns></returns>
    public string GetSize(int group_id)
    {
        SqlConnection conn = new SqlConnection(connStr);
        //string[] RecordIDs;
        string size_width = "";
        string size_height = "";
        //string ExecuteRecord = "";

        if (conn.State.ToString() == "Closed")
        {
            conn.Open();
        }

        string strSql = "select size_width,size_height from groups where id =" + group_id;
        SqlCommand comm = new SqlCommand(strSql, conn);
        SqlDataReader dr = comm.ExecuteReader();
        if (dr.Read())
        {
            //LastRecordID = dr["LastRecordID"].ToString();
            size_width = dr["size_width"].ToString();
            size_height = dr["size_height"].ToString();
        }
        dr.Close();
        if (conn.State.ToString() == "Open")
        {
            conn.Close();
        }

        if (size_height == "" && size_width == "")

            return "还没有设定";
        else

            return size_width + " X " + size_height;

    }


    /// <summary>
    /// 获取商品的价格
    /// </summary>
    /// <param name="task_id"></param>
    /// <returns></returns>
    public float GetPrice(int image_id)
    {
        SqlConnection conn = new SqlConnection(connStr);
        //string[] RecordIDs;
        string size_width = "";
        string size_height = "";
        //string ExecuteRecord = "";
        float price = 0;

        if (conn.State.ToString() == "Closed")
        {
            conn.Open();
        }

        string strSql = "select value from Images where id =" + image_id;
        SqlCommand comm = new SqlCommand(strSql, conn);
        SqlDataReader dr = comm.ExecuteReader();
        if (dr.Read())
        {
            price = float.Parse(dr["value"].ToString());

        }
        dr.Close();
        if (conn.State.ToString() == "Open")
        {
            conn.Close();
        }

        return price;
    }

    /// <summary>
    /// 把用户ID得到相应邮编
    /// </summary>
    /// <param name="task_id"></param>
    /// <returns></returns>
    public string GetNumber(int id)
    {
        SqlConnection conn = new SqlConnection(connStr);
        string number = "";
        if (conn.State.ToString() == "Closed")
        {
            conn.Open();
        }

        string strSql = "select number from Users where user_id=" + id;
        SqlCommand comm = new SqlCommand(strSql, conn);
        SqlDataReader dr = comm.ExecuteReader();
        if (dr.Read())
        {

            number = dr["number"].ToString();
        }
        dr.Close();
        if (conn.State.ToString() == "Open")
        {
            conn.Close();
        }
        return number;

    }


    /// <summary>
    /// 把用户ID得到相应邮编
    /// </summary>
    /// <param name="task_id"></param>
    /// <returns></returns>
    public string GetAddress(int id)
    {
        SqlConnection conn = new SqlConnection(connStr);
        string address = "";
        if (conn.State.ToString() == "Closed")
        {
            conn.Open();
        }

        string strSql = "select address from Users where user_id=" + id;
        SqlCommand comm = new SqlCommand(strSql, conn);
        SqlDataReader dr = comm.ExecuteReader();
        if (dr.Read())
        {

            address = dr["address"].ToString();
        }
        dr.Close();
        if (conn.State.ToString() == "Open")
        {
            conn.Close();
        }
        return address;

    }

    /// <summary>
    /// 通过用户ID号得出该用户所有的数目
    /// </summary>
    /// <param name="task_id"></param>
    /// <returns></returns>
    public double GetTotalShoping(int id)
    {

        double count = 0;//定义总的金额
        SqlConnection conn = new SqlConnection(connStr);
        string number = "";
        if (conn.State.ToString() == "Closed")
        {
            conn.Open();
        }

        string strSql = "select sum(num*price)as '总和' from Shoping where user_id=" + id;
        SqlCommand comm = new SqlCommand(strSql, conn);
        SqlDataReader dr = comm.ExecuteReader();
        if (dr.Read())
        {
            if (dr["总和"].ToString() != "")

                count = double.Parse(dr["总和"].ToString());
        }
        dr.Close();
        if (conn.State.ToString() == "Open")
        {
            conn.Close();
        }
        return count;



    }

    /// <summary>
    /// 并返回任务表中最大的任务ID
    /// </summary>

    public int GetMaxFollowClientID()
    {
        SqlConnection conn = new SqlConnection(connStr);
        int ID = 0;

        if (conn.State.ToString() == "Closed")
        {
            conn.Open();
        }

        string strGetID = "select max(id) as 'id' from FollowClient";
        SqlCommand comm1 = new SqlCommand(strGetID, conn);
        SqlDataReader dr = comm1.ExecuteReader();
        if (dr.Read())
        {
            ID = int.Parse(dr[0].ToString());
        }
        dr.Close();

        if (conn.State.ToString() == "Open")
        {
            conn.Close();
        }

        return ID;
    }


    /// <summary>
    /// 创建一条空记录,并返回该记录的ID
    /// </summary>

    public int InsertEmptyRecord()
    {
        SqlConnection conn = new SqlConnection(connStr);
        int ID = 0;

        if (conn.State.ToString() == "Closed")
        {
            conn.Open();
        }

        string strSql = "insert into  ExecuteRecord(TaskID) values('')";
        SqlCommand comm = new SqlCommand(strSql, conn);
        comm.ExecuteNonQuery();


        string strGetID = "select max(record_id) as 'id' from ExecuteRecord";
        SqlCommand comm1 = new SqlCommand(strGetID, conn);
        SqlDataReader dr = comm1.ExecuteReader();
        if (dr.Read())
        {
            ID = int.Parse(dr[0].ToString());
        }
        dr.Close();

        if (conn.State.ToString() == "Open")
        {
            conn.Close();
        }

        return ID;
    }



    /// <summary>
    /// 把最后的记录添加到任务的记录中
    /// </summary>
    /// <param name="ID"></param>
    public void TaskAddRecord(int task_id, int record_id)
    {
        SqlConnection conn = new SqlConnection(connStr);
        // int ID = 0;
        string LastRecordID = "";
        string ExecuteRecord = "";

        if (conn.State.ToString() == "Closed")
        {
            conn.Open();
        }

        string strSql = "select LastRecordID,ExecuteRecord from Tasks where TaskID=" + task_id;
        SqlCommand comm = new SqlCommand(strSql, conn);
        SqlDataReader dr = comm.ExecuteReader();
        if (dr.Read())
        {
            LastRecordID = dr["LastRecordID"].ToString();
            ExecuteRecord = dr["ExecuteRecord"].ToString();
        }


        if (ExecuteRecord != "")
        {
            ExecuteRecord += ",";

        }
        dr.Close();



        LastRecordID = record_id.ToString();
        ExecuteRecord += LastRecordID;

        string strUpdate = "update Tasks set ExecuteRecord='" + ExecuteRecord + "', LastRecordID='"
            + LastRecordID + "' where TaskID=" + task_id;
        SqlCommand comm1 = new SqlCommand(strUpdate, conn);
        comm1.ExecuteNonQuery();


        if (conn.State.ToString() == "Open")
        {
            conn.Close();
        }
    }

    /// <summary>
    /// 更新跟踪记录的总的次数
    /// </summary>
    /// <param name="tast_id"></param>
    public void UpdateFollowNumber(int id)
    {
        SqlConnection conn = new SqlConnection(connStr);
        int total = 0;

        if (conn.State.ToString() == "Closed")
        {
            conn.Open();
        }

        string strSql = "select count(*) as follow_number From FollowRecord where follow_client_id=" + id;
        SqlCommand comm = new SqlCommand(strSql, conn);
        SqlDataReader dr = comm.ExecuteReader();
        if (dr.Read())
        {
            //LastRecordID = dr["LastRecordID"].ToString();
            total = int.Parse(dr["follow_number"].ToString());
        }
        dr.Close();

        string strUpdate = "update FollowClient set follow_number=" + total + " where  id=" + id;
        SqlCommand comm2 = new SqlCommand(strUpdate, conn);
        comm2.ExecuteNonQuery();

        if (conn.State.ToString() == "Open")
        {
            conn.Close();
        }
    }
    //JackZhang
    //新方法更新跟踪记录的总的次数重载
    public void UpdateFollowNumber(int clientId, SqlConnection conn, SqlTransaction transaction)
    {
        string sql = "UPDATE FollowClient SET follow_number = follow_number + 1 WHERE id = @clientId";

        using (SqlCommand cmd = new SqlCommand(sql, conn, transaction))
        {
            cmd.Parameters.AddWithValue("@clientId", clientId);
            cmd.ExecuteNonQuery();
        }
    }

    /// <summary>
    /// 把记录字段的记录ID用数组提取出来
    /// </summary>
    /// <param name="tast_id"></param>
    public string[] GetRecordID(int task_id)
    {
        SqlConnection conn = new SqlConnection(connStr);
        string[] RecordIDs;
        string LastRecordID = "";
        string ExecuteRecord = "";

        if (conn.State.ToString() == "Closed")
        {
            conn.Open();
        }

        string strSql = "select ExecuteRecord from Tasks where TaskID=" + task_id;
        SqlCommand comm = new SqlCommand(strSql, conn);
        SqlDataReader dr = comm.ExecuteReader();
        if (dr.Read())
        {
            //LastRecordID = dr["LastRecordID"].ToString();
            ExecuteRecord = dr["ExecuteRecord"].ToString();
        }

        RecordIDs = ExecuteRecord.Split(',');

        dr.Close();
        if (conn.State.ToString() == "Open")
        {
            conn.Close();
        }
        return RecordIDs;

    }

    /// <summary>
    /// 更新任务记录的TaskID,使其保持与任务表相一致
    /// </summary>
    /// <param name="task_id"></param>
    public void Accord(int task_id)
    {
        SqlConnection conn = new SqlConnection(connStr);
        string[] RecordIDs;
        string LastRecordID = "";
        string ExecuteRecord = "";

        if (conn.State.ToString() == "Closed")
        {
            conn.Open();
        }

        string strSql = "select ExecuteRecord from Tasks where TaskID=" + task_id;
        SqlCommand comm = new SqlCommand(strSql, conn);
        SqlDataReader dr = comm.ExecuteReader();
        if (dr.Read())
        {
            //LastRecordID = dr["LastRecordID"].ToString();
            ExecuteRecord = dr["ExecuteRecord"].ToString();
        }
        dr.Close();
        RecordIDs = ExecuteRecord.Split(',');

        if (RecordIDs.Length > 0)
        {
            for (int i = 0; i < RecordIDs.Length; i++)
            {
                if (conn.State.ToString() == "Closed")
                {
                    conn.Open();
                }

                string updateSql = "update ExecuteRecord set TaskID=" + task_id + " where record_id=" + RecordIDs[i].ToString();
                SqlCommand comm1 = new SqlCommand(updateSql, conn);
                comm1.ExecuteNonQuery();


            }


        }
        if (conn.State.ToString() == "Open")
        {
            conn.Close();
        }


        // return RecordIDs;

    }



    /// <summary>
    /// 把任务ID转换为相应的用户名ID
    /// </summary>
    /// <param name="task_id"></param>
    /// <returns></returns>
    public string GetRemind(int task_id)
    {
        SqlConnection conn = new SqlConnection(connStr);
        //string[] RecordIDs;
        string Remind = "";
        //string ExecuteRecord = "";

        if (conn.State.ToString() == "Closed")
        {
            conn.Open();
        }

        string strSql = "select Remind from Tasks where TaskID =" + task_id;
        SqlCommand comm = new SqlCommand(strSql, conn);
        SqlDataReader dr = comm.ExecuteReader();
        if (dr.Read())
        {
            //LastRecordID = dr["LastRecordID"].ToString();
            Remind = dr["Remind"].ToString();
        }
        dr.Close();
        if (conn.State.ToString() == "Open")
        {
            conn.Close();
        }
        return Remind;

    }


    /// <summary>
    /// 把任务ID转换为相应的用户名ID
    /// </summary>
    /// <param name="task_id"></param>
    /// <returns></returns>
    public void DeleteLastExecute(int task_id)
    {
        SqlConnection conn = new SqlConnection(connStr);
        string[] RecordIDs = GetRecordID(task_id);
        string Remind = "";
        string ExecuteRecord = "";
        string LastRecordID = "";

        if (conn.State.ToString() == "Closed")
        {
            conn.Open();
        }

        string strSql = "DELETE ExecuteRecord where record_id =" + GetLastRecordID(task_id);
        SqlCommand comm = new SqlCommand(strSql, conn);
        comm.ExecuteNonQuery();

        #region  更新任务
        for (int i = 0; i < RecordIDs.Length - 1; i++)
            if (i != 0)
                ExecuteRecord += "," + RecordIDs[i];
            else
                ExecuteRecord += RecordIDs[i];

        if (RecordIDs.Length < 2)
            LastRecordID = "";
        else
            LastRecordID = RecordIDs[RecordIDs.Length - 2];

        string updateTask = "UPDATE Tasks set ExecuteRecord='" + ExecuteRecord + "',LastRecordID=" + LastRecordID + " where TaskID=" + task_id;
        SqlCommand comm1 = new SqlCommand(updateTask, conn);
        comm1.ExecuteNonQuery();

        #endregion  更新任务


        if (conn.State.ToString() == "Open")
        {
            conn.Close();
        }

    }


    /// <summary>
    ///通过任务ID把任务的最后的记录ID返回
    /// </summary>
    /// <param name="task_id"></param>
    /// <returns></returns>
    public string GetLastRecordID(int task_id)
    {
        SqlConnection conn = new SqlConnection(connStr);
        string LastRecordID = "";


        if (conn.State.ToString() == "Closed")
        {
            conn.Open();
        }

        string strSql = "select LastRecordID from Tasks where TaskID =" + task_id;
        SqlCommand comm = new SqlCommand(strSql, conn);
        SqlDataReader dr = comm.ExecuteReader();
        if (dr.Read())
        {
            //LastRecordID = dr["LastRecordID"].ToString();
            LastRecordID = dr["LastRecordID"].ToString();
        }
        dr.Close();

        if (conn.State.ToString() == "Open")
        {
            conn.Close();
        }
        if (LastRecordID == "")
            LastRecordID = "0";
        return LastRecordID.Trim();

    }


    /// <summary>
    /// 利用评价ID获取评价的内容
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    public string GetRmark(int id)
    {

        if (id == 1)
            return "优秀";
        else
            if (id == 2)
            return "良好";
        else
                if (id == 3)
            return "基本完成";
        else
                    if (id == 4)
            return "未达标";
        else
            return "严重不达标";

    }


    /// <summary>
    ///通过执行记录的ID取出要汇报的内容
    /// </summary>
    /// <param name="task_id"></param>
    /// <returns></returns>
    public string GetReport(int record_id)
    {
        SqlConnection conn = new SqlConnection(connStr);
        string ReportDescription = "";
        string TaskExamine = "";
        string actualTime = "";

        if (conn.State.ToString() == "Closed")
        {
            conn.Open();
        }

        string strSql = "select ReportDescription,ExamineDescription,ActualTime from ExecuteRecord where record_id =" + record_id;
        SqlCommand comm = new SqlCommand(strSql, conn);
        SqlDataReader dr = comm.ExecuteReader();
        if (dr.Read())
        {
            //LastRecordID = dr["LastRecordID"].ToString();
            ReportDescription = dr["ReportDescription"].ToString();
            TaskExamine = dr["ExamineDescription"].ToString();
            actualTime = dr["ActualTime"].ToString();
        }
        dr.Close();

        if (conn.State.ToString() == "Open")
        {
            conn.Close();
        }

        if (actualTime == "" || actualTime == "0")
            return ReportDescription;
        if (TaskExamine == "")
            return ReportDescription.Trim();
        else
        {
            ReportDescription = "";
            return ReportDescription;
        }

    }


    /// <summary>
    ///通过任务的ID取出该记录的汇报的内容，若前一次没有执行没有审核，返回肯定不为空。
    /// </summary>
    /// <param name="task_id"></param>
    /// <returns></returns>
    public string GetReportDescription(int task_id)
    {
        SqlConnection conn = new SqlConnection(connStr);
        string ReportDescription = "";
        string TaskExamine = "";
        string[] executeRecord = GetRecordID(task_id);//提出任务的ID

        if (executeRecord.Length < 1)
            return TaskExamine;

        else if (executeRecord.Length == 1)
        {
            return GetReport(int.Parse(executeRecord[0]));

        }
        else
        {
            if (GetReport(int.Parse(executeRecord[executeRecord.Length - 2])) != "") //表示前一次审核为空
                return GetReport(int.Parse(executeRecord[executeRecord.Length - 2]));
            else
                return GetReport(int.Parse(executeRecord[executeRecord.Length - 1]));
        }




    }


    /// <summary>
    /// 插入消息提醒，对消息的数据库进行更新
    /// </summary>
    public void InsertMessage(int executorID, DateTime dt, string message)
    {
        SqlConnection conn = new SqlConnection(connStr);


        int sortID = 0;
        string noteContent1 = message;
        int noteState = 0;
        int delayTime = 0;


        if (conn.State.ToString() == "Closed")
        {
            conn.Open();
        }



        string strAddNote1 = "INSERT INTO eventStateHanding (user_id,sort_id,sortName,bDealed,eventTime,delayTime) VALUES(";
        strAddNote1 += executorID + "," + SORT_ID + ",'" + message + "'," + noteState + ",'" + dt + "'," + delayTime + ")";


        SqlCommand comm1 = new SqlCommand(strAddNote1, conn);
        comm1.ExecuteNonQuery();



        if (conn.State.ToString() == "Open")
        {
            conn.Close();
        }



    }

    /// <summary>
    /// 更新消息提醒
    /// </summary>
    public void UpdateRemind(int id)
    {
        SqlConnection conn = new SqlConnection(connStr);

        if (conn.State.ToString() == "Closed")
        {
            conn.Open();
        }

        string strSql = "update Tasks set Remind='' where  TaskID=" + id;
        SqlCommand comm = new SqlCommand(strSql, conn);
        comm.ExecuteNonQuery();


        if (conn.State.ToString() == "Open")
        {
            conn.Close();
        }



    }

    /// <summary>
    /// 显示该任务所有的汇报内容
    /// </summary>
    public string ShowReport(int id)
    {

        string report = "";
        string strExecuteRecord = "";
        int count = 0;

        SqlConnection conn = new SqlConnection(connStr);

        if (conn.State.ToString() == "Closed")
        {
            conn.Open();
        }

        string strSql = "SELECT ExecuteRecord FROM Tasks where  TaskID=" + id;
        SqlCommand comm = new SqlCommand(strSql, conn);
        SqlDataReader dr = comm.ExecuteReader();
        if (dr.Read())
        {
            strExecuteRecord = dr["ExecuteRecord"].ToString();
        }
        dr.Close();

        string[] executeRecord = strExecuteRecord.Split(',');
        string[] str = new string[executeRecord.Length];

        for (int i = 0; i < executeRecord.Length; i++)
        {
            string reportTime = "";
            string strSql1 = "SELECT ReportDescription,RecordReportTime FROM ExecuteRecord  where  record_id=" + executeRecord[i];
            SqlCommand comm1 = new SqlCommand(strSql1, conn);
            SqlDataReader dr1 = comm1.ExecuteReader();
            if (dr1.Read())
            {
                if (dr1["ReportDescription"] != null)
                    str[i] = dr1["ReportDescription"].ToString();
                reportTime = dr1["RecordReportTime"].ToString();
            }
            dr1.Close();

            report += "第" + (i + 1) + "次执行：";
            //if ()
            //{
            //    report += "空";
            //    //report += "<br>汇报的时间：" + reportTime;
            //}
            //else
            if (str[i] == null || str[i] == "")
                report += "还没有汇报";
            else
            {
                report += str[i];
                report += "<br>汇报的时间：" + reportTime;
            }
            report += "<br>";

        }

        return report;

        //if (conn.State.ToString() == "Open")
        //{
        //    conn.Close();
        //}



    }

    public string MinuteToHour(string minitue)
    {
        string time = "";
        if (minitue == "")
            time = "0分钟";
        else
        {
            int min = int.Parse(minitue);

            if (min < 60)
                time += min + "分钟";
            if (min % 60 == 0 && min / 60 >= 1)
            {
                time += min / 60 + "小时";

            }
            if (min % 60 != 0 && min / 60 >= 1)
            {
                time += min / 60 + "小时  " + min % 60 + "分钟";

            }
        }

        return time;


    }

    public static void OpenConnection(SqlConnection conn)
    {
        if (conn.State == ConnectionState.Closed)
        {
            conn.Open();
        }
    }

    public static void CloseConnection(SqlConnection conn)
    {
        if (conn.State == ConnectionState.Open)
        {
            conn.Close();
        }
    }

    public static string GetVerifyCode()
    {
        Random random = new Random();
        return random.Next(100000, 999999).ToString();
    }

    // 执行查询，返回 DataTable
    public static DataTable ExecuteQuery(string query, SqlParameter[] parameters = null)
    {
        DataTable dt = new DataTable();

        using (SqlConnection conn = new SqlConnection(InputParams.connectionStr))
        {
            try
            {
                conn.Open();

                using (SqlCommand cmd = new SqlCommand(query, conn))
                {
                    // 如果有参数，添加参数到命令中
                    if (parameters != null)
                    {
                        cmd.Parameters.AddRange(parameters);
                    }

                    using (SqlDataAdapter adapter = new SqlDataAdapter(cmd))
                    {
                        adapter.Fill(dt);
                    }
                }
            }
            catch (Exception ex)
            {
                // 在生产环境中，不建议打印错误消息，应该记录日志
                throw new Exception("数据库查询失败", ex);
            }
        }

        return dt;
    }

    // 执行查询，返回单个值
    public static object ExecuteScalar(string query, SqlParameter[] parameters = null)
    {
        object result = null;

        using (SqlConnection conn = new SqlConnection(InputParams.connectionStr))
        {
            try
            {
                conn.Open();

                using (SqlCommand cmd = new SqlCommand(query, conn))
                {
                    // 如果有参数，添加参数到命令中
                    if (parameters != null)
                    {
                        cmd.Parameters.AddRange(parameters);
                    }

                    result = cmd.ExecuteScalar();  // 执行并返回单个值
                }
            }
            catch (Exception ex)
            {
                throw new Exception("数据库查询失败", ex);
            }
        }

        return result;
    }

    // 执行插入、更新、删除等操作
    public static int ExecuteNonQuery(string query, SqlParameter[] parameters = null)
    {
        int affectedRows = 0;

        using (SqlConnection conn = new SqlConnection(InputParams.connectionStr))
        {
            try
            {
                conn.Open();

                using (SqlCommand cmd = new SqlCommand(query, conn))
                {
                    // 如果有参数，添加参数到命令中
                    if (parameters != null)
                    {
                        cmd.Parameters.AddRange(parameters);
                    }

                    affectedRows = cmd.ExecuteNonQuery();  // 执行并返回受影响的行数
                }
            }
            catch (Exception ex)
            {
                throw new Exception("数据库操作失败", ex);
            }
        }

        return affectedRows;
    }

    public static string ToString(object o)
    {
        return o == null ? "" : o.ToString();
    }

    /// <summary>
    /// 主键查询
    /// </summary>
    public static List<Dictionary<string, object>> GetDataByPK(string tableName, string pkId = null, int pageNumber = 1, int pageSize = 10)
    {
        string query = $"SELECT * FROM {tableName}";
        if (!string.IsNullOrEmpty(pkId))
        {
            query += $" WHERE Id ={pkId} ";
        }

        // 添加分页查询逻辑
        //query += " OFFSET @Offset ROWS FETCH NEXT @PageSize ROWS ONLY";

        // 创建一个列表来存储查询结果
        var results = new List<Dictionary<string, object>>();

        using (SqlConnection conn = new SqlConnection(connStr))
        {
            conn.Open();
            using (SqlCommand command = new SqlCommand(query, conn))
            {
                using (SqlDataReader reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        var row = new Dictionary<string, object>();
                        for (int i = 0; i < reader.FieldCount; i++)
                        {
                            row.Add(reader.GetName(i), reader.GetValue(i));
                        }
                        results.Add(row);
                    }
                }
            }
        }

        return results;
    }

    /// <summary>
    ///  条件查询
    /// </summary>
    public static List<Dictionary<string, object>> GetDataByConditions(string tableName, Dictionary<string, object> conditions = null, int pageNumber = 1, int pageSize = 10)
    {
        StringBuilder query = new StringBuilder($"SELECT * FROM {tableName}");

        // 如果有查询条件，动态构建 WHERE 子句
        if (conditions != null && conditions.Count > 0)
        {
            query.Append(" WHERE ");
            bool firstCondition = true;
            foreach (var condition in conditions)
            {
                if (!firstCondition)
                {
                    query.Append(" AND ");
                }
                query.Append($"{condition.Key} = @{condition.Key}");
                firstCondition = false;
            }
        }

        var results = new List<Dictionary<string, object>>();

        using (SqlConnection connection = new SqlConnection(connStr))
        {
            try
            {
                connection.Open();
                using (SqlCommand command = new SqlCommand(query.ToString(), connection))
                {
                    // 动态添加查询条件的参数
                    if (conditions != null)
                    {
                        foreach (var condition in conditions)
                        {
                            command.Parameters.AddWithValue($"@{condition.Key}", condition.Value);
                        }
                    }

                    // 执行查询并读取数据
                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        // 遍历结果集并填充字典
                        while (reader.Read())
                        {
                            var row = new Dictionary<string, object>();
                            for (int i = 0; i < reader.FieldCount; i++)
                            {
                                row.Add(reader.GetName(i), reader.GetValue(i));
                            }
                            results.Add(row);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Log($"GetByConditions Error: {ex.Message}");
            }
        }

        return results;
    }

    public static string alertMsg(string msg)
    {
        return string.Format("<script type=\"text/javascript\">alert('{0}')</script>", msg);
    }
    public static void ShowAlert(Page page, string message)
    {
        string script = $"alert('{message}');";
        page.ClientScript.RegisterStartupScript(page.GetType(), "alert", script, true);
    }
}

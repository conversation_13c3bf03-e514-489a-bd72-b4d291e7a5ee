﻿var WBTB = new Object();
WBTB.yToolbars = new Array();
WBTB.YInitialized = false;
WBTB.filterScript = false;
WBTB.Charset = "UTF-8";
WBTB.ShowGridlines = false;
WBTB.XhtmlEnabled = false;
WBTB.pasteTextOnly = false;
WBTB.fileEdit = false;
WBTB.tempEdit = false;
WBTB.beforHtml = "";
WBTB.startHtml = false;
navVersion = navigator.appVersion.toLowerCase();
WBTB.bIsIE5 = (navVersion.indexOf("msie 5.0")!=-1) || (navVersion.indexOf("msie 5.5")!=-1) || (navVersion.indexOf("msie 6.0")!=-1) || (navVersion.indexOf("msie 7.0")!=-1);
WBTB.edit;
WBTB.RangeType;	//selectRang
WBTB.selection;
WBTB.Frame;
WBTB.HtmlArea;
WBTB.Hidden;

//-------------------
var WBTB_global = window.document;
WBTB_global.fo_currentMenu = null;
WBTB_global.fo_shadows = new Array;

var WBTB_menu_table="<table width='100%' cellspacing='0' cellpadding='2'>";
WBTB_menu_table+="<tr onmouseout='scolor(this)' onmouseover='rcolor(this)'><td style='cursor:default'><img src='wbTextBox/images/table_cr.gif' width='16' height='16' align='absmiddle' ></td><a href='javascript:WBTB_insertTable()'><td>插入表格</td></a></tr>";
WBTB_menu_table+="<tr onmouseout='scolor(this)' onmouseover='rcolor(this)'><td style='cursor:default'><img src='wbTextBox/images/table_sx.gif' width='16' height='16' align='absmiddle'></td><a href='javascript:WBTB_tableProp()'><td>表格属性</td></a></tr>";
WBTB_menu_table+="<tr onmouseout='scolor(this)' onmouseover='rcolor(this)'><td style='cursor:default'><img src='wbTextBox/images/table_sx2.gif' width='16' height='16' align='absmiddle'></td><a href='javascript:WBTB_cellProp()'><td>单元格属性</td></a></tr>";
WBTB_menu_table+="<tr onmouseout='scolor(this)' onmouseover='rcolor(this)'><td style='cursor:default'><img src='wbTextBox/images/table_tr.gif' width='16' height='16' align='absmiddle'></td><a href='javascript:WBTB_tablecommand(1)'><td>插入一行</td></a></tr>";
WBTB_menu_table+="<tr onmouseout='scolor(this)' onmouseover='rcolor(this)'><td style='cursor:default'><img src='wbTextBox/images/table_trdel.gif' width='16' height='16' align='absmiddle'></td><a href='javascript:WBTB_tablecommand(2)'><td>删除一行</td></a></tr>";
WBTB_menu_table+="<tr onmouseout='scolor(this)' onmouseover='rcolor(this)'><td style='cursor:default'><img src='wbTextBox/images/table_td.gif' width='16' height='16' align='absmiddle'></td><a href='javascript:WBTB_tablecommand(3)'><td>插入一列</td></a></tr>";
WBTB_menu_table+="<tr onmouseout='scolor(this)' onmouseover='rcolor(this)'><td style='cursor:default'><img src='wbTextBox/images/table_tddel.gif' width='16' height='16' align='absmiddle'></td><a href='javascript:WBTB_tablecommand(4)'><td>删除一列</td></a></tr>";
WBTB_menu_table+="<tr onmouseout='scolor(this)' onmouseover='rcolor(this)'><td style='cursor:default'><img src='wbTextBox/images/table_hby.gif' width='16' height='16' align='absmiddle'></td><a href='javascript:WBTB_tablecommand(5)'><td>向右合并</td></a></tr>";
WBTB_menu_table+="<tr onmouseout='scolor(this)' onmouseover='rcolor(this)'><td style='cursor:default'><img src='wbTextBox/images/table_hbx.gif' width='16' height='16' align='absmiddle'></td><a href='javascript:WBTB_tablecommand(6)'><td>向下合并</td></a></tr>";
WBTB_menu_table+="<tr onmouseout='scolor(this)' onmouseover='rcolor(this)'><td style='cursor:default'><img src='wbTextBox/images/table_cf.gif' width='16' height='16' align='absmiddle'></td><a href='javascript:WBTB_tablecommand(7)'><td>拆分单元格</td></a></tr>";
WBTB_menu_table+="</table>";
var WBTB_menu_para="<table width='100%' cellspacing='0' cellpadding='2' >";
//WBTB_menu_para+="<tr onmouseout='scolor(this)' onmouseover='rcolor(this)'><a href='javascript:WBTB_format(\"FormatBlock\",\"<p>\")'><td align=center><font size=3>正文(P)</font></td></a></tr>";
WBTB_menu_para+="<tr onmouseout='scolor(this)' onmouseover='rcolor(this)'><a href='javascript:WBTB_format(\"FormatBlock\",\"<p>\")'><td align=center><font size=3>正文(P)</font></td></a></tr>";

WBTB_menu_para+="<tr onmouseout='scolor(this)' onmouseover='rcolor(this)'><a href='javascript:WBTB_format(\"FormatBlock\",\"<h1>\")'><td align=center><font size=6><b>标题1(H1)</b></font></td></a></tr>";
WBTB_menu_para+="<tr onmouseout='scolor(this)' onmouseover='rcolor(this)'><a href='javascript:WBTB_format(\"FormatBlock\",\"<h2>\")'><td align=center><font size=5><b>标题2(H2)</b></font></td></a></tr>";
WBTB_menu_para+="<tr onmouseout='scolor(this)' onmouseover='rcolor(this)'><a href='javascript:WBTB_format(\"FormatBlock\",\"<h3>\")'><td align=center><font size=4><b>标题3(h3)</b></font></td></a></tr>";
WBTB_menu_para+="<tr onmouseout='scolor(this)' onmouseover='rcolor(this)'><a href='javascript:WBTB_format(\"FormatBlock\",\"<h4>\")'><td align=center><font size=3><b>标题4(h4)</b></font></td></a></tr>";
WBTB_menu_para+="<tr onmouseout='scolor(this)' onmouseover='rcolor(this)'><a href='javascript:WBTB_format(\"FormatBlock\",\"<h5>\")'><td align=center><font size=2><b>标题5(h5)</b></font></td></a></tr>";
WBTB_menu_para+="<tr onmouseout='scolor(this)' onmouseover='rcolor(this)'><a href='javascript:WBTB_format(\"FormatBlock\",\"<h6>\")'><td align=center><font size=1><b>标题6(h6)</b></font></td></a></tr>";
WBTB_menu_para+="<tr onmouseout='scolor(this)' onmouseover='rcolor(this)'><a href='javascript:WBTB_format(\"FormatBlock\",\"<pre>\")'><td align=center><font size=2>预设格式(pre)</font></td></a></tr>";
WBTB_menu_para+="</table>";
var WBTB_menu_font="<table width='100%' cellspacing='0' cellpadding='2' >";
WBTB_menu_font+="<tr><a href='javascript:WBTB_format(\"fontname\",\"宋体\")'><td align=center onmouseout='scolor(this)' onmouseover='rcolor(this)'><font face=宋体 size=3>宋体</font></td></a>";
WBTB_menu_font+="<a href='javascript:WBTB_format(\"fontname\",\"黑体\")'><td align=center onmouseout='scolor(this)' onmouseover='rcolor(this)'><font face=黑体 size=3>黑体</font></td></a></tr>";
WBTB_menu_font+="<tr><a href='javascript:WBTB_format(\"fontname\",\"楷体_GB2312\")'><td align=center onmouseout='scolor(this)' onmouseover='rcolor(this)'><font face=楷体_GB2312 size=3>楷体</font></td></a>";
WBTB_menu_font+="<a href='javascript:WBTB_format(\"fontname\",\"仿宋_GB2312\")'><td align=center onmouseout='scolor(this)' onmouseover='rcolor(this)'><font face=仿宋_GB2312 size=3>仿宋</font></td></a></tr>";
WBTB_menu_font+="<tr><a href='javascript:WBTB_format(\"fontname\",\"隶书\")'><td align=center onmouseout='scolor(this)' onmouseover='rcolor(this)'><font face=隶书 size=3>隶书</font></td></a>";
WBTB_menu_font+="<a href='javascript:WBTB_format(\"fontname\",\"幼圆\")'><td align=center onmouseout='scolor(this)' onmouseover='rcolor(this)'><font face=幼圆 size=3>幼圆</font></td></a></tr>";
WBTB_menu_font+="<tr><a href='javascript:WBTB_format(\"fontname\",\"Arial\")'><td align=center onmouseout='scolor(this)' onmouseover='rcolor(this)'><font face=Arial size=3>Arial</font></td></a>";
WBTB_menu_font+="<a href='javascript:WBTB_format(\"fontname\",\"Arial Black\")'><td align=center onmouseout='scolor(this)' onmouseover='rcolor(this)'><font face='Arial Black' size=3>Arial Black</font></td></a></tr>";
WBTB_menu_font+="<tr><a href='javascript:WBTB_format(\"fontname\",\"Comic Sans MS\")'><td align=center onmouseout='scolor(this)' onmouseover='rcolor(this)'><font face='Comic Sans MS' size=3>Comic Sans MS</font></td></a>";
WBTB_menu_font+="<a href='javascript:WBTB_format(\"fontname\",\"Courier\")'><td align=center onmouseout='scolor(this)' onmouseover='rcolor(this)'><font face='Courier' size=3>Courier</font></td></a></tr>";
WBTB_menu_font+="<tr><a href='javascript:WBTB_format(\"fontname\",\"Courier New\")'><td align=center onmouseout='scolor(this)' onmouseover='rcolor(this)'><font face='Courier New' size=3>Courier New</font></td></a>";
WBTB_menu_font+="<a href='javascript:WBTB_format(\"fontname\",\"MS Sans Serif\")'><td align=center onmouseout='scolor(this)' onmouseover='rcolor(this)'><font face='MS Sans Serif' size=3>MS Sans Serif</font></td></a></tr>";
WBTB_menu_font+="<tr><a href='javascript:WBTB_format(\"fontname\",\"Times New Roman\")'><td align=center onmouseout='scolor(this)' onmouseover='rcolor(this)'><font face='Times New Roman' size=3>Times New Roman</font></td></a>";
WBTB_menu_font+="<a href='javascript:WBTB_format(\"fontname\",\"Verdana\")'><td align=center onmouseout='scolor(this)' onmouseover='rcolor(this)'><font face=Verdana size=3>Verdana</font></td></a></tr>";
WBTB_menu_font+="<tr><a href='javascript:WBTB_format(\"fontname\",\"Webdings\")'><td colspan=2 align=center onmouseout='scolor(this)' onmouseover='rcolor(this)'><font face='Webdings' size=3>Webdings</font>(Webdings)</td></a></tr>";
WBTB_menu_font+="<tr><a href='javascript:WBTB_format(\"fontname\",\"Wingdings\")'><td colspan=2 align=center onmouseout='scolor(this)' onmouseover='rcolor(this)'><font face=Wingdings size=3>Wingdings</font>(Wingdings)</td></a></tr>";
WBTB_menu_font+="</table>";
var WBTB_menu_size="<table width='100%' cellspacing='0' cellpadding='2' >";
WBTB_menu_size+="<tr onmouseout='scolor(this)' onmouseover='rcolor(this)'><a href='javascript:WBTB_format(\"fontsize\",\"1\")'><td align=center><font size=1>AaBb...(1)</font></td></a></tr>";
WBTB_menu_size+="<tr onmouseout='scolor(this)' onmouseover='rcolor(this)'><a href='javascript:WBTB_format(\"fontsize\",\"2\")'><td align=center><font size=2>AaBb...(2)</font></td></a></tr>";
WBTB_menu_size+="<tr onmouseout='scolor(this)' onmouseover='rcolor(this)'><a href='javascript:WBTB_format(\"fontsize\",\"3\")'><td align=center><font size=3>AaBb...(3)</font></td></a></tr>";
WBTB_menu_size+="<tr onmouseout='scolor(this)' onmouseover='rcolor(this)'><a href='javascript:WBTB_format(\"fontsize\",\"4\")'><td align=center><font size=4>AaBb...(4)</font></td></a></tr>";
WBTB_menu_size+="<tr onmouseout='scolor(this)' onmouseover='rcolor(this)'><a href='javascript:WBTB_format(\"fontsize\",\"5\")'><td align=center><font size=5>AaBb...(5)</font></td></a></tr>";
WBTB_menu_size+="<tr onmouseout='scolor(this)' onmouseover='rcolor(this)'><a href='javascript:WBTB_format(\"fontsize\",\"6\")'><td align=center><font size=6>AaBb...(6)</font></td></a></tr>";
WBTB_menu_size+="<tr onmouseout='scolor(this)' onmouseover='rcolor(this)'><a href='javascript:WBTB_format(\"fontsize\",\"7\")'><td align=center><font size=7>AaBb...(7)</font></td></a></tr>";
WBTB_menu_size+="</table>";
var WBTB_menu_symbols="<table width='100%' cellspacing='0' cellpadding='2' >";
WBTB_menu_symbols+="<tr><a href='javascript:WBTB_InsertHtml(\"&#8230;\")'><td width=40 align=center onmouseout='scolor(this)' onmouseover='rcolor(this)'><font size=1 face=arial>&#8230;</font></td></a>";
WBTB_menu_symbols+="<a href='javascript:WBTB_InsertHtml(\"&#163;\")'><td width=40 align=center onmouseout='scolor(this)' onmouseover='rcolor(this)'><font size=3>&#163;</font></td></a>";
WBTB_menu_symbols+="<a href='javascript:WBTB_InsertHtml(\"&#165;\")'><td width=40 align=center onmouseout='scolor(this)' onmouseover='rcolor(this)'><font size=3>&#165;</font></td></a></tr>";
WBTB_menu_symbols+="<tr><a href='javascript:WBTB_InsertHtml(\"&#166;\")'><td align=center onmouseout='scolor(this)' onmouseover='rcolor(this)'><font size=3>&#166;</font></td></a>";
WBTB_menu_symbols+="<a href='javascript:WBTB_InsertHtml(\"&#169;\")'><td align=center onmouseout='scolor(this)' onmouseover='rcolor(this)'><font size=3>&#169;</font></td></a>";
WBTB_menu_symbols+="<a href='javascript:WBTB_InsertHtml(\"&#174;\")'><td align=center onmouseout='scolor(this)' onmouseover='rcolor(this)'><font size=3>&#174;</font></td></a></tr>";
WBTB_menu_symbols+="<tr><a href='javascript:WBTB_InsertHtml(\"&#176;\")'><td align=center onmouseout='scolor(this)' onmouseover='rcolor(this)'><font size=3>&#176;</font></td></a>";
WBTB_menu_symbols+="<a href='javascript:WBTB_InsertHtml(\"&#177;\")'><td align=center onmouseout='scolor(this)' onmouseover='rcolor(this)'><font size=3>&#177;</font></td></a>";
WBTB_menu_symbols+="<a href='javascript:WBTB_InsertHtml(\"&#177;\")'><td align=center onmouseout='scolor(this)' onmouseover='rcolor(this)'><font size=3>&#177;</font></td></a></tr>";
WBTB_menu_symbols+="<tr><a href='javascript:WBTB_InsertHtml(\"&#183;\")'><td align=center onmouseout='scolor(this)' onmouseover='rcolor(this)'><font size=3>&#183;</font></td></a>";
WBTB_menu_symbols+="<a href='javascript:WBTB_InsertHtml(\"&#171;\")'><td align=center onmouseout='scolor(this)' onmouseover='rcolor(this)'><font size=3>&#171;</font></td></a>";
WBTB_menu_symbols+="<a href='javascript:WBTB_InsertHtml(\"&#187;\")'><td align=center onmouseout='scolor(this)' onmouseover='rcolor(this)'><font size=3>&#187;</font></td></a></tr>";
WBTB_menu_symbols+="<tr><a href='javascript:WBTB_InsertHtml(\"&#188;\")'><td align=center onmouseout='scolor(this)' onmouseover='rcolor(this)'><font size=3>&#188;</font></td></a>";
WBTB_menu_symbols+="<a href='javascript:WBTB_InsertHtml(\"&#189;\")'><td align=center onmouseout='scolor(this)' onmouseover='rcolor(this)'><font size=3>&#189;</font></td></a>";
WBTB_menu_symbols+="<a href='javascript:WBTB_InsertHtml(\"&#190;\")'><td align=center onmouseout='scolor(this)' onmouseover='rcolor(this)'><font size=3>&#190;</font></td></a></tr>";
WBTB_menu_symbols+="<tr><a href='javascript:WBTB_InsertHtml(\"&#247;\")'><td align=center onmouseout='scolor(this)' onmouseover='rcolor(this)'><font size=3>&#247;</font></td></a>";
WBTB_menu_symbols+="<a href='javascript:WBTB_InsertHtml(\"&#8224;\")'><td align=center onmouseout='scolor(this)' onmouseover='rcolor(this)'><font size=3>&#8224;</font></td></a>";
WBTB_menu_symbols+="<a href='javascript:WBTB_InsertHtml(\"&#8225;\")'><td align=center onmouseout='scolor(this)' onmouseover='rcolor(this)'><font size=3>&#8225;</font></td></a></tr>";
WBTB_menu_symbols+="<tr><a href='javascript:WBTB_InsertHtml(\"&#8364;\")'><td align=center onmouseout='scolor(this)' onmouseover='rcolor(this)'><font size=3>&#8364;</font></td></a>";
WBTB_menu_symbols+="<a href='javascript:WBTB_InsertHtml(\"&#8482;\")'><td align=center onmouseout='scolor(this)' onmouseover='rcolor(this)'><font size=3>&#8482;</font></td></a>";
WBTB_menu_symbols+="<a href='javascript:WBTB_InsertHtml(\"&#8226;\")'><td align=center onmouseout='scolor(this)' onmouseover='rcolor(this)'><font size=3>&#8226;</font></td></a></tr>";
WBTB_menu_symbols+="<tr><a href='javascript:WBTB_InsertHtml(\"&#8211;\")'><td align=center onmouseout='scolor(this)' onmouseover='rcolor(this)'><font size=3>&#8211;</font></td></a>";
WBTB_menu_symbols+="<a href='javascript:WBTB_InsertHtml(\"&#162;\")'><td align=center onmouseout='scolor(this)' onmouseover='rcolor(this)'><font size=3>&#162;</font></td></a>";
WBTB_menu_symbols+="<td></td></tr>";
WBTB_menu_symbols+="</table>";
var MENU_SHADOW_COLOR="aaaaaa";//定义下拉菜单阴影色

function HideMenu() 
{
	var mX;
	var mY;
	var vDiv;
	var mDiv;
	if (isvisible == true)
	{
		vDiv = document.getElementById("menuDiv");
		mX = window.event.clientX + document.body.scrollLeft;
		mY = window.event.clientY + document.body.scrollTop;
		if ((mX-5 < parseInt(vDiv.style.left)) || (mX > parseInt(vDiv.style.left)+vDiv.offsetWidth) || (mY-20 < parseInt(vDiv.style.top)-h) || (mY > parseInt(vDiv.style.top)+vDiv.offsetHeight))
		{
			vDiv.style.visibility = "hidden";
			isvisible = false;
		}
	}
}

function ShowMenu(vMnuCode,tWidth) {
	var space = 1;
	var topMar = 1;
	var leftMar = -2;
	vSrc = window.event.srcElement;
	vMnuCode = "<table id='submenu' cellspacing=1 cellpadding=3 style='width:"+tWidth+"' class=WBTB_menu onmouseout='HideMenu()'><tr height=23><td nowrap align=left class=MenuBody>" + vMnuCode + "</td></tr></table>";

	h = vSrc.offsetHeight;
	w = vSrc.offsetWidth;
	l = vSrc.offsetLeft + leftMar+4;
	t = vSrc.offsetTop + topMar + h + space-2;
	vParent = vSrc.offsetParent;
	while (vParent.tagName.toUpperCase() != "BODY")
	{
		l += vParent.offsetLeft;
		t += vParent.offsetTop;
		vParent = vParent.offsetParent;
	}

	menuDiv.innerHTML = vMnuCode;
	menuDiv.style.top = t;
	menuDiv.style.left = l;
	menuDiv.style.visibility = "visible";
	isvisible = true;
	makeRectangularDropShadow(submenu, MENU_SHADOW_COLOR, 4)
}

function makeRectangularDropShadow(el, color, size)
{
	if (!WBTB.bIsIE5){
		return;
	}
	var i;
	for (i=size; i>0; i--)
	{
		var rect = document.createElement('div');
		var rs = rect.style
		rs.position = 'absolute';
		rs.left = (el.style.posLeft + i) + 'px';
		rs.top = (el.style.posTop + i) + 'px';
		rs.width = el.offsetWidth + 'px';
		rs.height = el.offsetHeight + 'px';
		rs.zIndex = el.style.zIndex - i;
		rs.backgroundColor = color;
		var opacity = 1 - i / (i + 1);
		rs.filter = 'alpha(opacity=' + (100 * opacity) + ')';
		el.insertAdjacentElement('afterEnd', rect);
		WBTB_global.fo_shadows[WBTB_global.fo_shadows.length] = rect;
	}
}
function scolor(obj)
{
	obj.style.backgroundColor="";
}
function rcolor(obj)
{
	obj.style.backgroundColor="#E1F4EE";
	obj.style.cursor = "hand";
}
function WBTB_BtnMenuMouseOver()
{
	/*
	if (event.srcElement.tagName != "IMG") return false;
	var image = event.srcElement;
	var element = image.parentElement;
	
	if (image.className == "WBTB_Ico") element.className = "WBTB_BtnMenuMouseOverUp";
	else if (image.className == "WBTB_IcoDown") element.className = "WBTB_BtnMenuMouseOverDown";
	event.cancelBubble = true;
	*/
	if (this.className.indexOf("WBTB_BtnMenu")==0){
		this.className = "WBTB_BtnMenuMouseOverUp";
	}
}

function WBTB_BtnMenuMouseOut()
{
	/*
	if (event.srcElement.tagName != "IMG") {
	event.cancelBubble = true;
	return false;
	}
	
	var image = event.srcElement;
	var element = image.parentElement;
	yRaisedElement = null;
	
	element.className = "WBTB_BtnMenu";
	image.className = "WBTB_Ico";
	
	event.cancelBubble = true;
	*/
	if (this.className.indexOf("WBTB_BtnMenu")==0){
		this.className = "WBTB_BtnMenu";
	}
}

function WBTB_BtnMenuMouseDown()
{
	/*
	if (event.srcElement.tagName != "IMG") {
	event.cancelBubble = true;
	event.returnValue=false;
	return false;
	}
	
	var image = event.srcElement;
	var element = image.parentElement;
	
	element.className = "WBTB_BtnMenuMouseOverDown";
	image.className = "WBTB_IcoDown";
	
	event.cancelBubble = true;
	event.returnValue=false;
	return false;
	*/
	if (this.className.indexOf("WBTB_BtnMenu")==0){
		this.className = "WBTB_BtnMenuMouseOverDown";
	}
}

function WBTB_BtnMenuMouseUp()
{
	/*
	if (event.srcElement.tagName != "IMG") {
	event.cancelBubble = true;
	return false;
	}
	
	var image = event.srcElement;
	var element =	image.parentElement;
	
	if (element.YUSERONCLICK) eval(element.YUSERONCLICK + "anonymous()");
	
	element.className = "WBTB_BtnMenuMouseOverUp";
	image.className = "WBTB_Ico";
	
	event.cancelBubble = true;
	return false;
	*/
	if (this.className.indexOf("WBTB_BtnMenu")==0){
		this.className = "WBTB_BtnMenuMouseOverUp";
	}

}

function WBTB_InitBtnMenu(BtnMenu)
{
	BtnMenu.onmouseover = WBTB_BtnMenuMouseOver;
	BtnMenu.onmouseout = WBTB_BtnMenuMouseOut;
	BtnMenu.onmousedown = WBTB_BtnMenuMouseDown;
	BtnMenu.onmouseup = WBTB_BtnMenuMouseUp;
	BtnMenu.ondragstart = WBTB_YCancelEvent;
	BtnMenu.onselectstart = WBTB_YCancelEvent;
	BtnMenu.onselect = WBTB_YCancelEvent;
//	BtnMenu.YUSERONCLICK = BtnMenu.onclick;
//	BtnMenu.onclick =WBTB_YCancelEvent;
//	BtnMenu.YINITIALIZED = true;
	return true;
}


// 从Word中粘贴，去除格式
function WBTB_pasteWord(){
	if (!WBTB_validateMode()) return;
	WBTB.Frame.focus();
	WBTB_InsertHtml( WBTB_getClipboardHTML() ) ;
	WBTB_CleanCode();
	WBTB.Frame.focus();
}


// 粘贴纯文本
function WBTB_pasteText(){
	if (!WBTB_validateMode()) return;
	WBTB.Frame.focus();
	if (window.clipboardData){
		var sText =  WBTB_HTMLEncode(window.clipboardData.getData("Text"))  ;
		WBTB_InsertHtml(sText);
		WBTB.Frame.focus();
	}else{
		alert("浏览器不支持粘贴");
	}
}


// 替换特殊字符
function WBTB_HTMLEncode(text){
	text = text.replace(/&/g, "&amp;") ;
	text = text.replace(/"/g, "&quot;") ;
	text = text.replace(/</g, "&lt;") ;
	text = text.replace(/>/g, "&gt;") ;
	text = text.replace(/'/g, "&#146;") ;
	text = text.replace(/\ /g,"&nbsp;");
	text = text.replace(/\n/g,"<br/>");
	text = text.replace(/\t/g,"&nbsp;&nbsp;&nbsp;&nbsp;");
	return text;
}

//取clipboard内容
//document.write("<div id='WBTB_divTemp'></div>");
function WBTB_getClipboardHTML() {
	var oDiv = document.getElementById("WBTB_divTemp")
	oDiv.innerHTML = "" ;
	if (WBTB.bIsIE5){
		var oTextRange = document.body.createTextRange();
		oTextRange.moveToElementText(oDiv) ;
	}else{
		var oTextRange = document.createRange();
		oTextRange.selectNodeContents(oDiv) ;
	}
	try{
		oTextRange.execCommand("Paste") ;
	}catch(e){
		alert("浏览器不支持粘贴");
		return "";
	}
	
	var sData = oDiv.innerHTML ;
	oDiv.innerHTML = "" ;
	
	return sData ;
}


//----------------------
function SetTab()
{
	if (WBTB.YInitialized) return;
	WBTB.YInitialized = true;
	
	var i, s, curr;
	var con = document.getElementById("WBTB_Container");
	for (i=0; i<con.getElementsByTagName("td").length; i++)
	{
		curr=con.getElementsByTagName("td")[i];
	//	if (curr.className == "yToolbar")
		if (curr.className == "WBTB_Btn"){
		//	alert(curr +" td");
			//	WBTB_InitTB(curr);
			//	WBTB.yToolbars[WBTB.yToolbars.length] = curr;
			WBTB_InitBtn(curr);
		}else if (curr.className=="WBTB_BtnMenu"){
			WBTB_InitBtnMenu(curr)
		}
	}
	
}


function WBTB_InitBtn(btn)
{
	btn.onmouseover = WBTB_BtnMouseOver;
	btn.onmouseout = WBTB_BtnMouseOut;
	btn.onmousedown = WBTB_BtnMouseDown;
	btn.onmouseup	= WBTB_BtnMouseUp;
	btn.ondragstart = WBTB_YCancelEvent;
	btn.onselectstart = WBTB_YCancelEvent;
	btn.onselect = WBTB_YCancelEvent;
	//btn.YUSERONCLICK = btn.onclick;
	//btn.onclick = WBTB_YCancelEvent;
//	btn.YINITIALIZED = true;
	return true;
}

/*
function WBTB_InitTB(y)
{
	y.TBWidth = 0;
	if (!WBTB_PopulateTB(y)) return false;
	y.style.posWidth = y.TBWidth;
	return true;
}
*/

function WBTB_YCancelEvent()
{
	return false;
}

function WBTB_BtnMouseOver()
{
	if (this.className.indexOf("WBTB_Btn")==0) {
		//var image = event.srcElement;
		//var element = image.parentElement;
		
		//if (image.className == "WBTB_Ico") element.className = "WBTB_BtnMouseOverUp";
		//else if (image.className == "WBTB_IcoDown") element.className = "WBTB_BtnMouseOverDown";
		this.className = "WBTB_BtnMouseOverUp";
	}
}

function WBTB_BtnMouseOut()
{
	/*
	if (event.srcElement.tagName != "IMG") {
		event.cancelBubble = true;
		return false;
	}
	
	var image = event.srcElement;
	var element =	image.parentElement;
	yRaisedElement = null;
	
	element.className = "WBTB_Btn";
	image.className = "WBTB_Ico";
	
	event.cancelBubble = true;
	*/
	if (this.className.indexOf("WBTB_Btn")==0) {
		this.className = "WBTB_Btn";
	}

}

function WBTB_BtnMouseDown()
{
	/*
	if (event.srcElement.tagName != "IMG") {
		event.cancelBubble = true;
		event.returnValue=false;
		return false;
	}
	
	var image = event.srcElement;
	var element = image.parentElement;
	
	element.className = "WBTB_BtnMouseOverDown";
	image.className = "WBTB_IcoDown";
	
	event.cancelBubble = true;
	event.returnValue=false;
	*/
	if (this.className.indexOf("WBTB_Btn")==0) {
		this.className = "WBTB_BtnMouseOverDown";
	}
	return false;

}

function WBTB_BtnMouseUp()
{
	/*
	if (event.srcElement.tagName != "IMG") {
		event.cancelBubble = true;
		return false;
	}
	
	var image = event.srcElement;
	var element = image.parentElement;
	
	if (element.YUSERONCLICK) eval(element.YUSERONCLICK + "anonymous()");
	
	element.className = "WBTB_BtnMouseOverUp";
	image.className = "WBTB_Ico";
	
	event.cancelBubble = true;
	return false;
	*/
	if (this.className.indexOf("WBTB_Btn")==0) {
		this.className = "WBTB_BtnMouseOverUp";
	}
	return false;
}

/*
function WBTB_PopulateTB(y)
{
	var i, elements, element;
	
	elements = y.children;
	for (i=0; i<elements.length; i++) {
	element = elements[i];
	if (element.tagName== "SCRIPT" || element.tagName == "!") continue;
	
	switch (element.className) {
		case "WBTB_Btn":
			if (element.YINITIALIZED == null) {
				if (! WBTB_InitBtn(element))
					return false;
			}
			
			element.style.posLeft = y.TBWidth;
			y.TBWidt += element.offsetWidth + 1;
			break;
		
		case "WBTB_BtnMenu":
			if (element.YINITIALIZED == null) {
				if (! WBTB_InitBtnMenu(element))
					return false;
			}
			
			element.style.posLeft = y.TBWidth;
			y.TBWidth += element.offsetWidth + 1;
			break;
		
		case "WBTB_TBGen":
			element.style.posLeft = y.TBWidth;
			y.TBWidth += element.offsetWidth + 1;
			break;
		
		default:
			//  return false;
		}
	}
	
	y.TBWidth += 1;
	return true;
}

function WBTB_DebugObject(obj)
{
	var msg = "";
	for (var i in TB) {
		ans=prompt(i+"="+TB[i]+"\n");
		if (! ans) break;
	}
}
*/


function WBTB_validateMode()
{
	if (!WBTB_bTextMode) return true;
	alert("请取消“查看HTML源代码”选项再使用系统编辑功能或者提交!");
	WBTB.Frame.focus();
	return false;
}

function WBTB_format1(what,opt)
{
	if (opt=="removeFormat")
	{
		what=opt;
		opt=null;
	}
	WBTB.Frame.focus();
	if (opt==null)
	{
		WBTB.Frame.document.execCommand(what,"",null);
	}else{
		WBTB.Frame.document.execCommand(what,"",opt);
	}
	WBTB_pureText = false;
	WBTB.Frame.focus();
}

function WBTB_format(what,opt)
{
	  if (!WBTB_validateMode()) return;
	
	  WBTB_format1(what,opt);
}

// 编辑方式
function WBTB_setMode()
{
	WBTB_bTextMode=!WBTB_bTextMode;
	WBTB_setTab();
	var cont;
	if (WBTB_bTextMode) {
		document.getElementById("WBTB_Toolbars").style.display='none';
		document.getElementById("WBTB_Composition").style.display='none';
		WBTB.HtmlArea.style.display='';
		WBTB.HtmlArea.style.height = WBTB.HtmlArea.parentNode.style.height;
		WBTB_cleanHtml();
		if (WBTB.fileEdit){
			cont=WBTB.beforHtml + WBTB.Frame.document.body.parentElement.outerHTML;
		}else{
			cont=WBTB.Frame.document.body.innerHTML;
		}
		cont=WBTB_correctUrl(cont);
		if (WBTB.XhtmlEnabled) {
			try{
				cont=WBTBXHTML.GetXHTML(WBTB.Frame.document.body)
			}catch(e){
			}
		}
		if (WBTB.filterScript) {
			cont=WBTB_FilterScript(cont);
		}
		//WBTB.Frame.document.body.innerText=cont;
		if (WBTB.tempEdit){
			WBTB.HtmlArea.value = WBTB_BeforTempSave(cont);
		}else{
			WBTB.HtmlArea.value = cont;
		}
		WBTB.HtmlArea.focus();
	} else {
		document.getElementById("WBTB_Toolbars").style.display='';
		document.getElementById("WBTB_Composition").style.display='';
		WBTB.HtmlArea.style.display='none';
		//cont=WBTB.Frame.document.body.innerText;
		cont=WBTB.HtmlArea.value;
		cont=WBTB_correctUrl(cont);
		if (WBTB.filterScript) {
			cont=WBTB_FilterScript(cont);
		}
		//WBTB.Frame.document.body.innerHTML=cont;
		document.getElementById(WBTB.Hidden).value=cont;
		WBTB_InitDocument(WBTB.Hidden,WBTB.Charset);
	}
	//WBTB_setStyle();
	return true;
}

function WBTB_setStyle()
{
	bs = WBTB.Frame.document.body.runtimeStyle;
	if(bs){
		//根据mode设置iframe样式表	
		if (WBTB_bTextMode) {
			bs.fontFamily="宋体,Arial";
			bs.fontSize="10pt";
		}else{
			bs.fontFamily="宋体,Arial";
			bs.fontSize="10.5pt";
		}
		bs.scrollbar3dLightColor= '#D4D0C8';
		bs.scrollbarArrowColor= '#000000';
		bs.scrollbarBaseColor= '#D4D0C8';
		bs.scrollbarDarkShadowColor= '#D4D0C8';
		bs.scrollbarFaceColor= '#D4D0C8';
		bs.scrollbarHighlightColor= '#808080';
		bs.scrollbarShadowColor= '#808080';
		bs.scrollbarTrackColor= '#D4D0C8';
		bs.border='0';
	}
}

function WBTB_setTab()
{
	//html和design按钮的样式更改
	var mhtml=document.getElementById("WBTB_TabHtml");
	var mdesign=document.getElementById("WBTB_TabDesign");
	if (WBTB_bTextMode)
	{
		mhtml.className="WBTB_TabOn";
		mdesign.className="WBTB_TabOff";
	}else{
		mhtml.className="WBTB_TabOff";
		mdesign.className="WBTB_TabOn";
	}
}

function WBTB_getEl(sTag,start)
{
	while ((start!=null) && (start.tagName!=sTag)) start = start.parentElement;
	return start;
}

function WBTB_UserDialog(what)
{
	if (!WBTB_validateMode()) return;
	WBTB.Frame.focus();
	WBTB.Frame.document.execCommand(what, true);
	
	/*/去掉添加图片时的src="file://
	if(what=="InsertImage")
	{
		WBTB.Frame.document.body.innerHTML=(WBTB.Frame.document.body.innerHTML).replace("src=\"file://","src=\"");
	}
	*/
	WBTB_pureText = false;
	WBTB.Frame.focus();
}

function WBTB_foreColor(arr)
{
	if (!WBTB_validateMode()) return;
	if (arr==null){
		arr=WBTB_colorPicker('forecolor');
	}
	if (arr != null) WBTB_format('forecolor', arr);
}

function WBTB_backColor(arr)
{
	if (!WBTB_validateMode()) return;
	if (arr==null){
		arr=WBTB_colorPicker('backcolor');
	}
	if (arr != null) WBTB_format('backcolor', arr);

}

// 开选择颜色窗口
function WBTB_colorPicker(commandName)
{
		if (WBTB.bIsIE5){
			var arr = showModalDialog("wbTextBox/selcolor.html", "", "dialogWidth:290px; dialogHeight:280px; status:0; help:0");
			WBTB.Frame.focus();
			return arr;
		}else{
			var fcolorWin = window.open("","fcolorWin","width=290,height=280");
			if (fcolorWin) {
				fcolorWin.focus();
			}else{
				alert("Please turn off your PopUp blocking software");
				return;
			}
		
			launchParameters = new Object();
			launchParameters['wbtb'] = window;
			launchParameters['commandName'] = commandName;
			fcolorWin.launchParameters = launchParameters;
			fcolorWin.location.href="wbTextBox/selcolor.html";
		}
}

// 执行execCommand
function WBTB_ExecuteCommand(commandName, middle, commandValue)
{
	if (!WBTB_validateMode()) return;
	WBTB.Frame.focus();
	if (commandName == 'backcolor' && !WBTB.bIsIE5) commandName = 'hilitecolor';
	WBTB.Frame.document.execCommand(commandName,middle,commandValue);
}

function OpenUpload()
{
	if (!WBTB_validateMode()) return;
	if (WBTB.bIsIE5){
		showModalDialog("UploadNewsPicture.htm", window, "dialogWidth:400px; dialogHeight:120px; status:0; help:0; scroll:no");
		WBTB.Frame.focus();
	}else{
		var imageWin = window.open("","imageWin","width=390,height=90");
		if (imageWin) {
			imageWin.focus();
		}else{
			alert("Please turn off your PopUp blocking software");
			return;
		}
	
		launchParameters = new Object();
		launchParameters['wbtb'] = window;
		imageWin.launchParameters = launchParameters;
		imageWin.location.href="UploadNewsPicture.htm";
//		imageWin.load();
	}
}

function WBTB_img()
{
	if (!WBTB_validateMode()) return;
	if (WBTB.bIsIE5){
		showModalDialog("wbTextBox/img.html", window, "dialogWidth:370px; dialogHeight:320px; status:0; help:0; scroll:no");
		WBTB.Frame.focus();
	}else{
		var imageWin = window.open("","imageWin","width=360,height=290");
		if (imageWin) {
			imageWin.focus();
		}else{
			alert("Please turn off your PopUp blocking software");
			return;
		}
	
		launchParameters = new Object();
		launchParameters['wbtb'] = window;
		imageWin.launchParameters = launchParameters;
		imageWin.location.href="wbTextBox/img.html";
//		imageWin.load();
	}
}

function WBTB_href()
{
	if (!WBTB_validateMode()) return;
	if (WBTB.bIsIE5){
		showModalDialog("wbTextBox/href.html", window, "dialogWidth:280px; dialogHeight:180px; status:0; help:0; scroll:no");
		WBTB.Frame.focus();
	}else{
		var hrefWin = window.open("","herfWin","width=280,height=160");
		if (hrefWin) {
			hrefWin.focus();
		}else{
			alert("Please turn off your PopUp blocking software");
			return;
		}
	
		launchParameters = new Object();
		launchParameters['wbtb'] = window;
		hrefWin.launchParameters = launchParameters;
		hrefWin.location.href="wbTextBox/href.html";
//		imageWin.load();	}
	}
}


function WBTB_forswf(arr)
{
	if (arr==null){
		if (WBTB.bIsIE5){
			var arr = showModalDialog("wbTextBox/swf.htm", "", "dialogWidth:240px; dialogHeight:180px; status:0; help:0; scroll:0");
			WBTB.Frame.focus();
		}else{
			var swfWin = window.open("","swfWin","width=260,height=150");
			if (swfWin) {
				swfWin.focus();
			}else{
				alert("Please turn off your PopUp blocking software");
				return;
			}
		
			launchParameters = new Object();
			launchParameters['wbtb'] = window;
			swfWin.launchParameters = launchParameters;
			swfWin.location.href="wbTextBox/swf.htm";
		}
	}
	
	if (arr != null){
		var ss;
		ss=arr.split("*")
		path=ss[0];
		row=ss[1];
		col=ss[2];
		fs = ss[3];
		fst = ss[4];
		var string;
		if (fs=="1" && fst.length>0){
			string="<table align='center'><tr><td align='center'><object classid='clsid:D27CDB6E-AE6D-11cf-96B8-444553540000' codebase='http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=6,0,0,0' width='"+row+"' height='"+col+"'><param name='movie' value='"+path+"'/><param name='quality' value='high'/><param name='play' value='-1'><embed src='"+path+"' type='application/x-shockwave-flash' pluginspage='http://www.macromedia.com/go/getflashplayer' quality='high' width='"+row+"' height='"+col+"'></embed></object><br/><br/><a href='"+ path +"'>"+ fst +"</a></td></tr></table>"
		}else{
			string="<center><object classid='clsid:D27CDB6E-AE6D-11cf-96B8-444553540000' codebase='http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=6,0,0,0' width='"+row+"' height='"+col+"'><param name='movie' value='"+path+"'/><param name='quality' value='high'/><param name='play' value='-1'><embed src='"+path+"' type='application/x-shockwave-flash' pluginspage='http://www.macromedia.com/go/getflashplayer' quality='high' width='"+row+"' height='"+col+"'></embed></object></center>"
		}content=WBTB.Frame.document.body.innerHTML;
		content=content+string;
		WBTB.Frame.document.body.innerHTML=content;
	}
}

function WBTB_forwmv(arr)
{
	if (arr==null){
		if (WBTB.bIsIE5){
			var arr = showModalDialog("wbTextBox/wmv.htm", "", "dialogWidth:260px; dialogHeight:170px; status:0; help:0; scroll:0");
			WBTB.Frame.focus();
		}else{
			var wmvWin = window.open("","wmvWin","width=260,height=150");
			if (wmvWin) {
				wmvWin.focus();
			}else{
				alert("Please turn off your PopUp blocking software");
				return;
			}
		
			launchParameters = new Object();
			launchParameters['wbtb'] = window;
			wmvWin.launchParameters = launchParameters;
			wmvWin.location.href="wbTextBox/wmv.htm";
		}
	}
	
	if (arr != null){
		var ss;
		ss=arr.split("*")
		path=ss[0];
		width=ss[1];
		height=ss[2];
		var string;
		string="<center><object align='center' classid='CLSID:6BF52A52-394A-11d3-B153-00C04F79FAA6' hspace='5' vspace='5' width='"+ width +"' height='"+ height +"' codebase=http://activex.microsoft.com/activex/controls/mplayer/en/nsmp2inf.cab#Version=5,1,52,701standby=Loading Microsoft? Windows Media? Player components... type=application/x-oleobject><param name='URL' value='"+ path +"'/><param name='UIMode' value='full'><param name='AutoStart' value='true'><param name='Enabled' value='true'></object></center>";
		//string="<center><embed src='"+ path+"' width='"+ width +"' height='"+ height +"' autostart='true' loop='false'></embed></center>";
		content=WBTB.Frame.document.body.innerHTML;
		content=content+string;
		WBTB.Frame.document.body.innerHTML=content;
	}
}


function WBTB_forrm(arr)
{
	if (arr==null){
		if (WBTB.bIsIE5){
			var arr = showModalDialog("wbTextBox/rm.htm", "", "dialogWidth:260px; dialogHeight:180px; status:0; help:0; scroll:0");
			WBTB.Frame.focus();
		}else{
			var rmWin = window.open("","rmWin","width=260,height=150");
			if (rmWin) {
				rmWin.focus();
			}else{
				alert("Please turn off your PopUp blocking software");
				return;
			}
		
			launchParameters = new Object();
			launchParameters['wbtb'] = window;
			rmWin.launchParameters = launchParameters;
			rmWin.location.href="wbTextBox/rm.htm";
		}
	}

	if (arr != null)
	{
		var ss;
		ss=arr.split("*")
		path=ss[0];
		row=ss[1];
		col=ss[2];
		var string;
		var id = "Clip"+ Math.round(Math.random()*10000);
		string="<center><object classid='clsid:CFCDAA03-8BE4-11cf-B84B-0020AFBBCCFA' width='"+row+"' height='"+col+"'><param name='CONTROLS' value='ImageWindow'/><param name='CONSOLE' value='"+ id +"'/><param name='AUTOSTART' value='-1'/><param name='src' value='"+path+"'/></object><br/><object classid='clsid:CFCDAA03-8BE4-11cf-B84B-0020AFBBCCFA'  width='"+row+"' height='60'><param name='CONTROLS' value='ControlPanel,StatusBar'/><param name='CONSOLE' value='"+id+"'/></object></center>";
		content=WBTB.Frame.document.body.innerHTML;
		content=content+string;
		WBTB.Frame.document.body.innerHTML=content;
	}
	
}

// 插入iwms函数
function WBTB_iwms(fname)
{
	if (!WBTB_validateMode()) return;
	
	if (WBTB.bIsIE5){
		showModalDialog("wbTextBox/"+ fname +".htm", window, "dialogWidth:270px; dialogHeight:140px; status:0; help:0; scroll:no");
		WBTB.Frame.focus();
	}else{
		var iwmsWin = window.open("","iwmsWin","width=270,height=120");
		if (pageWin) {
			iwmsWin.focus();
		}else{
			alert("Please turn off your PopUp blocking software");
			return;
		}
	
		launchParameters = new Object();
		launchParameters['wbtb'] = window;
		iwmsWin.launchParameters = launchParameters;
		iwmsWin.location.href="wbTextBox/"+ fname +".htm";
	}
}

// 选择iwms样式表
function WBTB_iwmsCss(css)
{
	if (!WBTB_validateMode()) return;
	
	if (css==null){
		if (WBTB.bIsIE5){
			showModalDialog("wbTextBox/iwms_css.htm", window, "dialogWidth:270px; dialogHeight:140px; status:0; help:0; scroll:no");
			WBTB.Frame.focus();
		}else{
			var iwmsWin = window.open("","iwmsWin","width=270,height=120");
			if (pageWin) {
				iwmsWin.focus();
			}else{
				alert("Please turn off your PopUp blocking software");
				return;
			}
		
			launchParameters = new Object();
			launchParameters['wbtb'] = window;
			iwmsWin.launchParameters = launchParameters;
			iwmsWin.location.href="wbTextBox/iwms_css.htm";
		}
	}
	
	if (css!=null){
		if (WBTB.Frame.document.styleSheets.length>0){
			WBTB.Frame.document.styleSheets(0).href = css;
		}

	}
	
}


function WBTB_insertTable(arr)
{
	if (! WBTB_validateMode())	return;
	WBTB.Frame.focus();
	if (arr==null){
		if (WBTB.bIsIE5){
			var arr = showModalDialog("wbTextBox/inserttable.html", "", "dialogWidth:20em;dialogHeight:20em;help: no; scroll: no; status: no");
		}else{
			var arr;
			var tableWin = window.open("","tableWin","width=18em,height=11em");
			if (tableWin) {
				tableWin.focus();
			}else{
				alert("Please turn off your PopUp blocking software");
				return;
			}
		
			launchParameters = new Object();
			launchParameters['wbtb'] = window;
			tableWin.launchParameters = launchParameters;
			tableWin.location.href="wbTextBox/inserttable.html";
		}
	}
	
	if (arr != null){
		WBTB_InsertHtml(arr);
	}
	WBTB.Frame.focus();
}

function WBTB_tableProp(){
	var tableflag=false;
	WBTB.Frame.focus();
	var xsel=WBTB.Frame.document.selection;
	var xobj=WBTB.Frame.document.selection.createRange();
	if(xsel.type=="None"||xsel.type=="Text"){
		xsel=xobj.parentElement();
		while(xsel.tagName!="BODY"&&tableflag==false){
			if(xsel.tagName=="TABLE"){tableflag=true;}
			if(tableflag==false){xsel=xsel.parentElement;}
		}
	}else if(xsel.type=="Control"){
		xsel=xobj.item(0);
		if(xsel.tagName=="TABLE"){
			tableflag=true;
		}else{
			while(xsel.tagName!="BODY"&&tableflag==false){
				if(xsel.tagName=="TABLE"){tableflag=true;}
				if(tableflag==false){xsel=xsel.parentElement;}
			}
		}
	}
	if(tableflag==true){
		if(xsel.className!=""&&xsel.className!=null){tableclass=xsel.className;}else{tableclass="";}
		if(xsel.width!=""&&xsel.width!=null){tablewidthspecified="yes";tablewidth=xsel.width;}else{tablewidthspecified="no";tablewith="";}
		if(xsel.align!=""&&xsel.align!=null){tablealign=xsel.align;}else{tablealign="";}
		if(xsel.border!=""&&xsel.border!=null){tablebordersize=xsel.border;}else{tablebordersize="";}
		if(xsel.cellPadding!=""&&xsel.cellPadding!=null){tablecellpadding=xsel.cellPadding;}else{tablecellpadding="";}
		if(xsel.cellSpacing!=""&&xsel.cellSpacing!=null){tablecellspacing=xsel.cellSpacing;}else{tablecellspacing="";}
		if(xsel.borderColor!=""&&xsel.borderColor!=null){tablebordercolor=xsel.borderColor;}else{tablebordercolor="";}
		if(xsel.bgColor!=""&&xsel.bgColor!=null){tablebackgroundcolor=xsel.bgColor;}else{tablebackgroundcolor="";}
		tableiscancel="";
		window.showModalDialog("wbTextBox/tableprops.html",window," dialogWidth: 20.5em; dialogHeight: 18em; help: no;scroll: no; status: no");
		if(tableiscancel=="no"){
			if(tablewidthspecified=="yes"){
				var tw1="";
				if(tablewidthtype=="percentage"){
					tw1=tablewidth+"%";
				}else{
					tw1=tablewidth;
				}
				xsel.width=tw1;
			}else{
				xsel.removeAttribute("width",0);
			}
			if(tablealign!=""&&tablealign!="Default"){xsel.align=tablealign;}else{xsel.removeAttribute("align",0);}
			if(tableclass!=""&&tableclass!="Default"){xsel.className=tableclass;}else{xsel.removeAttribute("classname",0);}
			if(tablebordersize!=""&&tablebordersize!=null){xsel.border=tablebordersize;}else{xsel.removeAttribute("border",0);}
			if(tablecellpadding!=""&&tablecellpadding!=null){xsel.cellPadding=tablecellpadding;}else{xsel.removeAttribute("cellpadding",0);}
			if(tablecellspacing!=""&&tablecellspacing!=null){xsel.cellSpacing=tablecellspacing;}else{xsel.removeAttribute("cellspacing",0);}
			if(tablebordercolor!=""&&tablebordercolor!="Default"){xsel.borderColor=tablebordercolor;}else{xsel.removeAttribute("bordercolor",0);}
			if(tablebackgroundcolor!=""&&tablebackgroundcolor!="Default"){xsel.bgColor=tablebackgroundcolor;}else{xsel.removeAttribute("bgcolor",0);}
		}
	}
}

function WBTB_cellProp(){
	var cellflag=false;
	WBTB.Frame.focus();
	var xsel=WBTB.Frame.document.selection;
	var xobj=WBTB.Frame.document.selection.createRange();
	if(xsel.type=="None"||xsel.type=="Text"){
		xsel=xobj.parentElement();
		while(xsel.tagName!="BODY"&&cellflag==false){
			if(xsel.tagName=="TD"){cellflag=true;}
			if(cellflag==false){xsel=xsel.parentElement;}
		}
	}else if(xsel.type=="Control"){
		xsel=xobj.item(0);
		if(xsel.tagName=="TD"){
			cellflag=true;
		}else{
			while(xsel.tagName!="BODY"&&cellflag==false){
				if(xsel.tagName=="TD"){cellflag=true;}
				if(cellflag==false){xsel=xsel.parentElement;}
			}
		}
	}
	if(cellflag==true){
		if(xsel.width!=""&&xsel.width!=null){tablewidthspecified="yes";tablewidth=xsel.width;}else{tablewidthspecified="no";tablewith="";}
		if(xsel.align!=""&&xsel.align!=null){tablealign=xsel.align;}else{tablealign="";}
		if(xsel.className!=""&&xsel.className!=null){tablecellclass=xsel.className;}else{tablecellclass="";}
		if(xsel.vAlign!=""&&xsel.vAlign!=null){tablevalign=xsel.vAlign;}else{tablevalign="";}
		if(xsel.borderColor!=""&&xsel.borderColor!=null){tablebordercolor=xsel.borderColor;}else{tablebordercolor="";}
		if(xsel.bgColor!=""&&xsel.bgColor!=null){tablebackgroundcolor=xsel.bgColor;}else{tablebackgroundcolor="";}
		tableiscancel="";
		window.showModalDialog("wbTextBox/cellprops.html",window,"dialogWidth: 20em; dialogHeight: 14.5em;help: no;scroll: no; status: no");
		if(tableiscancel=="no"){
			if(tablewidthspecified=="yes"){
				var tw1="";
				if(tablewidthtype=="percentage"){tw1=tablewidth+"%";}else{tw1=tablewidth;}
				xsel.width=tw1;
			}else{
				xsel.removeAttribute("width",0);
			}
			if(tablealign!=""&&tablealign!="Default"){xsel.align=tablealign;}else{xsel.removeAttribute("align",0);}
			if(tablevalign!=""&&tablevalign!="Default"){xsel.vAlign=tablevalign;}else{xsel.removeAttribute("valign",0);}
			if(tablecellclass!=""&&tablecellclass!="Default"){xsel.className=tablecellclass;}else{xsel.removeAttribute("classname",0);}
			if(tablebordercolor!=""&&tablebordercolor!="Default"){xsel.borderColor=tablebordercolor;}else{xsel.removeAttribute("bordercolor",0);}
			if(tablebackgroundcolor!=""&&tablebackgroundcolor!="Default"){xsel.bgColor=tablebackgroundcolor;}else{xsel.removeAttribute("bgcolor",0);}
		}
	}
}

function WBTB_tablecommand(command)
{
	var cellflag=false;
	var rowflag=false;
	var tableflag=false;
	var cellindex,rowindex,tableref;
	WBTB.Frame.focus();
	var xsel=WBTB.Frame.document.selection;
	var xobj=WBTB.Frame.document.selection.createRange();
	if(xsel.type=="None"||xsel.type=="Text"){
		xsel=xobj.parentElement();
		while(xsel.tagName!="BODY"&&cellflag==false){
			if(xsel.tagName=="TD"){cellindex=xsel.cellIndex;cellflag=true;}
			if(cellflag==false){xsel=xsel.parentElement;}
		}
	}else if(xsel.type=="Control"){
		xsel=xobj.item(0);
		if(xsel.tagName=="TD"){
			cellindex=xsel.cellIndex;
			cellflag=true;
		}else{
			while(xsel.tagName!="BODY"&&cellflag==false){
				if(xsel.tagName=="TD"){cellindex=xsel.cellIndex;cellflag=true;}
				if(cellflag==false){xsel=xsel.parentElement;}
			}
		}
	}
	if(cellflag==true){
		xsel=WBTB.Frame.document.selection;
		xobj=WBTB.Frame.document.selection.createRange();
		if(xsel.type=="None"||xsel.type=="Text"){
			xsel=xobj.parentElement();
			while(xsel.tagName!="BODY"&&rowflag==false){
				if(xsel.tagName=="TR"){
					rowindex=xsel.rowIndex;
					rowflag=true;
				}
				if(rowflag==false){xsel=xsel.parentElement;}
			}
		}else if(xsel.type=="Control"){
			xsel=xobj.item(0);
			if(xsel.tagName=="TR"){
				rowindex=xsel.rowIndex;
				rowflag=true;
			}else{
				while(xsel.tagName!="BODY"&&rowflag==false){
					if(xsel.tagName=="TR"){
						rowindex=xsel.rowIndex;
						rowflag=true;
					}
					if(rowflag==false){
						xsel=xsel.parentElement;
					}
				}
			}
		}
		xsel=WBTB.Frame.document.selection;
		xobj=WBTB.Frame.document.selection.createRange();
		if(xsel.type=="None"||xsel.type=="Text"){
			xsel=xobj.parentElement();
			while(xsel.tagName!="BODY"&&tableflag==false){
				if(xsel.tagName=="TABLE"){tableflag=true;}
				if(tableflag==false){xsel=xsel.parentElement;}
			}
		}else if(xsel.type=="Control"){
			xsel=xobj.item(0);
			if(xsel.tagName=="TABLE"){
				tableflag=true;
			}else{
				while(xsel.tagName!="BODY"&&tableflag==false){
					if(xsel.tagName=="TABLE"){tableflag=true;}
					if(tableflag==false){xsel=xsel.parentElement;}
				}
			}
		}
		if(command==3){
			var temprowcount=xsel.rows.length;
			var tempcell;
			var tempspancount=0;
			var tempspanholder;
			var tempcellwidth=xsel.rows[rowindex].cells[cellindex].width;
			var xpositequiv=-1;
			var xposcount=0;
			while(xposcount<=cellindex){
				xpositequiv+=parseInt(xsel.rows[rowindex].cells[xposcount].colSpan);
				xposcount++;
			}
			var ypositequiv=-1;
			var yposcount=0;
			var ymax=xsel.rows[rowindex].cells.length;
			while(yposcount<=ymax-1){
				ypositequiv+=parseInt(xsel.rows[rowindex].cells[yposcount].colSpan);
				yposcount++;
			}
			var idealinsert=xpositequiv+1;
			var zi2=0;
			var zirowtouse=0;
			var zirowtot=xsel.rows.length;
			var rowarray=new Array(zirowtot);
			var rowarray2=new Array(zirowtot);
			for(init1=0;init1<=zirowtot-1;init1++){
				rowarray[init1]=0;
				rowarray2[init1]=0;
			}
			for(zi1=0;zi1<=zirowtot-1;zi1++){
				zi2=0;
				while(zi2<idealinsert&&(rowarray[zi1]==null||rowarray[zi1]<idealinsert)){
					rowarray[zi1]+=parseInt(xsel.rows[zi1].cells[zi2].colSpan);
					rowarray2[zi1]++;
					zi2++;
				}
			}
			var allequal=true;
			var zi3a,zi3b;
			var zthemax=0;
			for(zi3=0;zi3<=zirowtot-1;zi3++){
				zi3a=rowarray[0];
				zi3b=rowarray[zi3];
				if(zi3b>zthemax){zthemax=zi3b;}
				if(zi3a!=zi3b){allequal=false;}
			}
			if(allequal==false){
				var zi4=0;
				var allequal2=true;
				while(zthemax<=ypositequiv&&allequal==false){
					for(zi5=0;zi5<=zirowtot-1;zi5++){
						rowarray[zi5]+=parseInt(xsel.rows[zi5].cells[rowarray2[zi5]].colSpan);
					}
					for(zi3=0;zi3<=zirowtot-1;zi3++){
						zi3a=rowarray[0];
						zi3b=rowarray[zi3];
						if(zi3b>zthemax){zthemax=zi3b;}
						if(zi3a!=zi3b){allequal2=false;}
					}
					if(allequal2==true){allequal=true;}
					for(zi8=0;zi8<=zirowtot-1;zi8++){rowarray2[zi8]++;}
					}
				}
				var zi9;
				for(zi7=0;zi7<=zirowtot-1;zi7++){
					zi9=xsel.rows[zi7].insertCell(rowarray2[zi7]);
					zi9.width=tempcellwidth;
				}
		}else if(command==4){
			var temprowcount=xsel.rows.length;
			for(iccount=0;iccount<=temprowcount-1;iccount++){
				xsel.rows[iccount].deleteCell(cellindex);
			}
			}else if(command==1){
				var tempcell;
				var tempcellb;
				var tempcellcount=xsel.rows[rowindex].cells.length;
				var cellcolarray=new Array(tempcellcount);
				var cellrowarray=new Array(tempcellcount);
				for(cacount=0;cacount<=tempcellcount-1;cacount++){
					cellcolarray[cacount]=xsel.rows[rowindex].cells(cacount).colSpan;
					cellrowarray[cacount]=xsel.rows[rowindex].cells(cacount).rowSpan;
				}
				tempcell=xsel.insertRow(rowindex);
				for(cbcount=0;cbcount<=tempcellcount-1;cbcount++){
					tempcellb=tempcell.insertCell();
					if(cellcolarray[cbcount]!=1){tempcellb.colSpan=cellcolarray[cbcount];}
				}
		}else if(command==2){
				var temprowcount=xsel.rows.length;tempcell=xsel.deleteRow(rowindex);
		}else if(command==5){
				if(xsel.rows[rowindex].cells[cellindex+1]){
					var x=parseInt(xsel.rows[rowindex].cells[cellindex].colSpan)+parseInt(xsel.rows[rowindex].cells[cellindex+1].colSpan);
					var y=xsel.rows[rowindex].cells[cellindex].innerHTML+" "+xsel.rows[rowindex].cells[cellindex+1].innerHTML;
					xsel.rows[rowindex].deleteCell(cellindex+1);
					xsel.rows[rowindex].cells[cellindex].colSpan=x;
					xsel.rows[rowindex].cells[cellindex].innerHTML=y;
				}
		}else if(command==6){
				var yatemprow=xsel.rows.length;
				var yamax=0;
				for(ya1=0;ya1<=yatemprow-1;ya1++){
					var ypositequiv=-1;
					var yposcount=0;
					var ymax=xsel.rows[ya1].cells.length;
					while(yposcount<=ymax-1){
						ypositequiv+=parseInt(xsel.rows[ya1].cells[yposcount].colSpan);
						yposcount++;
					}
					if(ypositequiv>yamax){yamax=ypositequiv;}
				}
				var rowarray=new Array();
				var rowarray2=new Array();
				var myrowcount=xsel.rows.length;
				for(ra1=0;ra1<=myrowcount-1;ra1++){
					rowarray[ra1]=new Array();
					rowarray2[ra1]=0;
					for(cr1=0;cr1<=yamax;cr1++){rowarray[ra1][cr1]=777;}
				}
				var tempra;
				var ra2=0;
				for(ra3=0;ra3<=yamax;ra3++){
					ra2=0;
					while(ra2<=myrowcount-1){
						if(xsel.rows[ra2].cells[ra3]){
							tempra=parseInt(xsel.rows[ra2].cells[ra3].rowSpan);
							if(tempra>1){
								rowarray[ra2][ra3]=ra3+rowarray2[ra2];
								for(zoo=1;zoo<=tempra-1;zoo++){rowarray2[ra2+zoo]--;}
							}
						}
						if(rowarray[ra2][ra3-1]!=ra3+rowarray2[ra2]){
							rowarray[ra2][ra3]=ra3+rowarray2[ra2];
						}else{
							rowarray[ra2][ra3]=555;
						}
						ra2++;
					}
				}
				var samx="";
				var samcount=0;
				for(rx1=0;rx1<=myrowcount-1;rx1++){
					samcount=rowarray[rx1].length;
					for(rx2=0;rx2<=samcount-1;rx2++){
						samx+="-"+rowarray[rx1][rx2];
					}
					samx+="\n";
				}
				var j=parseInt(xsel.rows[rowindex].cells[cellindex].rowSpan);
				var jcount=rowarray[rowindex].length;
				var jval=0;
				for(jc1=0;jc1<=jcount-1;jc1++){
					if(rowarray[rowindex][jc1]==cellindex){jval=jc1;}
				}
				if(xsel.rows[rowindex+j]){
					var cellindex2=rowarray[rowindex+j][jval];
					var x=parseInt(xsel.rows[rowindex].cells[cellindex].rowSpan)+parseInt(xsel.rows[rowindex+j].cells[cellindex2].rowSpan);
					var y=xsel.rows[rowindex].cells[cellindex].innerHTML+" "+xsel.rows[rowindex+j].cells[cellindex2].innerHTML;
					xsel.rows[rowindex+j].deleteCell(cellindex2);
					xsel.rows[rowindex].cells[cellindex].rowSpan=x;
					xsel.rows[rowindex].cells[cellindex].innerHTML=y;
				}
		}else if(command==7){
				var getrowspan=parseInt(xsel.rows[rowindex].cells[cellindex].rowSpan);
				var getcolspan=parseInt(xsel.rows[rowindex].cells[cellindex].colSpan);
				if(getrowspan>1){
					var xr1=getrowspan-1;
					var xrposit=rowindex;
					var xrcposit=cellindex;
					var xrholder;xsel.rows[rowindex].cells[cellindex].rowSpan=1;
					for(xr2=1;xr2<=xr1;xr2++){
						xrholder=xsel.rows[xrposit+xr2].insertCell(xrcposit);
						xrholder.colSpan=xsel.rows[rowindex].cells[cellindex].colSpan;
					}
				}
				if(getcolspan>1){
					var yr1=getcolspan-1;
					var yrposit=rowindex;
					var yrcposit=cellindex;
					var yrholder;xsel.rows[rowindex].cells[cellindex].colSpan=1;
					for(yr2=1;yr2<=yr1;yr2++){
						yrholder=xsel.rows[yrposit].insertCell(yrcposit);
						yrholder.rowSpan=xsel.rows[rowindex].cells[cellindex].rowSpan;
					}
				}
			}
		}
}


function WBTB_GetRangeReference(editor)
{
	editor.focus();
	var objReference = null;
	var RangeType = editor.document.selection.type;
	var selectedRange = editor.document.selection.createRange();
	
	switch(RangeType)
	{
	case 'Control' :
	if (selectedRange.length > 0 ) 
	{
	objReference = selectedRange.item(0);
	}
	break;
	
	case 'None' :
	objReference = selectedRange.parentElement();
	break;
	
	case 'Text' :
	objReference = selectedRange.parentElement();
	break;
	}
	return objReference
}

function WBTB_CheckTag(item,tagName)
{
	if (item.tagName.search(tagName)!=-1)
	{
	return item;
	}
	if (item.tagName=='BODY')
	{
	return false;
	}
	item=item.parentElement;
	return WBTB_CheckTag(item,tagName);
}

function WBTB_code()
{
	WBTB_specialtype("<div class='quote' style='cursor:hand'; title='Click to run the code' onclick=\"preWin=window.open('','','');preWin.document.open();preWin.document.write(this.innerText);preWin.document.close();\">","</div>");	
}

function WBTB_replace()
{
	var arr = showModalDialog("wbTextBox/replace.html", "", "dialogWidth:16em; dialogHeight:11em; status:0; help:0; scroll:0");
	
	if (arr != null){
		var ss;
		ss=arr.split("*")
		a=ss[0];
		b=ss[1];
		i=ss[2];
		con=WBTB.Frame.document.body.innerHTML;
		if (i==1)
		{
			con=WBTB_rCode(con,a,b,true);
		}else{
			con=WBTB_rCode(con,a,b);
		}
		WBTB.Frame.document.body.innerHTML=con;
	}
	else WBTB.Frame.focus();
}

// 清理word代码
function WBTB_CleanCode() {
	editor=WBTB.Frame;
	editor.focus();
	
	/// 图片前vml代码
	var html = editor.document.body.innerHTML;
	var re = new RegExp('<\\?xml:namespace prefix = v ns = "urn:schemas-microsoft-com:vml" />.+?</v:shapetype>',"ig");
	html = html.replace(re,"");
	// 图片转换
	re = new RegExp('<v:shape .+?width.+?(\\d+).+?height.+?(\\d+).+?src="(.+?)".+?</v:imagedata></v:shape>',"ig");
	editor.document.body.innerHTML = html.replace(re, "<img width='$1' height='$2' src='$3'>");
	

	// 0bject based cleaning
	var body = editor.document.body;
	for (var index = 0; index < body.all.length; index++) {
		tag = body.all[index];
		//if (tag.Attribute["className"].indexOf("mso") > -1)
		tag.removeAttribute("className","",0);
		tag.removeAttribute("style","",0);
		tag.removeAttribute("lang","",0);
	}

	// Regex based cleaning
	html = editor.document.body.innerHTML;
	html = html.replace(/<o:p>&nbsp;<\/o:p>/gi, "");
	html = html.replace(/o:/gi, "");
	//html = html.replace(/<st1:[^>]*>/gi, "");

	// Final clean up of empty tags
	html = html.replace(/<font[^>]*>\s*<\/font>/gi, "");
	html = html.replace(/<a[^>]*><\/a>/gi, "");
//	html = html.replace(/<span>\s*<\/span>/gi, "");
	html = html.replace(/<span>(.*?)<\/span>/gi, "$1");
	
	editor.document.body.innerHTML = html;
}

// 过滤脚本
function WBTB_FilterScript(content)
{
	content = WBTB_rCode(content, 'javascript:', 'javascript :');
	content = WBTB_rCode(content, 'vbscript:', 'vbscript :');
	//var RegExp = /<script[^>]*>(.|\n)*<\/script>/ig;
	//content = content.replace(RegExp, "<!-- Script Filtered -->");
	var RegExp = /<script[^>]*>/ig;
	content = content.replace(RegExp, "<!-- Script Filtered\n");
	RegExp = /<\/script>/ig;
	content = content.replace(RegExp, "\n-->");
	RegExp = /(<[^>]+\s)on\w+=[^>]+?(\s|>)/ig;
	content = content.replace(RegExp,"$1$2");
	var RegExp = /<iframe[^>]*>/ig;
	content = content.replace(RegExp, "<!-- Iframe Filtered");
	RegExp = /<\/iframe>/ig;
	content = content.replace(RegExp, " -->");
	RegExp = /<object.+text\/x-scriptlet.*>.*<\/object>/ig;
	content = content.replace(RegExp, "<!-- scriptlet filtered -->");
	return content;
}

// 只可以粘贴纯文件事件
function WBTB_onPaste(o)
{
	if (window.clipboardData){
		var sText =  WBTB_HTMLEncode(window.clipboardData.getData("Text"))  ;
		WBTB_InsertHtml(sText);
		WBTB.Frame.focus();
		o.event.returnValue = false;
	}else{
		o.event.returnValue = true;
	}
}

function WBTB_cleanHtml()
{
	var fonts = WBTB.Frame.document.body.getElementsByTagName("FONT");
	var curr;
	for (var i = fonts.length - 1; i >= 0; i--) {
		curr = fonts[i];
		if (curr.style.backgroundColor == "#ffffff") curr.outerHTML = curr.innerHTML;
	}
}

function WBTB_getPureHtml()
{
	var str = "";
	//var paras = WBTB.Frame.document.body.all.tags("P");
	//if (paras.length > 0){
	//  for	(var i=paras.length-1; i >= 0; i--) str= paras[i].innerHTML + "\n" + str;
	//} else {
	str = WBTB.Frame.document.body.innerHTML;
	//}
	str=WBTB_correctUrl(str);
	return str;
}


function WBTB_correctUrl(cont)
{
	var url=location.href.substring(0,location.href.lastIndexOf("/")+1);
	var dep=0;
	cont=WBTB_rCode(cont,location.href+"#","#");
	while(url.lastIndexOf("/")>7){
		var s="";
		url = url.substring(0,url.lastIndexOf("/")+1);
		for (var i=0;i<dep;i++){
			s+="../";
		}
		//cont=WBTB_rCode(cont,url,"");
		cont=WBTB_rCode(cont,"(<[^<>]*)"+url+"([^<>]*>)","$1"+s+"$2");
		url = url.substr(0,url.length-1);
		dep++;
	
	}
	return cont;
}

var WBTB_bLoad=false;
var WBTB_pureText=true;
var WBTB_bTextMode=false;

WBTB_public_description=new WBTB_editor;

function WBTB_editor()
{
	this.put_HtmlMode=WBTB_setMode;
	this.put_value=WBTB_putText;
	this.get_value=WBTB_getText;
}

function WBTB_getText()
{
	if (WBTB_bTextMode)
		//return WBTB.Frame.document.body.innerText;
		return WBTB.HtmlArea.value;
	else
	{
		WBTB_cleanHtml();
		return WBTB.Frame.document.body.innerHTML;
	}
}

function WBTB_putText(v)
{
	if (WBTB_bTextMode){
//		WBTB.Frame.document.body.innerText = v;
		WBTB.HtmlArea.value=v;
	}else{
		WBTB.Frame.document.body.innerHTML = v;
	}
}

function WBTB_InitDocument(hiddenid, charset)
{	
	if (WBTB.bIsIE5){
		WBTB.Frame=frames["WBTB_Composition"];
	}else{
		WBTB.Frame=document.getElementById("WBTB_Composition").contentWindow;
	}
	WBTB.HtmlArea=document.getElementById("WBTB_HtmlArea");
	WBTB.Hidden=hiddenid;
	
	if (WBTB.tempEdit && !WBTB.fileEdit){
		WBTB.fileEdit = true;
	}

	SetTab();

	if (charset!=null)
		WBTB.Charset=charset;
	var js = "<script type=\"text/javascript\">function WBTB_submit(){";
	js += "parent.WBTB_CopyData('"+ hiddenid +"');";
	js += "var WBTB_form=parent.document.getElementById('"+ hiddenid +"').form;";
	js += "var os = WBTB_form.tags(\"input\");";
	js += "var o;";
	js += "for (var i=0;i<os.length;i++) {if(os[i].type==\"submit\") {o=os[i];}} ";
	js += "if (o!=null) o.click();";
	js += "}</script>";
	var WBTB_bodyTag="<html><head><title>DvNews Editor</title><style type=\"text/css\">\r\n.quote{margin:5px 20px;border:1px solid #CCCCCC;padding:5px; background:#F3F3F3 }\r\nbody{boder:0px}</style>\r\n"+ js +"<script type='text/javascript' for='document' event='onkeydown'>if(event.ctrlKey && window.event.keyCode==13){WBTB_submit();}</script></head>\r\n<body "+(WBTB.pasteTextOnly?"onpaste='parent.WBTB_onPaste(window)' ":"")+"bgcolor=\"#FFFFFF\">";
	var editor=WBTB.Frame;
	var s=document.getElementById(hiddenid).value;
	
	if (WBTB.fileEdit){
		//取html前内容
		var re = /^((.|\n)+?)(?=<html)/ig;
		var arr = re.exec(s);
		if (arr!=null){
			WBTB.beforHtml = arr[0];
//			alert(WBTB.beforHtml);
			s = s.replace(re, "");
		}
	}
	
	if (!WBTB.bIsIE5){
		editor.document.designMode="On";
	}
	if (WBTB_bLoad){
		editor.document.body.innerHTML = "";
	}
	editor.document.open();
	if (WBTB.tempEdit){
		s = WBTB_BeforTempEdit(s);
		editor.document.write(s);
	}else if (WBTB.fileEdit){
		editor.document.write(s);
	}else{
		editor.document.write(WBTB_bodyTag + s + "</body></html>");
	}
	editor.document.close();
	editor.document.body.contentEditable = true;
	editor.document.charset=WBTB.Charset;
	
	
	if (WBTB.startHtml && !WBTB_bLoad){
		WBTB_bTextMode = true;
		WBTB_setTab();
		document.getElementById("WBTB_Toolbars").style.display='none';
		document.getElementById("WBTB_Composition").style.display='none';
		WBTB.HtmlArea.style.display='';
		WBTB.HtmlArea.value = document.getElementById(hiddenid).value;
		WBTB.HtmlArea.focus();
	}else{
		WBTB_setStyle();
		editor.focus();
	}
	WBTB_bLoad=true;
}


function WBTB_doSelectClick(str, el) {
	var Index = el.selectedIndex;
	if (Index != 0){
		el.selectedIndex = 0;
		WBTB_format(str,el.options[Index].value);
	}
}

//应用html
function WBTB_specialtype(Mark1, Mark2){
	var strHTML;
	if (WBTB.bIsIE5) WBTB_selectRange();
	if (WBTB.RangeType == "Text"){
		if (Mark2==null)
		{
			strHTML = "<" + Mark1 + ">" + WBTB.edit.htmlText + "</" + Mark1 + ">"; 
		}else{
			strHTML = Mark1 + WBTB.edit.htmlText +  Mark2;
		}
		WBTB.edit.pasteHTML(strHTML);
		WBTB.Frame.focus();
		WBTB.edit.select();
	}		
}

//选择内容替换文本
function WBTB_InsertHtml(str1)
{
	WBTB.Frame.focus();
	if (WBTB.bIsIE5) {
		sel = WBTB.Frame.document.selection.createRange();
		sel.pasteHTML(str1);
	}else{
		selection = WBTB.Frame.window.getSelection();
		if (selection) {
			range = selection.getRangeAt(0);
		} else {
			range = WBTB.Frame.document.createRange();
		}

	        var fragment = WBTB.Frame.document.createDocumentFragment();
	        var div = WBTB.Frame.document.createElement("div");
	        div.innerHTML = str1;

	        while (div.firstChild) {
	            fragment.appendChild(div.firstChild);
	        }

	        selection.removeAllRanges();
	        range.deleteContents();
	
	        var node = range.startContainer;
	        var pos = range.startOffset;

	        switch (node.nodeType) {
	            case 3:
	                if (fragment.nodeType == 3) {
	                    node.insertData(pos, fragment.data);
	                    range.setEnd(node, pos + fragment.length);
	                    range.setStart(node, pos + fragment.length);
	                } else {
	                    node = node.splitText(pos);
	                    node.parentNode.insertBefore(fragment, node);
	                    range.setEnd(node, pos + fragment.length);
	                    range.setStart(node, pos + fragment.length);
	                }
	                break;
	
	            case 1:
	                node = node.childNodes[pos];
	                node.parentNode.insertBefore(fragment, node);
	                range.setEnd(node, pos + fragment.length);
	                range.setStart(node, pos + fragment.length);
	                break;
	        }
		selection.addRange(range);
	}


}


function WBTB_selectRange(){
	WBTB.selection = WBTB.Frame.document.selection;
	WBTB.edit = WBTB.Frame.document.selection.createRange();
	WBTB.RangeType =  WBTB.Frame.document.selection.type;
}

// 替换字符
function WBTB_rCode(s,a,b,i,q){
	//s原字串，a要换掉pattern，b换成字串，i是否区分大小写，q是否转换?因为url中可能有
	if (q==null){
		a = a.replace("?","\\?");
	}else if (q) {
		a = a.replace("?","\\?");
	}
	if (i==null)
	{
		var r = new RegExp(a,"gi");
	}else if (i) {
		var r = new RegExp(a,"g");
	}else{
		var r = new RegExp(a,"gi");
	}
	return s.toString().replace(r,b); 
}

// 表格功能
// show/hide Gridlines
function WBTB_showGridlines() {
	if (!WBTB_validateMode()) return;
	
	var allForms = WBTB.Frame.document.body.getElementsByTagName("FORM");
	var allInputs = WBTB.Frame.document.body.getElementsByTagName("INPUT");
	var allTables = WBTB.Frame.document.body.getElementsByTagName("TABLE");
	var allLinks = WBTB.Frame.document.body.getElementsByTagName("A");

	// 表单
	for (a=0; a < allForms.length; a++) {
		if (!WBTB.ShowGridlines) {
			allForms[a].runtimeStyle.border = "1px dotted #FF0000"
		} else {
			allForms[a].runtimeStyle.cssText = ""
		}
	}

	// Input Hidden类
	for (b=0; b < allInputs.length; b++) {
		if (!WBTB.ShowGridlines) {
			if (allInputs[b].type.toUpperCase() == "HIDDEN") {
				allInputs[b].runtimeStyle.border = "1px dashed #000000"
				allInputs[b].runtimeStyle.width = "15px"
				allInputs[b].runtimeStyle.height = "15px"
				allInputs[b].runtimeStyle.backgroundColor = "#FDADAD"
				allInputs[b].runtimeStyle.color = "#FDADAD"
			}
		} else {
			if (allInputs[b].type.toUpperCase() == "HIDDEN")
				allInputs[b].runtimeStyle.cssText = ""
		}
	}

	// 表格
	for (i=0; i < allTables.length; i++) {
			if (!WBTB.ShowGridlines) {
				allTables[i].runtimeStyle.border = "1px dotted #BFBFBF"
			} else {
				allTables[i].runtimeStyle.cssText = ""
			}

			allRows = allTables[i].rows
			for (y=0; y < allRows.length; y++) {
			 	allCellsInRow = allRows[y].cells
					for (x=0; x < allCellsInRow.length; x++) {
						if (WBTB.ShowGridlines == "0") {
							allCellsInRow[x].runtimeStyle.border = "1px dotted #BFBFBF"
						} else {
							allCellsInRow[x].runtimeStyle.cssText = ""
						}
					}
			}
	}

	// 链接 A
	for (a=0; a < allLinks.length; a++) {
		if (WBTB.ShowGridlines == "0") {
			if (allLinks[a].href.toUpperCase() == "") {
				allLinks[a].runtimeStyle.borderBottom = "1px dashed #000000"
			}
		} else {
			allLinks[a].runtimeStyle.cssText = ""
		}
	}

	if (!WBTB.ShowGridlines) {
		WBTB.ShowGridlines = true
	} else {
		WBTB.ShowGridlines = false
	}

}

// 预览
function WBTB_View()
{
	if (WBTB_bTextMode) {
		//cont=WBTB.Frame.document.body.innerText;
		cont=WBTB.HtmlArea.value;
	}else if (WBTB.fileEdit) {
		cont=WBTB.Frame.document.body.parentElement.outerHTML;
	}else{
		cont=WBTB.Frame.document.body.innerHTML;
	}
	cont=WBTB_correctUrl(cont);
	bodyTag="<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\"><html><head><title>DvNews Perview</title><style type=\"text/css\">\r\n.quote{margin:5px 20px;border:1px solid #CCCCCC;padding:5px; background:#F3F3F3 }\r\nbody{boder:0px; font-family:Arial; font-size:10.5pt}</style></head><body>";
	if (WBTB.filterScript)
		cont=WBTB_FilterScript(cont);
//	cont=WBTB_rCode(cont,"\\[dvnews_page(=(.+?))?]","<br/><br/><hr size='2' width='95%' align='left'/>&nbsp; <font color='red' face='Tahoma,Arail' size='2'><strong>Next Page ... $2</strong></font><br/><hr size='2' width='95%' align='left'/>",true,false);
	preWin=window.open('preview','','left=0,top=0,width=550,height=400,resizable=1,scrollbars=1, status=1, toolbar=1, menubar=0');
	preWin.document.open();
	if (WBTB.fileEdit){
		// 过滤contentEditable
		var re = /(<body[^>]*?) contentEditable=true/ig;
		cont = cont.replace(re, "$1");
		preWin.document.write(cont);
	}else{
		preWin.document.write(bodyTag);
		preWin.document.write(cont);
		preWin.document.write("\r\n</body>\r\n</html>");
	}
	preWin.document.close();
	preWin.document.title="Preview";
	preWin.document.charset=WBTB.Charset;
}


// 修改编辑栏高度
function WBTB_Size(num)
{
	var obj=document.getElementById("WBTB_areaDiv");
	if (parseInt(obj.style.height)+num>=200) {
		obj.style.height = parseInt(obj.style.height) + num;
		WBTB.HtmlArea.style.height = WBTB.HtmlArea.parentElement.style.height;
	}
	if (parseInt(obj.style.height)>200){
		document.getElementById("wbtb_height").value=parseInt(obj.style.height);
	}else{
		document.getElementById("wbtb_height").value="";
	}
	if (num>0)
	{
		obj = document.getElementById("WBTB_Container");
		obj.style.width="100%";
	}
}

// 拷贝数据到hidden
function WBTB_CopyData(hiddenid)
{
	d = WBTB.Frame.document;
	if (WBTB_bTextMode)
	{
		//cont=d.body.innerText;
		cont=WBTB.HtmlArea.value;
	}else{
		var got = false;	// 是否已经在xhtml里取了内容
		if (WBTB.XhtmlEnabled) {
			try{
				cont=WBTBXHTML.GetXHTML(d.body)
			}catch(e){
				got = true;
			}
		}
		if (!got){
			if (WBTB.fileEdit){
				cont=WBTB.beforHtml +WBTB_BeforTempSave(d.body.parentElement.outerHTML);
			}else{
				cont=d.body.innerHTML;
			}
		}
	}
	cont=WBTB_correctUrl(cont);
	cont=cont.replace(/<PARAM NAME="Play" VALUE="0">/ig,"<PARAM NAME=\"Play\" VALUE=\"-1\">");
	if (WBTB.filterScript){
		cont=WBTB_FilterScript(cont);
	}
//	if (cont=="<p>&nbsp;</p>"){
	if (cont=="&nbsp;"){

		cont="";
	}
	document.getElementById(hiddenid).value = cont;
}


//另一个函数， 拷贝数据到hidden

 function  WBTB_CopyData2(hiddenid)   
  {   
  d = WBTB_Composition.document;  
  
  if   (WBTB_bTextMode)   
  {   
  cont=d.body.innerText;   
  }
  else{   
  if   (WBTB.XhtmlEnabled) 
    {   
  try{   
  cont=WBTBXHTML.GetXHTML(d.body)   
  }catch(e){   
  cont=d.body.innerHTML;   
  }   
  }else{   
  cont=d.body.innerHTML;   
  }   
  }   
  cont=WBTB_correctUrl(cont);   
  if   (WBTB.filterScript)   
  cont=WBTB_FilterScript(cont);   
  document.getElementById(hiddenid).value   =   cont;       
//  if   (document.getElementById(hiddenid).value   ==   '<p>&nbsp;</p>')   
  if   (document.getElementById(hiddenid).value   ==   '&nbsp;')   

  {   
  document.getElementById(hiddenid).value   =   '';   
  }   
  }   


/// 清除HtmlArea内容
function WBTB_ClearArea()
{
	WBTB.HtmlArea.value='';
}

/// 取输入内容长度
function WBTB_Length()
{
	WBTB_CopyData(WBTB.Hidden);
	alert("内容字数："+ document.getElementById(WBTB.Hidden).value.length);
}

/* xhtml object
--------------------------*/
var WBTBXHTML=new Object();
WBTBXHTML.GetXHTML=function(node){
	if (window.ActiveXObject)
		this.XML=new ActiveXObject('Msxml2.DOMDocument');
	else{
		this.XML=document.implementation.createDocument('', '', null);
		Node.prototype.__defineGetter__('xml', WBTBXHTML._Node_getXML);
	};
	this.MainNode=this.XML.appendChild(this.XML.createElement( 'XHTML' ));
	this._AppendChildNodes(this.MainNode, node);
	var sXHTML=this.MainNode.xml;
	return sXHTML.substr(7, sXHTML.length - 15);
};
WBTBXHTML._Node_getXML=function(){
	var oSerializer=new XMLSerializer();
	return oSerializer.serializeToString(this);
};
WBTBXHTML._AppendAttribute=function(xmlNode, attributeName, attributeValue){
	var oXmlAtt=this.XML.createAttribute(attributeName);
	if (typeof( attributeValue )=='boolean' && attributeValue == true)
		oXmlAtt.value=attributeName;
	else
		oXmlAtt.value=attributeValue;
	xmlNode.attributes.setNamedItem(oXmlAtt);
};
WBTBXHTML._AppendChildNodes=function(xmlNode, htmlNode){
	var oChildren=htmlNode.childNodes;
	var i=0;
	while (i < oChildren.length){
		i +=this._AppendNode(xmlNode, oChildren[i]);
	};
};
WBTBXHTML._AppendNode=function(xmlNode, htmlNode){
	var iAddedNodes=1;
	switch (htmlNode.nodeType){
		case 1 : var sNodeName=htmlNode.nodeName.toLowerCase();
			var oNode=xmlNode.appendChild(this.XML.createElement( sNodeName ));
			var oAttributes=htmlNode.attributes;
		for (var n=0 ; n < oAttributes.length ; n++){
			var oAttribute=oAttributes[n];
			if (oAttribute.specified)
				this._AppendAttribute(oNode, oAttribute.nodeName.toLowerCase(), oAttribute.nodeValue);
		};
		switch (sNodeName){
			case "script" : case "style" : oNode.appendChild(this.XML.createCDATASection( htmlNode.text ));break;
			case "abbr" : if (document.all){var oNextNode=htmlNode.nextSibling;
				while (true){
					iAddedNodes++;
					if (oNextNode && oNextNode.nodeName !='/ABBR'){
						this._AppendNode(oNode, oNextNode);
						oNextNode=oNextNode.nextSibling;
					}else
						break;
				};
				break;
			};
			case "area" : if (document.all && ! oNode.attributes.getNamedItem( 'coords' )){var sCoords=htmlNode.getAttribute('coords', 2);if (sCoords && sCoords !='0,0,0') this._AppendAttribute(oNode, 'coords', sCoords);};
			case "img" : if (! oNode.attributes.getNamedItem( 'alt' )) this._AppendAttribute(oNode, 'alt', '');
			default : this._AppendChildNodes(oNode, htmlNode);
			break;
		};
		break;
		case 3 : xmlNode.appendChild(this.XML.createTextNode( htmlNode.nodeValue ));break;
		default : xmlNode.appendChild(this.XML.createComment( "Element not supported - Type: " + htmlNode.nodeType + " Name: " + htmlNode.nodeName ));break;
	};
	return iAddedNodes;
};

WBTB.GetXHTML=function(){var bSource=WBTB_bTextMode;
	if (bSource) WBTB_setMode();
	var sXHTML=WBTBXHTML.GetXHTML(WBTB.Frame.document.body);
	if (bSource) WBTB_setMode();
	return sXHTML;
};


function WBTB_help()
{
	if(WBTB.bIsIE5){
		showModalDialog("wbTextBox/help.html", "", "dialogWidth:200px; dialogHeight:180px; status:0; help:0; scroll:0");
	}else{
		window.open("wbTextBox/help.html","helpWin","width=200,height=180");
	}
}


﻿<html>
<head>
<title>替换</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="pop.css">
<script language="javascript" for="Ok" event="onclick">
  window.returnValue = document.getElementById("a").value+"*"+document.getElementById("b").value+"*"+(document.getElementById("c").checked?"1":"0")+"*";
  window.close();
</script>
<script>
function IsDigit()
{
  return ((event.keyCode >= 48) && (event.keyCode <= 57));
}
</script>
</head>

<body bgcolor="menu" topmargin="5" leftmargin="5">

<table width="100%" border="0" cellspacing="3" cellpadding="0" align="center">
<tr><td>
	<fieldset>
	<legend>替换</legend>
	<table>
	<tr>
	<td >
	替换内容: <input type="text" size="23" id="a">
	</td>
	</tr>
	<tr>
	<td>
	替 换 为: <input type="text" size="23" id="b" >
	</td>
	</tr>
	<tr>
	<td>
	<input type="checkbox" id="c" class="cb">
	<label for="c">区分大小写</label>
	</td>
	</tr>
	</table>
	</fieldset>
</td></tr>
<tr><td align=right height=30>
<button id="Ok" type="submit">确定</button>&nbsp; &nbsp;<button onclick="window.close();">取消</button>
</td></tr>
</table>

</BODY>   
</HTML>
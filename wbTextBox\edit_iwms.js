﻿function WBTB_BeforTempSave(str)
{
	// 替换掉<body>里的contentEditable=true
	var re = /(<body[^>]*?) contentEditable=true/ig;
	str = str.replace(re, "$1");
	
	str = str.replace(/&lt;%/ig, "<%");
	str = str.replace(/%&gt;/ig, "%>");
	str = str.replace(/\s*xmlns:asp/ig, "");
	
	// 分类页右侧样式表
	s = /<div class=mframe title=cssLiteral>/ig;
	str = str.replace(s, "<div class='<asp:Literal id=\"cssLiteral\" runat=\"server\"/>'>");
	
	// 图片的urlPrefix
	s = /<div class=prefix><img(.+?)src="(.+?)"(.+?)><\/div>/ig;
	str = str.replace(s, "<img$1src=\"<%=urlPrefix%>$2\"$3");

	// 函数
	s = /<div class=function>(.+?)<\/div>/ig;
	str = str.replace(s, "$1");

	// 相关新闻链接
	s = /<span class=tt><!--script begin ((.|\n)+?) -->.+?<!--script end--><\/span>/ig;
	str = str.replace(s, "$1");

	// 过滤客户脚本
	s = /<!--clientscript begin ((.|\n)+?) -->/ig;
	str = str.replace(s, "$1");


	// 控件及iframe，一个全角空格是插入时不加字符会自动去掉注释
	s = /<div class=control>　?<!--control begin ((.|\n)+?) -->.+?<!--control end--><\/div>/ig;
	str = str.replace(s, "$1");

	// 包含navclass.aspx
	s = /<!--navclass begin-->(.|\n)+?<!--navclass end-->/ig;
	str = str.replace(s, "<!--#include file=\"../inc/navclass.aspx\"-->");

	// 包含foot.inc
	s = /<!--foot begin-->(.|\n)+?<!--foot end-->/ig;
	str = str.replace(s, "<!--#include file=\"../foot.inc\" -->");

	// 包含head.inc
	s = /<!--head begin-->(.|\n)+?<!--head end-->/ig;
	str = str.replace(s, "<!--#include file=\"../head.inc\" -->");

	// 包含html_head.inc
	s = /<!--htmlhead begin-->(.|\n)+?<!--htmlhead end-->/ig;
	str = str.replace(s, "<!--#include file=\"../inc/html_head.inc\"-->");
	
	// title会跑到注释前去，删
	s = /<title>(.|\n)+?<\/title>/ig;
	str = str.replace(s, "");

	// 样式表函数先替换为样式表
	str = str.replace(/<div class=dayhot title=frameCss_((\w|\.)+)/ig, "<div class=\"<%=GetFrameCss($1,\"lframe\")%>\"");
	str = str.replace(/<div class=(\w+) title=frameCss_((\w|\.)+)/ig, "<div class=\"<%=GetFrameCss($2,\"$1\")%>\"");
	str = str.replace(/<span class=tt title=(.+?)>(.+?)<\/span>/ig, "<%=GetTitlePic($1,\"$2\")%>");
	return str;
}

// 替换掉server端代码
function WBTB_BeforTempEdit(str)
{
	var s;
	// 过滤html注释
	str = str.replace(/<!--[^#](.|\n)+?-->/ig, "");
	
	// 样式表函数先替换为样式表,样式表嵌套(lframe下的lframe)会使ie无响应，今日换为dayhot
	str = str.replace(/<div class="<%=GetFrameCss\(style.CssDayHot,"(\w+)"\)%>"/ig, "<div class=\"dayhot\" title=\"frameCss_style.CssDayHot\"");
	str = str.replace(/<div class="<%=GetFrameCss\((.+?),"(\w+)"\)%>"/ig, "<div class=\"$2\" title=\"frameCss_$1\"");
	str = str.replace(/<%=GetTitlePic\((.+?),"(.+?)"\)%>/ig, "<span class='tt' title='$1'>$2</span>");
	
	// 包含html_head.inc
	s = '<!--htmlhead begin-->\r\n'+
		'<title>iwms</title>\r\n'+
		'<meta http-equiv="content-type" content="text/html;charset=gb2312"/>'+
		'<link rel="stylesheet" href="style/iwms/style.css" type="text/css"/>\r\n'+
		'<style type="text/css">.control{height:100%;word-break:break-all;word-wrap:break-word;font-size:14px;background:#EEEEEE;color:red;padding:5px;border:3px dotted red;}</style>'+
		'<style type="text/css">.function{width:100%;word-break;break-all;word-wrap:break-word;font-size:14px;background:#EEEEEE;color:blue;padding:5px;border:3px dotted blue;}</style>'+
//		'<script type="text/javascript" src="inc/flash.js"></script>\r\n'+
//		'<script type="text/javascript">var thumbWidth=120;var thumbHeight=0;var thumbHWidth=thumbWidth;var thumbHHeight=thumbHeight;var urlPrefix = "";</script>\r\n'+
//		'<script type="text/javascript" src="inc/thumbnail.js"></script>'+
		'<!--htmlhead end-->';
	str = str.replace(/<!--#include file="\.\.\/inc\/html\_head\.inc"-->/ig, s);
	
	// 包含head.inc
	s = '<!--head begin-->\r\n'+
		'<div class="twidth"><div contentEditable="false"><table id="sitehead" align="center" cellpadding="0" cellspacing="0" border="0"><tr><td class="l"></td><td class="m">'+
		'<table align="center" width="100%" ><tr><td width="200"><img src="pic/logo.gif"></td>'+
		'<td align="center"></td><td width="100" align="center" style="line-height:120%">'+
		'<a href="#" >加入收藏</a><br/>'+
		'<a href="sitemap.aspx">网站地图</a><br/><a href="search.aspx">网站搜索</a><br/>'+
		 '<a href="chinese.aspx?big5=0">简</a> <a href="chinese.aspx?big5=1">繁</a> <a href="chinese.aspx">默</a></div>'+
		 '</td></tr></table></td><td class="r"></td></tr></table></div><!--head end-->';
	str = str.replace(/<!--#include file="\.\.\/head\.inc" -->/ig, s);

	// 友情链接滚动函数不能用在此替换
	//s += '<script type="text/javascript">function StartRollV(){}\r\nfunction StartRollH(){}</script>'+

	// 包含foot.inc
	s = '<!--foot begin-->\r\n'+
		'<div  contentEditable="false"><table id="footer" border="0" cellpadding="0" cellspacing="0"><tr align="center" style="line-height:130%"><td height="60">'+
		'版权信息'+
	//	'<div class="menuskin" id="popmenu" onmouseover="clearhidemenu();highlightmenu(event,\'on\')" onmouseout="highlightmenu(event,\'off\');dynamichide(event)" style="Z-index:100"></div>'+
	//	'<script type="text/javascript" language="javascript1.2">jsdone=false;</script></td></tr></table></div>'+
		'</div><!--foot end-->';
	str = str.replace(/<!--#include file="\.\.\/foot\.inc" -->/ig, s);
	
	// 包含navclass.aspx
	s = '<!--navclass begin-->'+
	//	'<script type="text/javascript" language="javascript1.2" src="inc/popmenu.js"></script>'+
		'<span contentEditable="false"><a href=#>分类列表</a></span>'+
		 '<!--navclass end-->';
	str = str.replace(/<!--#include file="\.\.\/inc\/navclass\.aspx"-->/ig, s);
	
	// 所有分类新闻控件
	s = /(<%BindAllNew\(\);%>\s*<asp:DataList (.|\n)+?<\/asp:DataList>)/ig;
	str = str.replace(s, "<div class=control><!--control begin $1 -->首页一级分类新闻列表控件显示<!--control end--></div>");
	
	// 网站导航控件
	s = /(<%BindNavList\(\);%>\s*<asp:DataList (.|\n)+<\/asp:DataList>)/ig;
	str = str.replace(s, "<div class=control><!--control begin $1 -->网站导航控件<!--control end--></div>");
	
	// 左侧网站导航控件
	s = /(<%BindLeftNav\(.+?\);%>\s*<asp:DataList (.|\n)+?<\/asp:DataList>)/ig;
	str = str.replace(s, "<div class=control><!--control begin $1 -->左侧网站导航控件<!--control end--></div>");
	
	// 分类绑定新闻控件
	s = /(<asp:DataList id="dlNews"(.|\n)+?<\/asp:DataList>)/ig;
	str = str.replace(s, "<div class=control><!--control begin $1 -->分类绑定新闻控件<!--control end--></div>");
	
	// 绑定评论控件
	s = /(<%BindRemarks\(5\);%>\s*<asp:Repeater (.|\n)+?<\/asp:Repeater>)/ig;
	str = str.replace(s, "<div class=control><!--control begin $1 -->绑定评论控件<!--control end--></div>");
	
	// 绑定新闻分页列表控件
	s = /(<asp:DataList id="dlPages"(.|\n)+?<\/asp:DataList>)/ig;
	str = str.replace(s, "<div class=control><!--control begin $1 -->新闻分页列表控件<!--control end--></div>");
	
	// 登陆iframe
	s = /(<span id="news_login"><\/span>\s*<iframe (.|\n)+?<\/iframe>)(?! -->)/ig;
	str = str.replace(s, "<div class=control><!--control begin $1 -->会员登陆内容<!--control end--></div>");
	
	// 统计点击iframe
	s = /(<iframe (.|\n)+?frm_hit.aspx(.|\n)+?<\/iframe>)(?! -->)/ig;
	str = str.replace(s, "<div class=control><!--control begin $1 -->统计点击数<!--control end--></div>");
	
	// 表情图片
	s = /(<input\s+type="radio"\s+name="face"(.|\n)+?pic\/face18.gif"\s+alt=""\/>)/ig;
	str = str.replace(s, "<div class=control><!--control begin $1 -->评论表情<!--control end--></div>");

	// 分页标题
	s = /(<%if\(subTitle!=""\){%>(.|\n)+?<%}%>)/ig
	str = str.replace(s, "<div class=control><!--control begin $1 -->分页标题<!--control end--></div>");
	
	// 相关新闻链接
	//<%if (Nkey=="")
	s = /(<%if \(Nkey==""\)(.|\n)+?%>(?! -->))/ig;
	str = str.replace(s, "<span class='tt'><!--script begin $1 -->相关新闻<!--script end--></span>");
	
	// 输出文本函数
	s = /(<%=\w+\(.*?\)%>)/ig;
	str = str.replace(s, "<div class=function>$1</div>");
	
	// 过滤客户脚本中html注释
	s = /(<script[^>]*?>)\s*<!--((.|\n)*?)-->\s*(<\/script>)/ig;
	str = str.replace(s, "$1$2$4");
	
	// 过滤客户脚本
	s = /(<script(\s+(type|language)=[^ ]+?)*?>(.|\n)*?<\/script>)/ig;
	str = str.replace(s, "<!--clientscript begin $1 -->");
	
	// 相关新闻函数<%RelateNews(5,40,true,2);%>
	s = /(<%\w+\(.*?\);%>)/ig;
	str = str.replace(s, "<div class=function>$1</div>");
	
	// 分类页右侧样式表
	s = /<div class='<asp:Literal id="cssLiteral" runat="server"\/>'>/ig;
	str = str.replace(s, "<div class=mframe title=cssLiteral>");
	
	// 加urlPrefix的图片
	s = /<img(.+?)src="<%=urlPrefix%>(.+?)"(.+?)>/ig
	str = str.replace(s, "<div class=prefix><img$1src=\"$2\" $3></div>");
	
	// 侧栏广告位
	s = /(<%=config\.SideAd%>)/ig
	str = str.replace(s, "<div class=control><!--control begin $1 -->侧栏广告位<!--control end--></div>");
	
	// 内容前广告位
	s = /(<%=config\.TopAd%>)/ig
	str = str.replace(s, "<div class=control><!--control begin $1 -->内容前广告位<!--control end--></div>");
	
	// 内容后广告位
	s = /(<%=config\.FootAd%>)/ig
	str = str.replace(s, "<div class=control><!--control begin $1 -->内容后广告位<!--control end--></div>");
	
	str = str.replace(/<%/ig, "&lt;%");
//	str = str.replace(/<%#/ig, "&lt;%#");
	str = str.replace(/%>/ig, "%&gt;");

	return str;
}


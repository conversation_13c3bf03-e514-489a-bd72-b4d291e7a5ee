﻿using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Data.SqlClient;

public partial class SeeTask : System.Web.UI.Page
{
    string pKey = "id";
    readonly string ConnStr = InputParams.connectionStr;

    public EnumField[][] EnumField()
    {
        EnumField[] enum0 ={new EnumField(0, "非目标客户"), new EnumField(1, "号码待确认"), new EnumField(2, "号码确认，无联系人"), 
                             new EnumField(3, "已有联系人"), new EnumField(4, "已发资料"), new EnumField(5, "已拜访过"),
                             new EnumField(6, "已看过供应商"), new EnumField(7, "试吃过,未成功"),
                             new EnumField(8, "正在试用中"), new EnumField(9, "正式合作中")};

        EnumField[] enum1 ={new EnumField(0, "无执照资质"), new EnumField(1, "普通执照资质"), new EnumField(2, "盒饭"), 
                             new EnumField(3, "桶饭"), new EnumField(4, "盒饭 + 桶饭")
                            };
        EnumField[][] efTs ={ enum0, enum1 };
        return efTs;
    }

    public Table[] Tables()
    {
        string tblName1 = "Cooperation";

        Field fld0 = new Field(0, "firm_name", "公司名称", 0, EnumFieldType.charType, 0, 0, "", 0, "");
        Field fld1 = new Field(1, "area", "区域", 0, EnumFieldType.enumType, 0, 0, "", 0, "");
        Field fld2 = new Field(2, "telephone", "电话", 0, EnumFieldType.charType, 0, 0, "", 0, "");
        Field fld3 = new Field(3, "linkman", "联系人", 0, EnumFieldType.charType, 0, 0, "", 0, "");
        Field fld4 = new Field(4, "cooperate_state", "合作状态", 0, EnumFieldType.enumType, 0, 0, "", 0, "");
        Field fld5 = new Field(5, "last_follow_time", "最后联系时间", 0, EnumFieldType.dateType, EnumSortType.DESC, 0, "", 0, "");
        Field fld6 = new Field(6, "profit", "返利", 0, EnumFieldType.numberType, 0, 0, "", 0, "");
        Field fld7 = new Field(7, "business_mode", "营业资质", 0, EnumFieldType.enumType, 0, 1, "", 0, "");
        Field fld8 = new Field(8, "create_time", "创建时间", 0, EnumFieldType.dateType, EnumSortType.DESC, 0, "", 0, "");
        Field fld9 = new Field(9, "creator_id", "创建者", 0, EnumFieldType.charType, 0, 0, "", 0, "");
        Field fld10 = new Field(10, "executor_id", "跟踪者", 0, EnumFieldType.charType, 0, 0, "", 0, "");
        Field fld11 = new Field(11, "address", "地址", 0, EnumFieldType.charType, 0, 0, "", 0, "");
        Field fld12 = new Field(12, "remark", "备注", 0, EnumFieldType.charType, 0, 0, "", 0, "");
  

        Field[] flds ={ fld0, fld1, fld2, fld3, fld4, fld5, fld6, fld7, fld8, fld9, fld10,fld11,fld12};
        Join jon1 = new Join();
        Table table1 = new Table(tblName1, jon1, flds);

        Table[] tables ={ table1 };
        return tables; ;
    }

    protected void Page_Load(object sender, EventArgs e)
    {
        int TaskID = int.Parse(Request.QueryString["TaskID"].ToString());
        string strSql = GetSqlStr(TaskID);
        GenerateSeeMould(strSql);

        //if (Request.QueryString["message"] != null)
        //{
        //    ClientScript.RegisterClientScriptBlock(this.GetType(), "info", "<script language=\"javascript\" type=\"text/javascript\">alert(\"添加成功！\");window.location.href='See.aspx';</script>");

        //}
    }

    protected void hlkReturn_Click(object sender, EventArgs e)
    {
        Response.Redirect("See.aspx?id=2");
    }

    /// <summary>
    /// 生成查看模板
    /// </summary>
    protected void GenerateSeeMould(string strSql)
    {
        int flag = 0;
        placeHolder1.Controls.Clear();

        SqlConnection conn = new SqlConnection(ConnStr);
        conn.Open();
        SqlDataAdapter da = new SqlDataAdapter(strSql, conn);
        DataSet ds = new DataSet();
        da.Fill(ds);
        DataTable dt = ds.Tables[0];
        DataRow dr = dt.Rows[0];

        EnumField[][] efTs = EnumField();
        Table[] tables = Tables();

        #region forTable
        int colIndex = -1;
        for (int tableIndex = 0; tableIndex < tables.Length; tableIndex++)
        {
            #region forField
            for (int fieldIndex = 0; fieldIndex < tables[tableIndex].fields.Length; fieldIndex++)
            {
                colIndex++;
                Literal ltlBrs = new Literal();
                ltlBrs.Text = "<br/>";
                placeHolder1.Controls.Add(ltlBrs);


                Label lbl = new Label();
                lbl.Text = tables[tableIndex].fields[fieldIndex].fieldShowName + "：";
                lbl.ForeColor = System.Drawing.ColorTranslator.FromHtml(InputParams.lblFieldShowNameColor);
                placeHolder1.Controls.Add(lbl);

                #region switch
                string strFldName = tables[tableIndex].fields[fieldIndex].fieldName;

                switch (tables[tableIndex].fields[fieldIndex].fieldType)
                {

                    case EnumFieldType.numberType:
                        if (tables[tableIndex].fields[fieldIndex].fieldName == "PlanSpendTime")
                        {
                            int spendTime = int.Parse(dr[colIndex].ToString());
                            //以分钟为单位
                            //int spendYear = spendTime / (24 * 60 * 365);
                            //int spendMonth = (spendTime % (24 * 60 * 365)) / (24 * 60 * 30);
                            //int spendDay = (((spendTime % (24 * 60 * 365)) % (24 * 60 * 30)) / (24 * 60));
                            //int spendHour = (((spendTime % (24 * 60 * 365)) % (24 * 60 * 30)) % (24 * 60)) / 60;
                            //int spendMinute = ((((spendTime % (24 * 60 * 365)) % (24 * 60 * 30)) % (24 * 60))) % 60;

                            int spendHour = spendTime / 60;
                            int spendMinute = spendTime % 60;

                            string strSpendTime = "";
                            //if (spendYear != 0)
                            //{
                            //    strSpendTime += spendYear + "年";
                            //}
                            //if (spendMonth != 0)
                            //{
                            //    strSpendTime += spendMonth + "个月";

                            //}
                            //if (spendDay != 0)
                            //{
                            //    strSpendTime += spendDay + "天";

                            //}
                            if (spendHour != 0)
                            {
                                strSpendTime += spendHour + "小时";

                            }
                            if (spendMinute != 0)
                            {
                                strSpendTime += spendMinute + "分钟";
                            }

                            Literal ltlN = new Literal();
                            ltlN.Text = strSpendTime;
                            placeHolder1.Controls.Add(ltlN);

                        }
                        else
                        {
                            Literal ltlN = new Literal();
                            ltlN.Text = dr[colIndex].ToString();
                            placeHolder1.Controls.Add(ltlN);
                        }
                        continue;

                    case EnumFieldType.boolType:

                        string text = "";
                        if (flag!= 2)
                        {
                            if (int.Parse(dr[colIndex].ToString()) == 1)
                                text = "是";
                            else
                                text = "否";
                            flag++;

                        }
                        else
                        {
                            if (int.Parse(dr[colIndex].ToString()) == 1)
                                text = "男";
                            else
                                text = "女";
                            flag = 1;
                        }

                        Literal ltlB = new Literal();
                        ltlB.Text = text;
                        placeHolder1.Controls.Add(ltlB);
                        continue;

                    //case EnumFieldType.picType:

                    //    continue;

                    case EnumFieldType.enumType:
                        int enumTag = tables[tableIndex].fields[fieldIndex].enumTag;
                        int enumItem = int.Parse(dr[colIndex].ToString());
                        Literal ltlE = new Literal();

                        if (tables[tableIndex].fields[fieldIndex].fieldName == "area")
                            ltlE.Text = (new Common()).GetTown(enumItem);
                        else
                        ltlE.Text = efTs[enumTag][enumItem].itemDetail;
                        placeHolder1.Controls.Add(ltlE);
                        continue;


                    case EnumFieldType.longcharType:

                        if (dr[colIndex].ToString() != "")
                        {
                            Literal ltlBr1 = new Literal();
                            ltlBr1.Text = "<br/>";
                            placeHolder1.Controls.Add(ltlBr1);


                            Label lblD1 = new Label();
                            lblD1.Text = "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" + dr[colIndex].ToString();
                            lblD1.ForeColor = System.Drawing.ColorTranslator.FromHtml("#0000FF");
                            //lblD1.BackColor = System.Drawing.ColorTranslator.FromHtml("#F6FFFF");
                            //lblD1.Height = 50;
                            lblD1.Width = 700;
                            placeHolder1.Controls.Add(lblD1);

                            ////生成换行符
                            //Literal ltlBr2 = new Literal();
                            //ltlBr2.Text = "<br/>";
                            //placeHolder1.Controls.Add(ltlBr2);
                        }
                        continue;

                    //case EnumFieldType.dateType:
                    //    continue;
                    //case EnumFieldType.charType:
                    //    continue;

                    default:

                        if (tables[tableIndex].fields[fieldIndex].fieldName == "creator_id" || tables[tableIndex].fields[fieldIndex].fieldName == "executor_id")
                        {
                            Literal ltlName = new Literal();
                            ltlName.Text = (new Common()).GetNickname(int.Parse(dr[colIndex].ToString()));
                            placeHolder1.Controls.Add(ltlName);

                        }
                        else
                        if ((dr[colIndex] != null) && (dr[colIndex].ToString() != ""))
                        {
                            Literal ltlT = new Literal();
                            ltlT.Text = dr[colIndex].ToString();
                            placeHolder1.Controls.Add(ltlT);
                        }
                        continue;

                }

                #endregion switch
            }
            #endregion forField
        }
        #endregion  forTable

        //添加追加记录

        Literal ltlRecord = new Literal();

        ltlRecord.Text = "<br> <p style='color:#871F78;'>追加记录：</p><br>" + (new Common()).GetRecord(int.Parse(Request.QueryString["TaskID"].ToString()));

        placeHolder1.Controls.Add(ltlRecord);

    }

    /// <summary>
    /// 获得Sql字符串
    /// </summary>
    /// <returns></returns>
    protected string GetSqlStr(int TaskID)
    {
        EnumField[][] efTs = EnumField();
        Table[] tables = Tables();

        string strSelect = "SELECT  ";
        string strFrom = " FROM ";
        string strWhere = " WHERE " + pKey + "=" + TaskID;

        #region forTable
        for (int tableIndex = 0; tableIndex < tables.Length; tableIndex++)
        {
            string strTblName = tables[tableIndex].tableName;

            if (tableIndex == 0)
            {
                strFrom += strTblName;
            }
            else
            {
                strFrom += "," + strTblName;
            }
            if (tables[tableIndex].join.joinField != null)
            {
                strWhere += " AND " + strTblName + "." + tables[tableIndex].join.joinField + "=" + tables[tableIndex].join.joinRTable + "." + tables[tableIndex].join.joinRField;
            }

            #region forField
            for (int fieldIndex = 0; fieldIndex < tables[tableIndex].fields.Length; fieldIndex++)
            {
                if (!((tableIndex == 0) && (fieldIndex == 0)))
                {
                    strSelect += ",";
                }
                strSelect += tables[tableIndex].tableName + "." + tables[tableIndex].fields[fieldIndex].fieldName;
            }
            #endregion forField
        }
        #endregion forTable
        string strSql = strSelect + strFrom + strWhere;
        return strSql;
    }
}

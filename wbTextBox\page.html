﻿<html>
<head>
<title>插入分页</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="pop.css">

<script language="javascript">

var wbtbWin;
if (window.opener){
	wbtbWin=launchParameters["wbtb"];
}else{
	wbtbWin=dialogArguments;
}

// 本窗口返回值
function ReturnValue(){
	var sTitle=document.getElementById("d_title").value;
	var sHtml
	if (sTitle=="")
	{
		sHtml="<br/>[dvnews_page]</br>";
	}else{
		sTitle = sTitle.replace("[","「").replace("]","」");
		sHtml = "</br>[dvnews_page="+ sTitle +"]</br>";
	}
	wbtbWin.WBTB_InsertHtml(sHtml);

//	window.returnValue = null;
	window.close();
}

</script>

<body bgcolor="menu" topmargin="5" leftmargin="5">

<table width="100%" border="0" cellpadding="0" cellspacing="0" align="center" >
<tr style="padding:2 0">
	<td>
	<fieldset>
	<legend>插入分页</legend>
	<table border=0 cellpadding=3 cellspacing=0>
		<tr><td height="5"></td></tr>
		<tr>
			<td>
			下一页标题: <br><input type="text" id="d_title" style="width:230px" size="30" value="" >
			</td>
		</tr>
		</table>
	</fieldset>
	</td>
</tr>
<tr style="padding:2 0"><td align="right">
	<button id="Ok" onclick="ReturnValue();">  确定  </button>&nbsp;&nbsp;
	<button onclick="window.close();">  取消  </button>
</td></tr>
</table>

</body>
</html>
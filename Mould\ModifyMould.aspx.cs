﻿using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Data.SqlClient;

public partial class ModifyMould : System.Web.UI.Page
{
    string[] strUpdates;
    int modifyCount;
    int[] ckbsModify;
    int id;

    //iHM为0时，不生成时分控件，为1时，生成时分控件
    //int iHM = 0; 
    int iHM = 1;


    public const string AETable = "Tasks";
    public const string AEKey = "TaskID";

    readonly string ConnStr = InputParams.connectionStr;

    public EnumField[][] EnumField()
    {
        EnumField[] enum0 ={ new EnumField(0, "日任务"), new EnumField(1, "周任务"), new EnumField(2, "月任务"), new EnumField(3, "年任务") };
        EnumField[] enum1 ={ new EnumField(0, "已存在"), new EnumField(1, "待接受"), new EnumField(2, "待执行"), new EnumField(3, "执行中"), new EnumField(4, "延期执行中"), new EnumField(5, "待汇报"), new EnumField(6, "待审核"), new EnumField(7, "审核中"), new EnumField(8, "已关闭") };
        EnumField[][] efTs ={ enum0, enum1 };
        return efTs;
    }

    public Field[] InitFields()
    {
        Field fld0 = new Field(0, "TaskName", "任务名称", 0, EnumFieldType.charType, 0, 0, "", 0, "");
        Field fld1 = new Field(1, "PlanStartTime", "计划开始时间", 0, EnumFieldType.dateType, 0, 0, "", 0, "");
        Field fld2 = new Field(2, "PlanOverTime", "计划结束时间", 0, EnumFieldType.dateType, 0, 0, "", 0, "");
        Field fld3 = new Field(3, "Type", "任务类型", 0, EnumFieldType.enumType, EnumSortType.ASC, 0, "", 0, "");
        Field fld4 = new Field(4, "State", "任务状态", 0, EnumFieldType.enumType, 0, 1, "", 0, "");
        Field fld5 = new Field(5, "ActualStartTime", "实际开始时间", 0, EnumFieldType.dateType, 0, 0, "", 0, "");
        Field fld6 = new Field(6, "ActualOverTime", "时间结束时间", 0, EnumFieldType.dateType, 0, 0, "", 0, "");
        Field fld7 = new Field(7, "Description", "任务描述", 0, EnumFieldType.longcharType, 0, 0, "", 0, "");
        Field[] flds ={ fld0, fld1, fld2, fld3, fld4, fld5, fld6, fld7 };
        return flds;
    }



    protected void Page_Load(object sender, EventArgs e)
    {
        ckbsModify = (int[])Session["ckbsModify"];
        modifyCount = ckbsModify.Length;
        if (Request.QueryString["id"] != null)
        {
            try
            {
                id = int.Parse(Request.QueryString["id"]);
                GeneModifyMould();
            }
            catch
            {
            }
        }
        else
        {
            GeneModifyMould();
        }
    }
    protected void btnModify_Click(object sender, EventArgs e)
    {
        GetUpdateStr();
        try
        {
            Update();
            ClientScript.RegisterClientScriptBlock(this.GetType(), "info", "<script language=\"javascript\" type=\"text/javascript\">alert(\"修改成功！\");window.location.href='MyExecutedTasks.aspx';</script>");
        }
        catch
        {
            ClientScript.RegisterClientScriptBlock(this.GetType(), "info", "<script language=\"javascript\" type=\"text/javascript\">alert(\"修改失败，请重试\");window.location.href='MyExecutedTasks.aspx';</script>");
        }
    }



    protected void lbt_Command(object sender, CommandEventArgs e)
    {
        Field[] fields = InitFields();
        EnumField[][] efTs = EnumField();
        string linkPage = e.CommandArgument.ToString();

        for (int addIndex = 0; addIndex < modifyCount; addIndex++)
        {
            PlaceHolder pHolder = (PlaceHolder)div1.FindControl("placeHolder" + addIndex);
            for (int fieldIndex = 0; fieldIndex < fields.Length; fieldIndex++)
            {
                string strFldName = fields[fieldIndex].fieldName + addIndex;
                switch (fields[fieldIndex].fieldType)
                {
                    case EnumFieldType.enumType:
                        string str1 = "ddl" + strFldName;
                        DropDownList ddl = (DropDownList)pHolder.FindControl(str1);
                        Session[str1] = ddl.Text;
                        continue;

                    case EnumFieldType.boolType:
                        string str2 = "rb" + strFldName + "1";
                        if (((RadioButton)pHolder.FindControl(str2)).Checked == true)
                        {
                            Session[str2] = 1;
                        }
                        else
                        {
                            Session[str2] = 0;
                        }
                        continue;

                    case EnumFieldType.dateType:
                        string str3 = "tbx" + strFldName;
                        Session[str3] = ((TextBox)(pHolder.FindControl(str3))).Text.Trim().ToString();
                        string str3H = "ddl" + strFldName + "H";
                        string str3M = "ddl" + strFldName + "M";
                        Session[str3H] = ((DropDownList)(pHolder.FindControl(str3H))).Text.Trim().ToString();
                        Session[str3M] = ((DropDownList)(pHolder.FindControl(str3M))).Text.Trim().ToString();
                        continue;

                    default:
                        string str4 = "tbx" + strFldName;
                        Session[str4] = ((TextBox)(pHolder.FindControl(str4))).Text.Trim().ToString();
                        continue;
                }
            }
        }
        Session["supPage"] = "ModifyTasks.aspx";
        string strDic = linkPage + "?tbxName=" + e.CommandName;
        Response.Redirect(strDic);
    }


    /// <summary>
    /// 生成修改模板
    /// </summary>
    /// <param name="placeHolder"></param>
    /// <param name="fields"></param>
    public void GeneModifyMould()
    {
        div1.Controls.Clear();
        Field[] fields = InitFields();
        EnumField[][] efTs = EnumField();
        int arrLen = ckbsModify.Length;
        strUpdates = new string[arrLen];

        string strStart = "SELECT ";
        for (int fieldIndex = 0; fieldIndex < fields.Length; fieldIndex++)
        {
            strStart += fields[fieldIndex].fieldName;
            if (fieldIndex != fields.Length - 1)
            {
                strStart += ",";
            }
        }
        strStart += " FROM " + AETable + " WHERE " + AEKey + "=";
        SqlConnection conn = new SqlConnection(ConnStr);
        if (conn.State.ToString() == "Closed")
        {
            conn.Open();
        }


        for (int modifyIndex = 0; modifyIndex < ckbsModify.Length; modifyIndex++)
        {
            string str = strStart + ckbsModify[modifyIndex];
            SqlDataAdapter da = new SqlDataAdapter(str, conn);
            DataSet ds = new DataSet();
            da.Fill(ds);
            DataTable dt = ds.Tables[0];
            DataRow dr = dt.Rows[0];

            PlaceHolder placeHolder = new PlaceHolder();
            placeHolder.ID = "placeHolder" + modifyIndex;
            div1.Controls.Add(placeHolder);
            PlaceHolder pHolder = (PlaceHolder)div1.FindControl("placeHolder" + modifyIndex);
            if (modifyIndex != 0)
            {
                Literal ltlHR = new Literal();
                ltlHR.Text = "<HR/>";
                placeHolder.Controls.Add(ltlHR);
            }

            Literal ltlTag = new Literal();
            ltlTag.Text = AEKey + "：" + ckbsModify[modifyIndex];
            placeHolder.Controls.Add(ltlTag);

            for (int fieldIndex = 0; fieldIndex < fields.Length; fieldIndex++)
            {
                //生成换行符
                Label lbl1 = new Label();
                lbl1.Text = "<br/>";
                pHolder.Controls.Add(lbl1);

                //生成字段标签，即显示名称
                Label lblTag = new Label();
                lblTag.Text = fields[fieldIndex].fieldShowName + "：" + " ";
                lblTag.ForeColor = System.Drawing.ColorTranslator.FromHtml(InputParams.lblFieldShowNameColor);
                pHolder.Controls.Add(lblTag);

                string strFldName = fields[fieldIndex].fieldName + modifyIndex;
                switch (fields[fieldIndex].fieldType)
                {
                    case EnumFieldType.charType:
                        TextBox tbx1 = new TextBox();
                        tbx1.ID = "tbx" + strFldName;
                        string str1 = "tbx" + strFldName;
                        if (id == 1)
                        {
                            tbx1.Text = Session[str1].ToString();
                        }
                        else
                        {
                            tbx1.Text = dr[fieldIndex].ToString();
                        }

                        tbx1.Width = InputParams.tbxCharLength;
                        tbx1.Height = InputParams.tbxHeight;
                        tbx1.TextMode = TextBoxMode.MultiLine;
                        tbx1.Wrap = true;
                        tbx1.Style.Add("overflow", "hidden");
                        pHolder.Controls.Add(tbx1);
                        ((TextBox)pHolder.FindControl("tbx" + strFldName)).Attributes.Add("onMouseOver", "this.style.backgroundColor='" + InputParams.tbxOnmouseoverColor + "';");
                        ((TextBox)pHolder.FindControl("tbx" + strFldName)).Attributes.Add("onMouseOut", "this.style.backgroundColor='" + InputParams.tbxOnmouseoutColor + "';");
                        continue;

                    case EnumFieldType.longcharType:
                        TextBox tbx2 = new TextBox();
                        tbx2.ID = "tbx" + strFldName;
                        string str2 = "tbx" + strFldName;
                        if (id == 1)
                        {
                            tbx2.Text = Session[str2].ToString();
                        }
                        else
                        {
                            tbx2.Text = dr[fieldIndex].ToString();
                        }
                        tbx2.Width = InputParams.tbxLongCharLength;
                        tbx2.Height = InputParams.tbxLongCharHeight;
                        tbx2.TextMode = TextBoxMode.MultiLine;
                        //tbx2.Wrap = true;
                        //tbx2.Style.Add("overflow", "hidden");
                        pHolder.Controls.Add(tbx2);
                        ((TextBox)pHolder.FindControl("tbx" + strFldName)).Attributes.Add("onMouseOver", "this.style.backgroundColor='" + InputParams.tbxOnmouseoverColor + "';");
                        ((TextBox)pHolder.FindControl("tbx" + strFldName)).Attributes.Add("onMouseOut", "this.style.backgroundColor='" + InputParams.tbxOnmouseoutColor + "';");
                        continue;

                    case EnumFieldType.numberType:
                        TextBox tbx3 = new TextBox();
                        tbx3.ID = "tbx" + strFldName;
                        string str3 = "tbx" + strFldName;
                        if (id == 1)
                        {
                            tbx3.Text = Session[str3].ToString();
                        }
                        else
                        {
                            //tbx3.Text = dr[fieldIndex].ToString();
                            tbx3.Text = "";
                        }
                        tbx3.Width = InputParams.tbxNumberLength;
                        tbx3.Height = InputParams.tbxHeight;
                        pHolder.Controls.Add(tbx3);

                        ((TextBox)pHolder.FindControl("tbx" + strFldName)).Attributes.Add("onMouseOver", "this.style.backgroundColor='" + InputParams.tbxOnmouseoverColor + "';");
                        ((TextBox)pHolder.FindControl("tbx" + strFldName)).Attributes.Add("onMouseOut", "this.style.backgroundColor='" + InputParams.tbxOnmouseoutColor + "';");
                        if (fields[fieldIndex].linkPage.ToString() != "")
                        {
                            LinkButton lbt = new LinkButton();
                            lbt.Text = "  点此查找";
                            lbt.ID = "lbt" + strFldName;
                            lbt.Command += new CommandEventHandler(lbt_Command);
                            lbt.CommandName = "tbx" + strFldName;
                            lbt.CommandArgument = fields[fieldIndex].linkPage.ToString();
                            pHolder.Controls.Add(lbt);
                        }
                        continue;

                    case EnumFieldType.dateType:
                        TextBox tbx4 = new TextBox();
                        tbx4.ID = "tbx" + strFldName;
                        string str4 = "tbx" + strFldName;


                        tbx4.Width = InputParams.tbxDateLength;
                        tbx4.Height = InputParams.tbxHeight;
                        if (id == 1)
                        {
                            tbx4.Text = Session[str4].ToString();

                        }
                        else
                        {
                            try
                            {
                                tbx4.Text = DateTime.Parse(dr[fieldIndex].ToString()).ToShortDateString();
                            }
                            catch
                            {
                                tbx4.Text = DateTime.Now.ToShortDateString();
                            }
                        }
                        pHolder.Controls.Add(tbx4);
                        ((TextBox)pHolder.FindControl("tbx" + strFldName)).Attributes.Add("onClick", "javascript:calendar()");
                        ((TextBox)pHolder.FindControl("tbx" + strFldName)).Attributes.Add("onClick", "javascript:calendar()");


                        string str4H = "ddl" + strFldName + "H";
                        string str4M = "ddl" + strFldName + "M";
                        if (iHM == 1)
                        {
                            Literal ltlSpace1 = new Literal();
                            ltlSpace1.Text = "&nbsp;";
                            placeHolder.Controls.Add(ltlSpace1);

                            DropDownList ddlH = new DropDownList();
                            ddlH.ID = "ddl" + strFldName + "H";
                            ddlH.Width = InputParams.ddlTimeWidth;
                            for (int iH = 0; iH < 24; iH++)
                            {
                                ListItem li = new ListItem();
                                li.Text = iH.ToString();
                                li.Value = iH.ToString();
                                if ((id == 1) && (Session[str4H] != null) && (int.Parse(Session[str4H].ToString()) == iH))
                                {
                                    li.Selected = true;

                                }
                                ddlH.Items.Add(li);
                            }

                            placeHolder.Controls.Add(ddlH);

                            Label lblH = new Label();
                            lblH.Text = "时";
                            placeHolder.Controls.Add(lblH);

                            DropDownList ddlM = new DropDownList();
                            ddlM.ID = "ddl" + strFldName + "M";
                            ddlM.Width = InputParams.ddlTimeWidth;

                            for (int iM =0; iM < 60; iM++)
                            {
                                ListItem li = new ListItem();
                                li.Text = iM.ToString();
                                li.Value = iM.ToString();
                                if ((id == 1) && (Session[str4M] != null) && (int.Parse(Session[str4M].ToString()) == iM))
                                {
                                    li.Selected = true;
                                }
                                ddlM.Items.Add(li);
                            }

                            placeHolder.Controls.Add(ddlM);

                            Label lblM = new Label();
                            lblM.Text = "分";
                            placeHolder.Controls.Add(lblM);
                        }
                        continue;

                    case EnumFieldType.boolType:
                        RadioButton rbY = new RadioButton();
                        RadioButton rbN = new RadioButton();

                        rbY.ID = "rb" + strFldName + "1";
                        string strY = "rb" + strFldName + "1";
                        rbY.GroupName = "gn" + strFldName;
                        rbY.Text = "是";
                        rbY.ForeColor = System.Drawing.ColorTranslator.FromHtml(InputParams.rbYNColor);
                        pHolder.Controls.Add(rbY);

                        rbN.ID = "rb" + strFldName + "2";
                        string strN = "rb" + strFldName + "2";
                        rbN.GroupName = "gn" + strFldName;
                        rbN.Text = "否";
                        rbN.ForeColor = System.Drawing.ColorTranslator.FromHtml(InputParams.rbYNColor);
                        pHolder.Controls.Add(rbN);

                        if (id == 1)
                        {
                            if (Session[strY].ToString() != null)
                            {
                                if (Session[strY].ToString() == "1")
                                {
                                    ((RadioButton)pHolder.FindControl(strY)).Checked = true;
                                }
                                else
                                {
                                    ((RadioButton)pHolder.FindControl(strN)).Checked = true;

                                }
                            }
                        }
                        continue;

                    case EnumFieldType.picType:
                        TextBox tbx5 = new TextBox();
                        tbx5.ID = "tbx" + strFldName;
                        string str5 = "tbx" + strFldName;
                        if (id == 1)
                        {
                            tbx5.Text = Session[str5].ToString();
                        }
                        else
                        {
                            tbx5.Text = dr[fieldIndex].ToString();
                        }
                        tbx5.Width = InputParams.tbxCharLength;
                        tbx5.Height = InputParams.tbxHeight;
                        tbx5.TextMode = TextBoxMode.MultiLine;
                        tbx5.Wrap = true;
                        tbx5.Style.Add("overflow", "hidden");
                        pHolder.Controls.Add(tbx5);
                        ((TextBox)pHolder.FindControl("tbx" + strFldName)).Attributes.Add("onMouseOver", "this.style.backgroundColor='" + InputParams.tbxOnmouseoverColor + "';");
                        ((TextBox)pHolder.FindControl("tbx" + strFldName)).Attributes.Add("onMouseOut", "this.style.backgroundColor='" + InputParams.tbxOnmouseoutColor + "';");
                        continue;

                    case EnumFieldType.enumType:
                        DropDownList ddl = new DropDownList();
                        ddl.ID = "ddl" + strFldName;
                        string str6 = "ddl" + strFldName;
                        int enumT = fields[fieldIndex].enumTag;
                        ddl.Width = InputParams.ddlWidth;
                        ddl.AutoPostBack = false;
                        pHolder.Controls.Add(ddl);

                        for (int enumLen = 0; enumLen < efTs[enumT].Length; enumLen++)
                        {
                            ListItem li = new ListItem();
                            li.Value = efTs[enumT][enumLen].enumItem.ToString();
                            li.Text = efTs[enumT][enumLen].itemDetail;
                            if (id == 1)
                            {
                                if ((Session[str6] != null) && (efTs[enumT][enumLen].enumItem.ToString() == Session[str6].ToString()))
                                {
                                    li.Selected = true;
                                }
                            }
                            ddl.Items.Add(li);
                        }
                        ((DropDownList)pHolder.FindControl("ddl" + strFldName)).Attributes.Add("onMouseOver", "this.style.backgroundColor='" + InputParams.tbxOnmouseoverColor + "';");
                        ((DropDownList)pHolder.FindControl("ddl" + strFldName)).Attributes.Add("onMouseOut", "this.style.backgroundColor='" + InputParams.tbxOnmouseoutColor + "';");
                        continue;
                }
            }
        }
        if (conn.State.ToString() == "Open")
        {
            conn.Close();
        }

    }


    /// <summary>
    /// 获得更新记录的SQL Update字符串
    /// </summary>
    /// <param name="placeHolder"></param>
    /// <param name="fields"></param>
    /// <returns></returns>
    public void GetUpdateStr()
    {
        Field[] fields = InitFields();
        EnumField[][] efTs = EnumField();

        for (int modifyIndex = 0; modifyIndex < modifyCount; modifyIndex++)
        {
            string strUpdate = "UPDATE " + AETable + " SET ";
            PlaceHolder pHolder = (PlaceHolder)div1.FindControl("placeHolder" + modifyIndex);

            for (int fieldIndex = 0; fieldIndex < fields.Length; fieldIndex++)
            {
                if (fieldIndex != 0)
                {
                    strUpdate += ",";
                }
                strUpdate += fields[fieldIndex].fieldName + "=";

                string strFldName = fields[fieldIndex].fieldName + modifyIndex;

                switch (fields[fieldIndex].fieldType)
                {

                    case EnumFieldType.numberType:
                        string tbxName2 = "tbx" + strFldName + "2";
                        if (((TextBox)pHolder.FindControl("tbx" + strFldName)).Text != "")
                        {
                            if (Session[tbxName2] != null)
                            {
                                strUpdate += int.Parse(Session[tbxName2].ToString());
                            }
                            else
                            {
                                strUpdate += ((TextBox)pHolder.FindControl("tbx" + strFldName)).Text.Trim();
                            }
                        }
                        else
                        {
                            strUpdate += "' '";
                        }
                        continue;

                    case EnumFieldType.dateType:
                        if (((TextBox)pHolder.FindControl("tbx" + strFldName)).Text != "")
                        {
                            string strDataTime = ((TextBox)pHolder.FindControl("tbx" + strFldName)).Text.Trim().ToString();
                            if (iHM == 1)
                            {
                                DropDownList ddlH = (DropDownList)(pHolder.FindControl("ddl" + strFldName + "H"));
                                DropDownList ddlM = (DropDownList)(pHolder.FindControl("ddl" + strFldName + "M"));
                                string strH = "00";
                                if (ddlH.Text != "")
                                {
                                    strH = ddlH.Text.Trim().ToString();
                                }
                                string strM = "00";
                                if (ddlM.Text != "")
                                {
                                    strM = ddlM.Text.Trim().ToString();
                                }
                                strDataTime += " " + strH + ":" + strM + ":" + "00";
                            }
                            DateTime dt = DateTime.Parse(strDataTime);
                            strUpdate += "'" + dt.ToString() + "'";
                        }
                        continue;

                    case EnumFieldType.enumType:
                        string strText = ((DropDownList)pHolder.FindControl("ddl" + strFldName)).Text.Trim();
                        int iValue = 0;
                        int enumT = fields[fieldIndex].enumTag;
                        for (int enumLen = 0; enumLen < efTs[enumT].Length; enumLen++)
                        {
                            iValue = efTs[enumT][enumLen].enumItem;
                            if (efTs[enumT][enumLen].itemDetail == strText)
                            {
                                break;
                            }
                        }
                        strUpdate += iValue;
                        continue;

                    case EnumFieldType.boolType:

                        if (((RadioButton)pHolder.FindControl("rb" + strFldName + "1")).Checked)
                        {
                            strUpdate += "1";
                        }
                        else
                        {
                            strUpdate += "0";
                        }
                        continue;

                    default:
                        if (((TextBox)pHolder.FindControl("tbx" + strFldName)).Text != "")
                        {
                            strUpdate += "'" + ((TextBox)pHolder.FindControl("tbx" + strFldName)).Text.Trim() + "'";
                        }
                        else
                        {
                            strUpdate += "' '";
                        }
                        continue;
                }
            }
            strUpdate += " WHERE " + AEKey + "=" + ckbsModify[modifyIndex];
            strUpdates[modifyIndex] = strUpdate;
        }
    }

    /// <summary>
    /// 向数据库添加记录
    /// </summary>
    /// <param name="strInsert"></param>
    public void Update()
    {
        SqlConnection conn = new SqlConnection(ConnStr);

        if (conn.State.ToString() == "Closed")
        {
            conn.Open();
        }
        for (int modifyIndex = 0; modifyIndex < ckbsModify.Length; modifyIndex++)
        {
            SqlCommand comm = new SqlCommand(strUpdates[modifyIndex], conn);
            comm.ExecuteNonQuery();
        }
        if (conn.State.ToString() == "Open")
        {
            conn.Close();
        }
    }
}

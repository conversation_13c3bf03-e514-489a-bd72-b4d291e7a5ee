﻿iframe.WBTB_Composition
{
	border: 1px inset;
}
table.WBTB_Body
{
	background-color:gainsboro;
	border:1px solid #CCCCCC;
}
.yToolbar
{
}

.WBTB_Btn
{
	BACKGROUND-COLOR: gainsboro;
	Text-align: center;
	HEIGHT: 28px;
	WIDTH: 28px;
}
.WBTB_Ico
{
	cursor:hand;
}
.WBTB_TBGen
{
	FONT: 8pt verdana,arial,sans-serif;
}

.WBTB_BtnMouseOverUp
{
	BACKGROUND-COLOR: #CCCCCC;
	Text-align: center;
	cursor:hand;
	HEIGHT: 28px;
	WIDTH: 28px;
}

.WBTB_BtnMouseOverDown
{
	BACKGROUND-COLOR: #EEEEEE;
	BORDER: #999999 1px solid;
	Text-align: center;
	cursor:hand;
	HEIGHT: 26px;
	WIDTH: 26px;
}

.WBTB_IcoDown
{
	cursor:hand;
}

.WBTB_IcoDownPressed
{
	cursor:hand;
}

.WBTB_BtnMenu
{
	BACKGROUND-COLOR: gainsboro;
	Text-align: center;
	border: gainsboro 1px solid;
}

.WBTB_BtnMenuMouseOverUp
{
	BACKGROUND-COLOR: gainsboro;
	BORDER-BOTTOM: buttonshadow 1px solid;
	BORDER-LEFT: buttonhighlight 1px solid;
	BORDER-RIGHT: buttonshadow 1px solid;
	BORDER-TOP: buttonhighlight 1px solid;
}
.WBTB_BtnMenuMouseOverDown
{
	BACKGROUND-COLOR: gainsboro;
	BORDER-BOTTOM: buttonhighlight 1px solid;
	BORDER-LEFT: buttonshadow 1px solid;
	BORDER-RIGHT: buttonhighlight 1px solid;
	BORDER-TOP: buttonshadow 1px solid;
}
.WBTB_BtnMenuDown
{
	BACKGROUND-COLOR: gainsboro;
	BORDER-BOTTOM: buttonhighlight 1px solid;
	BORDER-LEFT: buttonshadow 1px solid;
	BORDER-RIGHT: buttonhighlight 1px solid;
	BORDER-TOP: buttonshadow 1px solid;
}

.WBTB_menu
{
	BACKGROUND-COLOR: #cccccc;
	border: #aaaaaa 1px solid;
	width:90%;
}
.WBTB_menu td,.WBTB_menu a
{
	font-size: 12px;text-decoration:none;color:black;
}

td.WBTB_TabOn {
	font: 8pt MS Sans Serif;
	padding:1px 5px;
	border:1px inset;
	background-color: #EEEEEE;
}

td.WBTB_TabOff {
	font: 8pt MS Sans Serif;
	padding:1px 5px;
	border:1px outset;
	cursor:hand;
}

.WBTB_BtnDis
{
	BACKGROUND-COLOR: gainsboro;
	Text-align: center;
	HEIGHT: 28px;
	WIDTH: 28px;
	-moz-opacity:0.4;
	filter:alpha(opacity=40);
}

.WBTB_BtnMenuDis
{
	BACKGROUND-COLOR: gainsboro;
	Text-align: center;
	-moz-opacity:0.4;
	filter:alpha(opacity=40);
}


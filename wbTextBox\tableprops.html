﻿<html>
<head>
<meta http-equiv="Content-Language" content="en-us">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>表格属性</title>
<link rel="stylesheet" href="pop.css" type="text/css">
<script>
function IsDigit()
{
  return ((event.keyCode >= 48) && (event.keyCode <= 57));
}
function selColor(obj, oImg)
{
	var arr = showModalDialog("selcolor.html", "", "dialogWidth:18.5em; dialogHeight:15.5em; status:0; help:0");
	if (arr != null && arr != "")
	{
		obj.value=arr;
		oImg.style.backgroundColor=arr;
	}
}

function checkchange()
{
if  (document.all.widthspecified.checked==true){
    document.all.tablewidth.disabled=false;
	}
else{	
   document.all.tablewidth.disabled=true;
   }
}	 

function colorchange(obj,oImg)
{
	if (obj.value != "")
	{
		try {
		oImg.style.backgroundColor = obj.value;
		}
		catch(e)
		{
			alert("你输入的不是颜色！");
			obj.value = "";
			oImg.style.backgroundColor = "";
		}
	}
}


</script>

<script language="jscript.encode">

function setattributes(){
	var x=window.dialogArguments;
	x.tablewidthspecified="yes";
	if(window.document.moondownerform.widthspecified.checked==false){
		x.tablewidthspecified="no";
	}
	x.tablewidth="";
	if(isNaN(parseInt(window.document.moondownerform.tablewidth.value))==false){x.tablewidth=parseInt(window.document.moondownerform.tablewidth.value);}
	var tempx="";
	if(window.document.moondownerform.widthtype[0].checked==true){tempx="pixels";}
	if(window.document.moondownerform.widthtype[1].checked==true){tempx="percentage";}
	x.tablewidthtype=tempx;
	var alignindex=window.document.moondownerform.align.selectedIndex;
	x.tablealign=window.document.moondownerform.align.options[alignindex].value;
	x.tablebordersize="1";
	if(isNaN(parseInt(window.document.moondownerform.bordersize.value))==false){x.tablebordersize=parseInt(window.document.moondownerform.bordersize.value);}
	x.tablecellpadding="1";
	if(isNaN(parseInt(window.document.moondownerform.cellpadding.value))==false){x.tablecellpadding=parseInt(window.document.moondownerform.cellpadding.value);}
	x.tablecellspacng="1";
	if(isNaN(parseInt(window.document.moondownerform.cellspacing.value))==false){x.tablecellspacing=parseInt(window.document.moondownerform.cellspacing.value);}
	var tablestyleindex=window.document.moondownerform.tablestyle.selectedIndex;
	x.tableclass="";
	if(tablestyleindex!=0){x.tableclass=window.document.moondownerform.tablestyle.options[tablestyleindex].value;}
	x.tablebordercolor=document.all.bordercolor.value;
	x.tablebackgroundcolor=document.all.bgcolor.value;
	x.tableiscancel="no";
	x.tableisinsert="yes";
	x.table_status=0;
	x.tablemoondowneropen=0;window.close();
}

function bye(){
	var x=window.dialogArguments;
	x.table_status=0;
	x.tableiscancel="yes";
	x.tableisinsert="no";
	window.close();
}

function clearme(){
	var x=window.dialogArguments;
	x.table_status=0;
}

function initmoondowner(){
	var x=window.dialogArguments;
	if(x.tablewidthspecified=="yes"){
		window.document.moondownerform.widthspecified.checked=true;
	}else{
		window.document.moondownerform.widthspecified.checked=false;
	}
	var s1=""+x.tablewidth;
	if(s1.indexOf("%")!=-1){
		window.document.moondownerform.widthtype[1].checked=true;
	}
		else{window.document.moondownerform.widthtype[0].checked=true;
	}
	if(x.tablewidth!=""&&isNaN(parseInt(x.tablewidth))!=true){window.document.moondownerform.tablewidth.value=parseInt(x.tablewidth);}else{window.document.moondownerform.tablewidth.value="";}
	var s2=""+x.tablealign;
	s2=s2.toUpperCase();
	if(s2=="LEFT"){
		window.document.moondownerform.align.options[1].selected=true;
	}else if(s2=="CENTER"){
		window.document.moondownerform.align.options[2].selected=true;
	}else if(s2=="RIGHT"){
		window.document.moondownerform.align.options[3].selected=true;
	}else{
		window.document.moondownerform.align.options[0].selected=true;
	}
	if(x.tablebordersize!=""&&x.tablebordersize!=null){
		window.document.moondownerform.bordersize.value=(parseInt(x.tablebordersize));
	}else{
		window.document.moondownerform.bordersize.value="";
	}
	if(x.tablecellpadding!=""&&x.tablecellpadding!=null){
		window.document.moondownerform.cellpadding.value=x.tablecellpadding;
	}else{
		window.document.moondownerform.cellpadding.value="";
	}
	if(x.tablecellspacing!=""&&x.tablecellspacing!=null){
		window.document.moondownerform.cellspacing.value=x.tablecellspacing;
	}else{
		window.document.moondownerform.cellspacing.value="";
	}
	if(x.tablebordercolor!=""&&x.tablebordercolor!=null){
		window.document.all.item("bordercolor").value=x.tablebordercolor;
		document.all.i_bordercolor.style.backgroundColor = x.tablebordercolor;
	}
	if(x.tablebackgroundcolor!=""&&x.tablebackgroundcolor!=null){
		window.document.all.item("bgcolor").value=x.tablebackgroundcolor;
		document.all.i_bgcolor.style.backgroundColor = x.tablebackgroundcolor;
	}
	
}

function checkkey(x){if(x=="13"){setattributes();}}

</script>
</head>

<body onLoad="initmoondowner();" onunload="clearme();" topmargin="5" leftmargin="5" onkeydown="return checkkey(event.keyCode);" bgcolor=menu>
<form method="POST" name="moondownerform">
  <table border="0" width="100%" cellspacing="0" cellpadding="5" align="center">
    <tr> 
      <td>
      <fieldset><legend>表格布局</legend>
              <table border="0" width="100%" cellpadding="3" cellspacing="0">
                <tr>
	      <td>边框粗细：
	              <input type="text" name="bordersize" size="7" value="0" ONKEYPRESS="event.returnValue=IsDigit();">
	      </td>
                  <td align="right">
                    对齐方式：<select size="1" name="align">
                      <option value="Default" selected>默认&nbsp;</option>
                      <option value="Left">居左</option>
                      <option value="Center">居中</option>
                      <option value="Right">居右</option>
                    </select>
                  </td>
                </tr>
                <tr>
                  <td >
                    单元边距：<input type="text" name="cellpadding" size="7" value="1" ONKEYPRESS="event.returnValue=IsDigit();">
                  </td>
                  <td align="right">
                    单元间距：<input type="text" name="cellspacing" size="7" value="1" ONKEYPRESS="event.returnValue=IsDigit();">
                  </td>
                </tr>
              </table>
      </fieldset>
      
      </td></tr>
      <tr><td>
      <fieldset><legend>表格宽度</legend>
              <table width="100%" border="0" cellspacing="0" cellpadding="0">
                <tr>
                  <td colspan=2 height=30>
                    <input type="checkbox" class="cb" onClick="checkchange();" name="widthspecified" id="widthspecified" value="ON" checked>
		<label for="widthspecified">指定表格宽度</label>
                   </td>
                  <td>
                    <input type="text" name="tablewidth" id="tablewidth" size="4" value="100" ONKEYPRESS="event.returnValue=IsDigit();">
                  </td>
                  <td><input type="radio" class="cb" value="pixels" name="widthtype" id="widthtype1">
                  <label for="widthtype1">像素</label>
                      &nbsp;&nbsp;
                      <input type="radio" class="cb" value="percentage" checked name="widthtype" id="widthtype2">
                  <label for="widthtype2">百分比</label>
                  </td>
                  </tr>
                 </table>
         </fieldset>
      </td>
    </tr>
    <tr> 
      <td width="100%">
      <fieldset><legend>边框</legend>
      <table width="100%" border="0" align="center" cellpadding="3" cellspacing="0">
    <tr>
      <td >
                        边框颜色：<input name='bordercolor' type='text' size="7" maxlength="20"  onchange="colorchange(this,i_bordercolor);">
		<img id="i_bordercolor" src="images/pickcolor.gif" align=absmiddle  onclick="selColor(bordercolor,this);">
                  </td>
                  <td align=right> 
                        背景颜色：<input name='bgcolor' type='text' size="7" maxlength="20"  onchange="colorchange(this,i_bgcolor);">
		<img id="i_bgcolor" src="images/pickcolor.gif" align=absmiddle  onclick="selColor(bgcolor,i_bgcolor);">
              </td>
      </tr>
  </table>
      </fieldset>
      </td>
    </tr>
    
    <tr style="display:none"> 
      <td>
      <fieldset><legend>表格样式</legend> 
          <div align="center">
            <select size="1" name="tablestyle">
              <option value="Default">默认</option>
            </select>
          </div>
      </fieldset>
      </td>
    </tr>
    
    <tr> 
      <td align="right">
        <button name="btn_insert" onclick="setattributes();" > 确定 </button>
        &nbsp;
<button name="btn_cancel" onclick="bye();"> 取消 </button>
      </td>
    </tr>
  </table>
</form>
</body>
</html>
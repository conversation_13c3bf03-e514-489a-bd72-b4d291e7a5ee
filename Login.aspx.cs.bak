﻿using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Data.SqlClient;


public partial class Login : System.Web.UI.Page
{
    const string ConnStr = " Data Source=" + InputParams.DataBaseServer + "; uid=" + InputParams.userName + "; pwd=" + InputParams.password + "; DATABASE=" + InputParams.dbName;
    /// <summary>
    /// 页面加载
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void Page_Load(object sender, EventArgs e)
    {
        HttpCookie cookie = Request.Cookies["user"];
        if (Request.Params["id"] != null)
        {
            cookie = null;
        }
        else
            if (cookie != null)//不用重新登录
            {
                Response.Redirect("Default.aspx");
            }
       
    }

    /// <summary>
    /// 登录用户按键
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void btnLogin_Click(object sender, EventArgs e)
    {
        string strSql = "SELECT user_id,nickname,group_id,isGroupManager FROM Users WHERE ( email='" + tbxUserName.Text.Trim() + "' OR mobile='"+tbxUserName.Text.Trim() +"')  AND password='" + tbxPassword.Text.Trim() + "'";
        SqlConnection conn = new SqlConnection(ConnStr);
        conn.Open();
        SqlCommand comm = new SqlCommand(strSql, conn);
        SqlDataReader dr = comm.ExecuteReader();

        if (dr.Read())
        {
            HttpCookie cookie = new HttpCookie("user");
            cookie.Values.Add("user_id", dr[0].ToString());
            cookie.Values.Add("nickname", dr[1].ToString());
            cookie.Values.Add("group_id", dr[2].ToString());
            cookie.Values.Add("isGroupManager", dr[3].ToString());

            if ( ckbAutoLogin .Checked ==true )
            {
            cookie.Expires = DateTime.Now.AddYears(10);
            }
            Response.AppendCookie(cookie);
            dr.Close();
            Response.Redirect("Default.aspx");

        }

        else
        {
            ClientScript.RegisterClientScriptBlock(this.GetType(), "info", "<script language=\"javascript\" type=\"text/javascript\">alert(\"用户名或密码错误,请重试！\");window.location.href='Login.aspx';</script>");
        }
        conn.Close();

    }


}

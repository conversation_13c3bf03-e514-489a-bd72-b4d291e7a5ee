﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="BrowseUsers.aspx.cs" Inherits="BrowseUsers" %>

<%@ Register Assembly="WebControlLibrary1" Namespace="WebControlLibrary1" TagPrefix="cc1" %>
<html xmlns="http://www.w3.org/1999/xhtml">
<%--"全选"复选框选择的处理函数--%>

<script language="javascript" type="text/javascript">
    function selectAll(obj) {
        var theTable = obj.parentElement.parentElement.parentElement;
        var i;
        var j = obj.parentElement.cellIndex;

        for (i = 0; i < theTable.rows.length; i++) {
            var objCheckBox = theTable.rows[i].cells[j].firstChild;
            if (objCheckBox.checked != null) objCheckBox.checked = obj.checked;
        }
    }
</script>

<head id="Head1" runat="server">
    <title>浏览用户</title>
    <meta name="viewport" content="width=device-width,height=device-height,initial-scale=0.65">
    <style type="text/css">
        body, table, tr, td {
            font-size: 12px;
            font-family: 宋体;
        }
        #browseUsersTable{
            width:80%;
        }
        @media (max-width: 768px) {
        body, table, tr, td {
            font-size: 12px;
            font-family: 宋体;
            max-width: 100%;
            overflow-x: auto;
            margin-top:1%;
            /*            width:350px;
*/ 
            position: relative;
            margin-left:-5%;
        }
        #browseUsersTable{
            width:100%;
        }

        }
    </style>
</head>
<body>
    <form id="form1" method="post" runat="server">
        <div id="div1" runat="server">
            <table id="browseUsersTable" style=" position: absolute; left: 12.5%">
                <tr>
                    <td>
                        <asp:PlaceHolder ID="placeHolder1" runat="server" />
                    </td>
                </tr>
                <tr>
                    <td>
                        <asp:Button ID="btnSearch" runat="server" Text="开始查找" OnClick="btnSearch_Click" />&nbsp;&nbsp;&nbsp;
                        <asp:HyperLink ID="hlkReturn" Text="返回首页" ForeColor="blue" NavigateUrl="~/default.aspx"
                            runat="server" />
                        <hr />
                        <asp:Button ID="btnChose" runat="server" OnClick="btnChoose_Click" Text="批量选定并返回"
                            Width="150" /></td>
                </tr>
                <tr>
                    <td>
                        <cc1:WebCustomControl1 ID="WebCustomControl1_1" OnChangePageClick="pager1_Click" runat="server"></cc1:WebCustomControl1>
                    </td>
                </tr>
                <tr valign="top" align="center">
                    <td valign="top" align="center" style="height: auto">
                        <asp:PlaceHolder ID="placeHolder2" runat="server" />
                    </td>
                </tr>
                <tr>
                    <td>
                        <cc1:WebCustomControl1 ID="WebCustomControl1_2" OnChangePageClick="pager2_Click" runat="server"></cc1:WebCustomControl1>
                    </td>
                </tr>
            </table>
        </div>
    </form>
</body>
</html>

﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="Modify.aspx.cs" Inherits="Modify" %>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html xmlns="http://www.w3.org/1999/xhtml">
<head id="Head1" runat="server">
    <title>修改页面</title>
    <meta name="viewport" content="width=device-width,height=device-height,initial-scale=0.65,user-scalable=no">
    <style type="text/css">
        body, table, tr, td {
            font-size: 16px;
            font-family: 宋体;
        }
        #modifyTable{
            width:80%;
        }
        @media (min-width: 768px) {
            #M_Td1 {
                text-align: center;
            }
        }
        @media (max-width: 768px) {
        body, table, tr, td {
            font-size: 22px;
            font-family: 宋体;
            max-width: 100%;
            overflow-x: auto;
            margin-top:0.2%;
            position: relative;
            margin-left:-5%;
        }
/*        body {
          font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
          background-color: #f7f7f7;
          margin: 0;
          padding: 0;
        }*/
        #modifyTable{
            width:100%;
        }
    
        .textbox1-style {
            width: 100%;
            padding: 8px;
            font-size: 25px;
/*            border: 1px solid #ccc;*/
            border-radius: 4px;
            box-sizing: border-box;
        }
        .textbox2-style {
            font-size: 25px;
            width: 100%;
        }

        .textbox13-style {
            font-size: 25px;
        }
        .time-dropdown {
            font-size: 25px;
            width: 100%;
        }
        .time-dropdown option {
            font-size: 14px;
        }
        .time-dropdown2 {
            font-size: 25px;
            width: 100%;
        }
        .time-dropdown2 option {
            font-size: 14px;
        }
        .time-dropdown3 {
            font-size: 25px;
            width: 100%;

        }
        .time-dropdown3 option{
            font-size: 14px;
        }
        .textbox4-style {
            font-size: 25px;
            width: 100%;
        }
        .navbar {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            font-size: 26px;
        }

        .navbar span {
            opacity: 0.8;
        }

        .container {
            padding: 20px;
        }

        .card {
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            width: 100%;
            max-width: 600px;
            margin: auto;
            box-shadow: 0 2px 6px rgba(0,0,0,0.05);
            box-sizing: border-box;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            font-size: 27px;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }

        .form-group span,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 8px;
            font-size: 25px;
/*            border: 1px solid #ccc;*/
            border-radius: 4px;
            box-sizing: border-box;
        }

        .form-group textarea {
            height: 80px;
            resize: vertical;
        }

        .form-actions {
            text-align: center;
            margin-top: 20px;
        }

        .form-actions button {
            padding: 8px 16px;
            margin: 0 10px;
            font-size: 14px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        .btn-primary {
            background-color: #007bff;
            color: white;
        }

        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }

        }
    </style>
</head>

<script src="js/time.js" type="text/javascript"></script>

<body>
    <form id="form1" method="post" runat="server">
        <table id="modifyTable" border="1" style="background: #e6e8fa; position: absolute; left: 10%; background-position: center; border-color: #666666">
            <tr>
                <td id="M_Td1" >
<%--                    <br />
                    <asp:LinkButton ID="hlkReturn" ForeColor="Blue" Font-Underline="false" Text="返回首页" runat="server" OnClick="hlkReturn_Click" 
                        Style="text-decoration: none; border: 1px solid #871f78; padding: 2px 8px; display: inline-block; border-radius: 10px;
                            background-color: #E6E8FA; "/>
                    <br />
                    <br />--%>
                    <div id="div1" runat="server">
                         <br/>
                    <br/>
                    </div>
                   
                    
                </td>
            </tr>
            <tr>
                <td align="center" style="border: 0px">
                    <br />
                    <asp:LinkButton ID="btnModify" Width="100px" runat="server" Text="确定修改" OnClick="btnModify_Click" 
                        Style="text-decoration: none; border: 1px solid #871f78; padding: 2px 8px; display: inline-block; border-radius: 10px;
                            background-color: #E6E8FA; "/>
                    <asp:LinkButton ID="LinkButton1" ForeColor="Blue" Font-Underline="false" Text="返回首页" runat="server" OnClick="hlkReturn_Click" 
                        Style="text-decoration: none; border: 1px solid #871f78; padding: 2px 8px; display: inline-block; border-radius: 10px;
                            background-color: #E6E8FA; "/>
                    <br />
                    <br />
                </td>
            </tr>
        </table>
    </form>
</body>
</html>

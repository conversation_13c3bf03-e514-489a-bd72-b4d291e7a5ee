﻿using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data.SqlClient;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Web;
using System.Web.Services;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class Register : System.Web.UI.Page
{
    protected void Page_Load(object sender, EventArgs e)
    {
        Response.Cache.SetCacheability(HttpCacheability.NoCache);
        if (!IsPostBack) // 首次加载
        {
            //BindGroups();
        }
        else
        {
            // 保留页面刷新后密码框输入消失
            tbPwd.Attributes["value"] = tbPwd.Text.Trim();
            tbConfirmPwd.Attributes["value"] = tbConfirmPwd.Text.Trim();

            if (rbCreate.Checked)
            {
                // 复现创建的组织
                string groupId = Common.ToString(Session["groupId"]);
                string groupName = Common.ToString(Session["groupName"]);
                if (!string.IsNullOrEmpty(groupId) || !string.IsNullOrEmpty(groupName))
                {
                    joinCompanySection.Visible = false;
                    createCompanySection.Visible = true;
                    txtGroupId.Value = groupId;
                    txtGroupName.Text = groupName;
                    txtNewCompanyName.Text = groupName;
                    lblCompanyCreationStatus.Text = "新企业/团队名称有效";
                    lblCompanyCreationStatus.ForeColor = System.Drawing.Color.Green;
                }
            }

        }
    }

    private void BindGroups()
    {
        string searchGroupName = txtCompanySearch.Text.Trim();
        List<Group> groups = new List<Group>();
        string query = string.Format("SELECT groupId, groupName FROM Groups where groupName like '%{0}%'", searchGroupName);
        //Logger.Log("query sql " + query);

        // 从数据库获取公司列表
        using (SqlConnection conn = new SqlConnection(InputParams.connectionStr))
        {
            using (SqlCommand cmd = new SqlCommand(query, conn))
            {
                conn.Open();
                using (SqlDataReader reader = cmd.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        // 将每条记录添加到列表中
                        groups.Add(new Group
                        {
                            groupId = reader.GetInt32(0),
                            groupName = reader.GetString(1)
                        });
                    }
                }
            }
        }

        //Logger.Log(string.Join(",", groups));

        rptGroupList.DataSource = groups;
        rptGroupList.DataBind();
    }

    [WebMethod]
    public static string SendVerificationCode(string phoneNumber)
    {
        // 验证手机号码格式
        if (!Regex.IsMatch(phoneNumber, @"^1[3-9]\d{9}$"))
        {
            return "无效的手机号码格式";
        }

        object count = Common.ExecuteScalar(string.Format("select count(0) from users where mobile={0} or telephone={1}", phoneNumber, phoneNumber));
        if (Convert.ToInt32(count) > 0)
        {
            return "该手机号码已注册";
        }

        // 生成验证码并发送
        string verificationCode = Common.GetVerifyCode();

        // 将异步发送短信的任务改为同步
        Task.Run(() => SmsService.SendSmsAsync(phoneNumber, verificationCode));
        //bool isSent = SmsService.SendSmsAsync(phoneNumber, verificationCode).GetAwaiter().GetResult();

        HttpContext.Current.Session["VerificationCode"] = verificationCode;
        HttpContext.Current.Session["LastSendTime"] = DateTime.Now;
        return "验证码已发送";
    }

    protected void btnSubmit_Click(object sender, EventArgs e)
    {
        string email = tbEmail.Text.Trim();
        string phone = tbPhone.Text.Trim();
        string password = tbPwd.Text.Trim();
        string confirmPassword = tbConfirmPwd.Text.Trim();
        string userName = tbUserName.Text.Trim();
        string realName = tbRealName.Text.Trim();
        string verifyCode = tbCode.Text.Trim();
        string groupId = txtGroupId.Value.Trim();
        string groupName = txtGroupName.Text.Trim();
        string correctVerifyCode = Common.ToString(Session["VerificationCode"]);
        string codeTimeStr = Common.ToString(Session["LastSendTime"]);

        if (string.IsNullOrEmpty(groupId))
        {
            Response.Write(Common.alertMsg("请选择企业/团队"));
            return;
        }

        if (string.IsNullOrEmpty(correctVerifyCode) || string.IsNullOrEmpty(codeTimeStr))
        {
            Response.Write(Common.alertMsg("验证码无效,请重新获取"));
            return;
        }

        DateTime codeTime;
        if (DateTime.TryParse(codeTimeStr, out codeTime))
        {
            DateTime currentTime = DateTime.Now;
            // 计算验证码是否过期
            TimeSpan timeSinceCodeGenerated = currentTime - codeTime;
            if (timeSinceCodeGenerated.TotalMinutes > InputParams.verificationCodeTimeoutMinutes)
            {
                Response.Write(Common.alertMsg("验证码已过期"));
                return;
            }
        }
        else
        {
            Response.Write(Common.alertMsg("验证码已过期或失效"));
            return;
        }
        if (verifyCode != correctVerifyCode)
        {
            Response.Write(Common.alertMsg("验证码错误"));
            return;
        }
        if (password != confirmPassword)
        {
            Response.Write(Common.alertMsg("两次密码不一致，请重新输入"));
            return;
        }
        if (rbJoin.Checked && string.IsNullOrEmpty(txtGroupName.Text))
        {
            Response.Write(Common.alertMsg("请选择企业/团队"));
            return;
        }

        try
        {
            using (SqlConnection conn = new SqlConnection(InputParams.connectionStr))
            {
                conn.Open();

                string checkQuery = "SELECT COUNT(*) FROM Users WHERE email = @Email OR nickname = @UserName OR mobile = @Mobile Or telephone = @Telephone";
                using (SqlCommand checkCmd = new SqlCommand(checkQuery, conn))
                {
                    checkCmd.Parameters.AddWithValue("@Email", email);
                    checkCmd.Parameters.AddWithValue("@UserName", userName);
                    checkCmd.Parameters.AddWithValue("@Mobile", phone);
                    checkCmd.Parameters.AddWithValue("@Telephone", phone);
                    int userExists = (int)checkCmd.ExecuteScalar();

                    if (userExists > 0)
                    {
                        Response.Write("<script>alert('电子邮箱或用户名与手机号已存在，请使用其他电子邮箱或用户名与手机号。');</script>");
                        return;
                    }
                }

                string insertSql;
                // 创建团队的人注册成为该团队的管理员
                if (rbCreate.Checked)
                {
                    insertSql = $"INSERT INTO Users (email, mobile, password, nickname, truename,group_id,isGroupManager) VALUES (@Email, @Phone, @Password, @UserName, @RealName, @GroupId,1)";
                }
                else
                {
                    insertSql = $"INSERT INTO Users (email, mobile, password, nickname, truename,group_id) VALUES (@Email, @Phone, @Password, @UserName, @RealName, @GroupId)";
                }

                using (SqlCommand cmd = new SqlCommand(insertSql, conn))
                {
                    cmd.Parameters.AddWithValue("@Email", email);
                    cmd.Parameters.AddWithValue("@Phone", phone);
                    cmd.Parameters.AddWithValue("@Password", password);
                    cmd.Parameters.AddWithValue("@UserName", userName);
                    cmd.Parameters.AddWithValue("@RealName", realName);
                    cmd.Parameters.AddWithValue("@GroupId", groupId);
                    cmd.ExecuteNonQuery();

                    Response.Write("<script>alert('注册成功！'); window.location='Login.aspx';</script>");

                    Session.Clear(); // 清楚session中的注册数据
                    HttpContext.Current.Session.Clear();
                }
            }
        }
        catch (Exception ex)
        {
            Response.Write("<script>alert('注册失败：" + ex.Message + "');</script>");
        }
    }
    protected void btnSendCode_Click(object sender, EventArgs e)
    {

    }

    protected void rblCompanyChoice_SelectedIndexChanged(object sender, EventArgs e)
    {
        if (rbJoin.Checked)
        {
            joinCompanySection.Visible = true;
            createCompanySection.Visible = false;
        }
        else if (rbCreate.Checked)
        {
            joinCompanySection.Visible = false;
            createCompanySection.Visible = true;
        }
        txtGroupId.Value = "";
        txtGroupName.Text = "";
    }

    protected void btnSearchCompany_Click(object sender, EventArgs e)
    {
        BindGroups();
    }

    protected void btnConfirmNewCompany_Click(object sender, EventArgs e)
    {
        string newCompanyName = txtNewCompanyName.Text.Trim();
        string countSql = string.Format("select count(0) from Groups where groupName = '{0}'", newCompanyName);
        object count = Common.ExecuteScalar(countSql);
        bool bExits = Convert.ToInt32(count) > 0;
        if (bExits)
        {
            lblCompanyCreationStatus.Text = "该企业/团队名称已存在，请重新输入";
            lblCompanyCreationStatus.ForeColor = System.Drawing.Color.Red;
        }
        else
        {
            string insertSql = string.Format("insert into Groups(groupName) values ('{0}'); SELECT SCOPE_IDENTITY();", newCompanyName);
            int insertId = Convert.ToInt32(Common.ExecuteScalar(insertSql));
            if (insertId > 0)
            {
                lblCompanyCreationStatus.Text = "新企业/团队名称有效";
                lblCompanyCreationStatus.ForeColor = System.Drawing.Color.Green;

                Session["groupId"] = insertId;
                Session["groupName"] = newCompanyName;
            }
        }
    }

    protected void rptCompanyList_ItemCommand(object source, RepeaterCommandEventArgs e)
    {

    }

    protected void btnSelectCompany_Command(object sender, CommandEventArgs e)
    {
        string[] args = e.CommandArgument.ToString().Split('|');
        txtGroupId.Value = args[0];
        txtGroupName.Text = args[1];
    }
}
﻿using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Data.SqlClient;

public partial class ModifyUsers : System.Web.UI.Page
{
    string strUpdates;
    int modifyCount;
    int[] ckbsModify;
    int id;
    public const string AETable = "Users";
    public const string AEKey = "user_id";
    readonly string ConnStr = InputParams.connectionStr;
    public EnumField[][] EnumField()
    {
        EnumField[] enum0 = { new EnumField(1, "男"), new EnumField(2, "女") };
        EnumField[] enum1 = { new EnumField(0, "普通用户"), new EnumField(2, "管理员") };

        EnumField[][] efTs = { enum0 };
        return efTs;
    }

    public Field[] InitFields()
    {
        //Field fld0 = new Field(0, "user_id", "用户编号", 80, EnumFieldType.numberType, 0, 0, "", 0, "");
        Field fld0 = new Field(0, "nickname", "用户名", 80, EnumFieldType.charType, 0, 0, "", 0, "");
        Field fld1 = new Field(1, "email", "电子邮箱", 80, EnumFieldType.charType, 0, 0, "", 0, "");
        Field fld2 = new Field(2, "truename", "真实姓名", 80, EnumFieldType.charType, 0, 0, "", 0, "");
        Field fld3 = new Field(3, "mobile", "联系方式", 50, EnumFieldType.charType, 0, 0, "", 0, "");
        //Field fld4 = new Field(4, "age", "年龄", 60, EnumFieldType.numberType, 0, 0, "", 0, "");
        Field fld4 = new Field(5, "sex", "性别", 50, EnumFieldType.enumType, 0, 0, "", 0, "");
        Field fld5 = new Field(6, "isNewYufutong", "预付通用户", 60, EnumFieldType.boolType, 0, 0, "", 0, "");
        Field fld6 = new Field(7, "state", "状态启用", 60, EnumFieldType.boolType, 0, 0, "", 0, "");
        Field fld7 = new Field(8, "isGroupManager", "角色", 60, EnumFieldType.enumType, 0, 0, "", 0, "");

        Field[] flds = { fld0, fld1, fld2, fld3, fld4, fld5,fld6,fld7 };
        return flds;
    }
    /// <summary>
    /// 页面初始化
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>

    protected void Page_Load(object sender, EventArgs e)
    {
        try
        {
            modifyCount = ckbsModify?.Length ?? 0;
            if (Request.Params["UserID"] != null)
            {
                id = int.Parse(Request.Params["UserID"].ToString());
                GeneModifyMould();
            }
            else
            {
                GeneModifyMould();
            }
        }
        catch (Exception ex)
        {
            Response.Write(Common.alertMsg(ex.Message));
        }


    }
    protected void btnModify_Click(object sender, EventArgs e)
    {

        GetUpdateStr();
        string user_id = "";
        if (Request.Cookies["user"] != null)
        {
            HttpCookie cookie = Request.Cookies["user"];
            user_id = cookie.Values["user_id"].ToString();
        }
        try
        {
            Update();
            string strDire = "<script language=\"javascript\" type=\"text/javascript\">alert(\"修改成功！\");window.location.href='Default.aspx?user_id=" + user_id + "';</script>";
            ClientScript.RegisterClientScriptBlock(this.GetType(), "info", strDire);

        }
        catch
        {
            string strDire = "<script language=\"javascript\" type=\"text/javascript\">alert(\"修改失败！\");window.location.href='Default.aspx?user_id=" + user_id + "';</script>";
        }
    }



    protected void lbt_Command(object sender, CommandEventArgs e)
    {
        Field[] fields = InitFields();
        EnumField[][] efTs = EnumField();
        string linkPage = e.CommandArgument.ToString();

        for (int addIndex = 0; addIndex < modifyCount; addIndex++)
        {
            PlaceHolder pHolder = (PlaceHolder)div1.FindControl("placeHolder" + addIndex);
            for (int fieldIndex = 0; fieldIndex < fields.Length; fieldIndex++)
            {
                string strFldName = fields[fieldIndex].fieldName + addIndex;
                switch (fields[fieldIndex].fieldType)
                {
                    case EnumFieldType.enumType:
                        string str1 = "ddl" + strFldName;
                        DropDownList ddl = (DropDownList)pHolder.FindControl(str1);
                        Session[str1] = ddl.Text;
                        continue;

                    case EnumFieldType.boolType:
                        string str2 = "rb" + strFldName + "1";
                        if (((RadioButton)pHolder.FindControl(str2)).Checked == true)
                        {
                            Session[str2] = 1;
                        }
                        else
                        {
                            Session[str2] = 0;
                        }
                        continue;

                    default:
                        string str3 = "tbx" + strFldName;
                        Session[str3] = ((TextBox)(pHolder.FindControl(str3))).Text.Trim().ToString();
                        continue;
                }
            }
        }
        Session["supPage"] = "ModifyTasks.aspx";
        string strDic = linkPage + "?tbxName=" + e.CommandName;
        Response.Redirect(strDic);
    }


    /// <summary>
    /// 生成信息输入模板
    /// </summary>
    /// <param name="placeHolder"></param>
    /// <param name="fields"></param>
    public void GeneModifyMould()
    {
        div1.Controls.Clear();
        Field[] fields = InitFields();
        EnumField[][] efTs = EnumField();

        string strStart = "SELECT ";
        for (int fieldIndex = 0; fieldIndex < fields.Length; fieldIndex++)
        {
            strStart += fields[fieldIndex].fieldName;
            if (fieldIndex != fields.Length - 1)
            {
                strStart += ",";
            }
        }
        strStart += " FROM " + AETable + " WHERE " + AEKey + "=";
        SqlConnection conn = new SqlConnection(ConnStr);
        Common.OpenConnection(conn);

        int modifyIndex = 0;
        string str = strStart + Request.Params["UserID"].ToString();
        SqlDataAdapter da = new SqlDataAdapter(str, conn);
        DataSet ds = new DataSet();
        da.Fill(ds);
        DataTable dt = ds.Tables[0];
        DataRow dr = dt.Rows[0];

        PlaceHolder placeHolder = new PlaceHolder();
        placeHolder.ID = "placeHolder" + modifyIndex;
        div1.Controls.Add(placeHolder);
        PlaceHolder pHolder = (PlaceHolder)div1.FindControl("placeHolder" + modifyIndex);
        if (modifyIndex != 0)
        {
            Literal ltlHR = new Literal();
            ltlHR.Text = "<HR/>";
            placeHolder.Controls.Add(ltlHR);
        }

        Literal ltlTag = new Literal();
        ltlTag.Text = AEKey + "：" + Request.Params["UserID"].ToString();
        placeHolder.Controls.Add(ltlTag);

        for (int fieldIndex = 0; fieldIndex < fields.Length; fieldIndex++)
        {
            Label lbl1 = new Label();
            lbl1.Text = "<br/>";
            pHolder.Controls.Add(lbl1);

            Label lblKongGe2 = new Label(); //定义两个空格
            lblKongGe2.Text = "&nbsp;&nbsp;";

            Label lblKongGe3 = new Label(); //定义四个空格
            lblKongGe3.Text = "&nbsp;&nbsp;&nbsp;&nbsp;";

            Label lblTag = new Label();
            lblTag.Text = fields[fieldIndex].fieldShowName + "：" + " ";
            lblTag.ForeColor = System.Drawing.ColorTranslator.FromHtml(InputParams.lblFieldShowNameColor);
            pHolder.Controls.Add(lblTag);

            string strFldName = fields[fieldIndex].fieldName;
            switch (fields[fieldIndex].fieldType)
            {
                case EnumFieldType.charType:
                    #region charType
                    TextBox tbx1 = new TextBox();
                    
                    tbx1.ID = "tbx" + strFldName;
                    string str1 = "tbx" + strFldName;
                    //if (id == 1)
                    //{
                    //    tbx1.Text = Session[str1].ToString();
                    //}
                    //else
                    //{
                    tbx1.Text = dr[fieldIndex].ToString();
                    //}

                    tbx1.Width = InputParams.tbxCharLength;
                    tbx1.Height = InputParams.tbxHeight;
                    tbx1.TextMode = TextBoxMode.MultiLine;
                    tbx1.Wrap = true;
                    tbx1.Style.Add("overflow", "hidden");

                    //为了界面美观，输入框能够对齐。在用户名后多加下一个空格，在性别名后多加两个空格
                    if (strFldName == "nickname")//如果是用户名,添加一个空格
                        pHolder.Controls.Add(lblKongGe2);
                    if (strFldName == "sex")   //若是性别，加两个空格
                    {
                        pHolder.Controls.Add(lblKongGe3);
                    }


                    //pHolder.Controls.Add(lblKongGe);//添加空格
                    pHolder.Controls.Add(tbx1);
                    ((TextBox)pHolder.FindControl("tbx" + strFldName)).Attributes.Add("onMouseOver", "this.style.backgroundColor='" + InputParams.tbxOnmouseoverColor + "';");
                    ((TextBox)pHolder.FindControl("tbx" + strFldName)).Attributes.Add("onMouseOut", "this.style.backgroundColor='" + InputParams.tbxOnmouseoutColor + "';");
                    continue;
                #endregion charType
                case EnumFieldType.longcharType:
                    TextBox tbx2 = new TextBox();
                    tbx2.ID = "tbx" + strFldName;
                    string str2 = "tbx" + strFldName;
                    //if (id == 1)
                    //{
                    //    tbx2.Text = Session[str2].ToString();
                    //}
                    //else
                    //{
                    tbx2.Text = dr[fieldIndex].ToString();
                    //}
                    tbx2.Width = InputParams.tbxLongCharLength;
                    tbx2.Height = InputParams.tbxLongCharHeight;
                    tbx2.TextMode = TextBoxMode.MultiLine;
                    pHolder.Controls.Add(tbx2);
                    ((TextBox)pHolder.FindControl("tbx" + strFldName)).Attributes.Add("onMouseOver", "this.style.backgroundColor='" + InputParams.tbxOnmouseoverColor + "';");
                    ((TextBox)pHolder.FindControl("tbx" + strFldName)).Attributes.Add("onMouseOut", "this.style.backgroundColor='" + InputParams.tbxOnmouseoutColor + "';");
                    continue;

                case EnumFieldType.numberType:
                    TextBox tbx3 = new TextBox();
                    tbx3.ID = "tbx" + strFldName;
                    string str3 = "tbx" + strFldName;
                    //if (id == 1)
                    //{
                    //    tbx3.Text = Session[str3].ToString();
                    //}
                    //else
                    //{
                    tbx3.Text = "";
                    //}
                    tbx3.Text = Request.Params["UserID"].ToString();//把用户ID放入用户ID框中

                    tbx3.Width = InputParams.tbxNumberLength;
                    tbx3.Height = InputParams.tbxHeight;
                    pHolder.Controls.Add(tbx3);

                    ((TextBox)pHolder.FindControl("tbx" + strFldName)).Attributes.Add("onMouseOver", "this.style.backgroundColor='" + InputParams.tbxOnmouseoverColor + "';");
                    ((TextBox)pHolder.FindControl("tbx" + strFldName)).Attributes.Add("onMouseOut", "this.style.backgroundColor='" + InputParams.tbxOnmouseoutColor + "';");
                    if (fields[fieldIndex].linkPage.ToString() != "")
                    {
                        LinkButton lbt = new LinkButton();
                        lbt.Text = "  点此查找";
                        lbt.ID = "lbt" + strFldName;
                        lbt.Command += new CommandEventHandler(lbt_Command);
                        lbt.CommandName = "tbx" + strFldName;
                        lbt.CommandArgument = fields[fieldIndex].linkPage.ToString();
                        pHolder.Controls.Add(lbt);
                    }
                    continue;

                case EnumFieldType.dateType:
                    TextBox tbx4 = new TextBox();
                    tbx4.ID = "tbx" + strFldName;
                    string str4 = "tbx" + strFldName;

                    //if (id == 1)
                    //{
                    //    tbx4.Text = Session[str4].ToString();
                    //}
                    //else
                    //{
                    try
                    {
                        tbx4.Text = DateTime.Parse(dr[fieldIndex].ToString()).ToShortDateString().ToString();
                    }
                    catch
                    {
                        tbx4.Text = DateTime.Now.ToShortDateString().ToString();
                    }
                    //}
                    tbx4.Width = InputParams.tbxDateLength;
                    tbx4.Height = InputParams.tbxHeight;
                    pHolder.Controls.Add(tbx4);
                    ((TextBox)pHolder.FindControl("tbx" + strFldName)).Attributes.Add("onClick", "javascript:calendar()");
                    ((TextBox)pHolder.FindControl("tbx" + strFldName)).Attributes.Add("onClick", "javascript:calendar()");
                    continue;

                case EnumFieldType.boolType:
                    RadioButton rbY = new RadioButton();
                    RadioButton rbN = new RadioButton();

                    rbY.ID = "rb" + strFldName + "1";
                    string strY = "rb" + strFldName + "1";
                    rbY.GroupName = "gn" + strFldName;
                    rbY.Text = "是";
                    rbY.ForeColor = System.Drawing.ColorTranslator.FromHtml(InputParams.rbYNColor);
                    

                    rbN.ID = "rb" + strFldName + "2";
                    string strN = "rb" + strFldName + "2";
                    rbN.GroupName = "gn" + strFldName;
                    rbN.Text = "否";
                    rbN.ForeColor = System.Drawing.ColorTranslator.FromHtml(InputParams.rbYNColor);
                   

                    //if (id == 1)
                    //{
                    //    if (Session[strY] != null)
                    //    {
                    //        if (Session[strY].ToString() == "1")
                    //        {
                    //            ((RadioButton)pHolder.FindControl(strY)).Checked = true;
                    //        }
                    //        else
                    //        {
                    //            ((RadioButton)pHolder.FindControl(strN)).Checked = true;

                    //        }
                    //    }
                    //}
                    
                    if (strFldName.Equals("isNewYufutong"))
                    {
                        bool isYufutong = (bool)dr[5];
                        rbY.Checked = isYufutong == true;
                        rbN.Checked = isYufutong == false;
                    }
                    else if (strFldName.Equals("state"))
                    {
                        int userState = (int)dr.ItemArray[6];
                        rbY.Checked = userState == 1;
                        rbN.Checked = userState == 0;
                    }
                    pHolder.Controls.Add(rbY);
                    pHolder.Controls.Add(rbN);
                    continue;

                case EnumFieldType.picType:
                    TextBox tbx5 = new TextBox();
                    tbx5.ID = "tbx" + strFldName;
                    string str5 = "tbx" + strFldName;
                    //if (id == 1)
                    //{
                    //    tbx5.Text = Session[str5].ToString();
                    //}
                    //else
                    //{
                    tbx5.Text = dr[fieldIndex].ToString();
                    //}
                    tbx5.Width = InputParams.tbxCharLength;
                    tbx5.Height = InputParams.tbxHeight;
                    tbx5.TextMode = TextBoxMode.MultiLine;
                    tbx5.Wrap = true;
                    tbx5.Style.Add("overflow", "hidden");
                    pHolder.Controls.Add(tbx5);
                    ((TextBox)pHolder.FindControl("tbx" + strFldName)).Attributes.Add("onMouseOver", "this.style.backgroundColor='" + InputParams.tbxOnmouseoverColor + "';");
                    ((TextBox)pHolder.FindControl("tbx" + strFldName)).Attributes.Add("onMouseOut", "this.style.backgroundColor='" + InputParams.tbxOnmouseoutColor + "';");
                    continue;

                case EnumFieldType.enumType:
                    DropDownList ddl = new DropDownList();
                    ddl.ID = "ddl" + strFldName;
                    string str6 = "ddl" + strFldName;
                    int enumT = fields[fieldIndex].enumTag;
                    ddl.Width = InputParams.ddlWidth;
                    ddl.AutoPostBack = false;
                    pHolder.Controls.Add(ddl);

                    for (int enumLen = 0; enumLen < efTs[enumT].Length; enumLen++)
                    {
                        ListItem li = new ListItem();
                        li.Value = efTs[enumT][enumLen].enumItem.ToString();
                        li.Text = efTs[enumT][enumLen].itemDetail;
                        if (id == 1)
                        {
                            if ((Session[str6] != null) && (efTs[enumT][enumLen].enumItem.ToString() == Session[str6].ToString()))
                            {
                                li.Selected = true;
                            }
                        }
                        ddl.Items.Add(li);
                    }
                    ((DropDownList)pHolder.FindControl("ddl" + strFldName)).Attributes.Add("onMouseOver", "this.style.backgroundColor='" + InputParams.tbxOnmouseoverColor + "';");
                    ((DropDownList)pHolder.FindControl("ddl" + strFldName)).Attributes.Add("onMouseOut", "this.style.backgroundColor='" + InputParams.tbxOnmouseoutColor + "';");
                    continue;
            }
        }

        if (conn.State.ToString() == "Open")
        {
            conn.Close();
        }

    }


    /// <summary>
    /// 获得更新记录的SQL Update字符串
    /// </summary>
    /// <param name="placeHolder"></param>
    /// <param name="fields"></param>
    /// <returns></returns>
    public void GetUpdateStr()
    {
        Field[] fields = InitFields();
        EnumField[][] efTs = EnumField();

        int modifyIndex = 0;//先只考虑一个的修改

        string strUpdate = "UPDATE " + AETable + " SET ";
        PlaceHolder pHolder = (PlaceHolder)div1.FindControl("placeHolder" + modifyIndex);

        for (int fieldIndex = 0; fieldIndex < fields.Length; fieldIndex++)
        {
            if (fieldIndex != 0)
            {
                strUpdate += ",";
            }
            strUpdate += fields[fieldIndex].fieldName + "=";

            string strFldName = fields[fieldIndex].fieldName;

            switch (fields[fieldIndex].fieldType)
            {

                case EnumFieldType.numberType:
                    string tbxName2 = "tbx" + strFldName + "2";
                    if (((TextBox)pHolder.FindControl("tbx" + strFldName)).Text != "")
                    {
                        if (Session[tbxName2] != null)
                        {
                            strUpdate += int.Parse(Session[tbxName2].ToString());
                        }
                        else
                        {
                            strUpdate += ((TextBox)pHolder.FindControl("tbx" + strFldName)).Text.Trim();
                        }
                    }
                    else
                    {
                        strUpdate += "' '";
                    }
                    continue;

                case EnumFieldType.dateType:
                    if (((TextBox)pHolder.FindControl("tbx" + strFldName)).Text != "")
                    {
                        DateTime dt = DateTime.Parse(((TextBox)pHolder.FindControl("tbx" + strFldName)).Text.Trim());
                        strUpdate += "'" + dt.ToString() + "'";
                    }
                    continue;

                case EnumFieldType.enumType:
                    string strText = ((DropDownList)pHolder.FindControl("ddl" + strFldName)).Text.Trim();
                    int iValue = 0;
                    int enumT = fields[fieldIndex].enumTag;
                    for (int enumLen = 0; enumLen < efTs[enumT].Length; enumLen++)
                    {
                        iValue = efTs[enumT][enumLen].enumItem;
                        if (efTs[enumT][enumLen].itemDetail == strText)
                        {
                            break;
                        }
                    }
                    strUpdate += iValue;
                    continue;

                case EnumFieldType.boolType:

                    if (((RadioButton)pHolder.FindControl("rb" + strFldName + "1")).Checked)
                    {
                        strUpdate += "1";
                    }
                    else
                    {
                        strUpdate += "0";
                    }
                    continue;

                default:
                    if (((TextBox)pHolder.FindControl("tbx" + strFldName)).Text != "")
                    {
                        strUpdate += "'" + ((TextBox)pHolder.FindControl("tbx" + strFldName)).Text.Trim() + "'";
                    }
                    else
                    {
                        strUpdate += "' '";
                    }
                    continue;
            }
        }
        strUpdate += " WHERE " + AEKey + "=" + Request.Params["UserID"].ToString();
        strUpdates = strUpdate;

    }

    /// <summary>
    /// 向数据库添加记录
    /// </summary>
    /// <param name="strInsert"></param>
    public void Update()
    {
        SqlConnection conn = new SqlConnection(ConnStr);

        if (conn.State.ToString() == "Closed")
        {
            conn.Open();
        }
        //for (int modifyIndex = 0; modifyIndex < ckbsModify.Length; modifyIndex++)
        //{
        SqlCommand comm = new SqlCommand(strUpdates, conn);
        try
        {
            comm.ExecuteNonQuery();

        }
        catch
        {
        }
        //}
        if (conn.State.ToString() == "Open")
        {
            conn.Close();
        }
    }
}

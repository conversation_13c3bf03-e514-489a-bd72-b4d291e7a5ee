﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="Register.aspx.cs" Inherits="Register" EnableEventValidation="False" EnableViewState="True" %>

<%@ Import Namespace="Newtonsoft.Json.Linq" %>
<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title>注册</title>
    <style>
        body, table, tbody, tr, td {
            font-size: 14px;
            font-family: 宋体;
        }

        table {
            width: 100%;
            height: 35px;
            max-width: 500px;
            margin: 50px auto;
            border-collapse: collapse;
            background-color: #f7f7f7
        }

        thead td {
            text-align: center;
            font-weight: bold;
            padding: 10px;
            background-color: #f2f2f2;
            font-size: medium;
            font-family: 幼圆;
        }

        tbody td {
            padding: 5px 10px;
            /*            min-width: 65px;*/
            border: 0;
        }

        .input-field {
            width: 100%;
            height: 35px;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            box-sizing: border-box;
        }

        .submit-button {
            padding: 3px 10px;
        }

        .aspNet-Error {
            color: red;
            font-size: 12px;
        }

        .td-head {
            border: 1px solid;
            background-color: #cccccc;
            font-weight: bold;
            font-size: medium;
            font-family: 幼圆;
        }

        .right-btn {
            float: right;
            height: 35px;
        }

        .fr {
            float: right;
        }
    </style>
</head>
<body>
    <form id="form1" runat="server" method="post" autocomplete="off">
        <div>
            <table border="1" cellpadding="1" cellspacing="0">
                <thead>
                    <tr>
                        <td colspan="2" class="td-head">客户跟踪系统注册</td>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td style="width: 80px">
                            <label for="tbEmail">电子邮箱</label></td>
                        <td>
                            <asp:TextBox ID="tbEmail" runat="server" CssClass="input-field" placeholder="请输入您的电子邮箱"></asp:TextBox>
                            <asp:RegularExpressionValidator ID="EmailValidator" runat="server"
                                ControlToValidate="tbEmail" ErrorMessage="请输入有效的电子邮箱"
                                ValidationExpression="^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$" ForeColor="Red" Display="Dynamic" />
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label for="tbPhone">手机号码</label></td>
                        <td>
                            <asp:TextBox ID="tbPhone" runat="server" CssClass="input-field" placeholder="请输入您的手机号码"></asp:TextBox>
                            <asp:RequiredFieldValidator ID="phoneValidator" runat="server" ControlToValidate="tbPhone" ErrorMessage="手机号不能为空"
                                ForeColor="Red" Display="Dynamic" />
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label for="tbCode">验证码</label></td>
                        <td>
                            <div>
                                <asp:TextBox ID="tbCode" runat="server" CssClass="input-field" placeholder="请输入验证码" Width="75%"></asp:TextBox>
                                <asp:Button ID="btnSendCode" runat="server" Text="获取验证码" CssClass="right-btn"
                                    OnClick="btnSendCode_Click" OnClientClick="return sendCode();" CausesValidation="False" />
                            </div>
                            <asp:RequiredFieldValidator ID="CodeValidator" runat="server" ControlToValidate="tbCode" ErrorMessage="验证码不能为空"
                                ForeColor="Red" Display="Dynamic" />
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label for="tbPwd">密码</label></td>
                        <td>
                            <asp:TextBox ID="tbPwd" runat="server" CssClass="input-field" TextMode="Password" placeholder="请输入密码" ></asp:TextBox>
                            <asp:RequiredFieldValidator ID="PwdRequiredValidator" runat="server"
                                ControlToValidate="tbPwd" ErrorMessage="密码不能为空" ForeColor="Red" Display="Dynamic" InitialValue="" />
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label for="tbConfirmPwd">确认密码</label></td>
                        <td>
                            <asp:TextBox ID="tbConfirmPwd" runat="server" CssClass="input-field" TextMode="Password" placeholder="请再次输入密码" ></asp:TextBox>
                            <asp:RequiredFieldValidator ID="ConfirmPwdRequiredValidator" runat="server" ControlToValidate="tbConfirmPwd" ErrorMessage="确认密码不能为空" ForeColor="Red" Display="Dynamic" InitialValue="" />
                            <asp:CompareValidator ID="ComparePwdValidator" runat="server" ControlToCompare="tbPwd" ControlToValidate="tbConfirmPwd" ErrorMessage="密码不匹配" ForeColor="Red" Display="Dynamic" />
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label for="tbUserName">用户名</label></td>
                        <td>
                            <asp:TextBox ID="tbUserName" runat="server" CssClass="input-field" placeholder="请输入用户名"></asp:TextBox>
                            <asp:RequiredFieldValidator ID="UserNameRequiredValidator" runat="server"
                                ControlToValidate="tbUserName" ErrorMessage="用户名不能为空" ForeColor="Red" Display="Dynamic" />
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label for="tbRealName">真实姓名</label></td>
                        <td>
                            <asp:TextBox ID="tbRealName" runat="server" CssClass="input-field" placeholder="请输入您的真实姓名"></asp:TextBox>
                            <asp:RequiredFieldValidator ID="tbRealNameValidator" runat="server" ErrorMessage="请输入姓名" Display="Dynamic" ControlToValidate="tbRealName"></asp:RequiredFieldValidator>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label for="tbGroup">企业/团队</label></td>
                        <td>
                            <div style="margin-bottom: 10px">
                                <asp:RadioButton ID="rbJoin" runat="server" Text="加入已注册企业/团队" AutoPostBack="true" GroupName="CompanyChoice" OnCheckedChanged="rblCompanyChoice_SelectedIndexChanged" Value="join" Checked="True" />
                                <asp:RadioButton ID="rbCreate" runat="server" Text="创建新企业/团队" AutoPostBack="true" GroupName="CompanyChoice" OnCheckedChanged="rblCompanyChoice_SelectedIndexChanged" Value="create" />
                            </div>
                            <div id="joinCompanySection" runat="server" visible="true">
                                <asp:TextBox ID="txtCompanySearch" runat="server" placeholder="输入企业名称关键字进行查询" CssClass="input-field" Width="85%"></asp:TextBox>
                                <asp:Button ID="btnSearchCompany" runat="server" Text="查询" OnClick="btnSearchCompany_Click" CssClass="right-btn" CausesValidation="False" />
                                <div style="max-height: 100px; overflow-y: auto; margin: 10px">
                                    <asp:Repeater ID="rptGroupList" runat="server" OnItemCommand="rptCompanyList_ItemCommand">
                                        <ItemTemplate>
                                            <div style="margin-bottom: 5px">
                                                <span style="margin-right: 20px;"><%# Eval("groupName") %></span>
                                                <asp:LinkButton runat="server" Text="确认" CommandArgument='<%# Eval("groupId") + "|" + Eval("groupName") %>' CssClass="fr" OnCommand="btnSelectCompany_Command" CausesValidation="False" />
                                            </div>
                                        </ItemTemplate>
                                    </asp:Repeater>
                                </div>
                                <div>
                                    <asp:Label ID="lb" runat="server" Text="企业/团队名称：" Width="30%"></asp:Label>
                                    <asp:HiddenField ID="txtGroupId" runat="server" />
                                    <asp:TextBox ID="txtGroupName" runat="server" CssClass="input-field" Width="65%" ReadOnly="True" Enabled="False"></asp:TextBox>
                                </div>
                            </div>

                            <div id="createCompanySection" runat="server" visible="false">
                                <asp:TextBox ID="txtNewCompanyName" runat="server" placeholder="请输入要创建的企业/团队名称" CssClass="input-field" Width="85%"></asp:TextBox>
                                <asp:Button ID="btnConfirmNewCompany" runat="server" Text="确认" OnClick="btnConfirmNewCompany_Click" CssClass="right-btn" CausesValidation="False" />
                                <asp:Label ID="lblCompanyCreationStatus" runat="server" ForeColor="Red"></asp:Label>
                            </div>

                            <asp:RequiredFieldValidator ID="GroupValidator" runat="server" ControlToValidate="txtNewCompanyName" ErrorMessage="企业/团队未选择"
                                ForeColor="Red" Display="Dynamic" />
                        </td>
                    </tr>
                    <tr>
                        <td colspan="2" align="center">
                            <a href="Login.aspx" style="margin-right: 40%">返回</a>
                            <asp:Button ID="btnSubmit" runat="server" Text="注册" CssClass="submit-button" OnClick="btnSubmit_Click" />
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </form>
    <script src="js/jquery.min.js"></script>
    <script>
        let countdownTime = 60; // 倒计时时间
        let countdownInterval;

        function sendCode() {
            const phoneNumber = document.getElementById('<%= tbPhone.ClientID %>').value;

            // 手机号格式验证
            if (!/^1[3-9]\d{9}$/.test(phoneNumber)) {
                alert("请输入有效的手机号码");
                return false;  // 阻止表单提交
            }

            // 启动倒计时
            startCountdown();

            // 使用 AJAX 向后端请求验证码
            $.ajax({
                type: "POST",
                url: "Register.aspx/SendVerificationCode",
                data: JSON.stringify({ phoneNumber: phoneNumber }),
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                success: function (response) {
                    const message = response.d;  // 获取返回的消息
                    alert(message);  // 弹出验证码发送成功的消息
                },
                error: function () {
                    alert("验证码发送失败");
                    resetButton();
                }
            });

            return false;  // 防止表单提交
        }

        function startCountdown() {
            let btn = document.getElementById('<%= btnSendCode.ClientID %>');
            btn.disabled = true;  // 禁用按钮
            btn.value = `${countdownTime}秒后重试`;

            countdownInterval = setInterval(function () {
                countdownTime--;
                if (countdownTime <= 0) {
                    clearInterval(countdownInterval);  // 停止倒计时
                    btn.disabled = false;  // 启用按钮
                    btn.value = "获取验证码";  // 恢复按钮文本
                    countdownTime = 60;  // 重置倒计时
                } else {
                    btn.value = `${countdownTime}秒后重试`;
                }
            }, 1000);
        }

        function resetButton() {
            let btn = document.getElementById('<%= btnSendCode.ClientID %>');
            btn.disabled = false;
            btn.value = "获取验证码";
            countdownTime = 60;  // 重置倒计时
        }

    </script>
</body>
</html>

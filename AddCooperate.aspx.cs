﻿using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Data.SqlClient;

public partial class ADD : System.Web.UI.Page
{

    int id;
    public const int AddCount = 1;
    string[] strInserts;
    public const string AETable = "Cooperation";
    const string ConnStr = " Data Source=" + InputParams.DataBaseServer + "; uid=" + InputParams.userName + "; pwd=" + InputParams.password + "; DATABASE=" + InputParams.dbName;

    //iHM为0时，不生成时分控件，为1时，生成时分控件
    //int iHM = 0; 
    int iHM = 1;

    public EnumField[][] EnumField()//1.号码待确认。2.号码确认，无联系人。3.已有联系人。4.已发资料 
                                     //5.已拜访过。6.已发资料。7.已看过供应商。8.试吃过。9.正在试用中。
                                     //11.正式合作中。12.流失的老客户。0.非目标客户。
    {
        EnumField[] enum0 ={new EnumField(0, "目标供应商"), new EnumField(1, "已有意向"), new EnumField(2, "已签合同"), 
                             new EnumField(3, "合作中"), new EnumField(4, "已暂停"), new EnumField(5, "已关闭")                    
        };

        EnumField[] enum1 ={new EnumField(0, "无执照资质"), new EnumField(1, "普通执照资质"), new EnumField(2, "盒饭"), 
                             new EnumField(3, "桶饭"), new EnumField(4, "盒饭 + 桶饭")
                            };
        EnumField[][] efTs ={ enum0,enum1 };
        return efTs;
    }

    public Field[] InitFields()
    {
        Field fld0 = new Field(0, "firm_name", "公司名称", 0, EnumFieldType.charType, 0, 0, "", 0, "");  
        Field fld1 = new Field(1, "area", "区域", 0, EnumFieldType.enumType, 0, 0, "", 0, "");
        Field fld2 = new Field(2, "telephone", "电话", 0, EnumFieldType.charType, 0, 0, "", 0, "");
        Field fld3 = new Field(3, "linkman", "联系人", 0, EnumFieldType.charType, 0, 0, "", 0, "");
        Field fld4 = new Field(4, "cooperate_state", "合作状态", 0, EnumFieldType.enumType, 0, 0, "", 0, "");
        Field fld5 = new Field(5, "profit", "返利", 0, EnumFieldType.numberType, 0, 0, "", 0, "");
        Field fld6 = new Field(6, "business_mode", "营业资质", 0, EnumFieldType.enumType, 0, 1, "", 0, "");
        Field fld7 = new Field(7, "address", "地址", 0, EnumFieldType.longcharType, 0, 0, "", 0, "");  
        Field fld8 = new Field(8, "remark", "备注", 0, EnumFieldType.longcharType, 0, 0, "", 0, "");
  
        Field[] flds1 ={ fld0, fld1, fld2, fld3, fld4, fld5, fld6,fld7,fld8};
       
        return flds1;
    }

    protected void Page_Load(object sender, EventArgs e)
    {
        if (Request.QueryString["id"] != null)
        {
            try
            {
                id = int.Parse(Request.QueryString["id"]);
                GeneAddMould();
            }
            catch
            {
            }
        }
        else
        {
            GeneAddMould();
        }
    }
    protected void btnAdd_Click(object sender, EventArgs e)
    {  
        string flag = "";
        flag = GetInsertStr();
        if (flag != "")
        {
            ClientScript.RegisterClientScriptBlock(this.GetType(), "info", "<script language=\"javascript\" type=\"text/javascript\">alert(\" " + flag + "没有选，请选择！\");</script>");

        }
        else
        {
            try
            {
                Add();
                ClientScript.RegisterClientScriptBlock(this.GetType(), "info", "<script language=\"javascript\" type=\"text/javascript\">alert(\"添加成功！\");window.location.href='AddCooperate.aspx';</script>");
                GeneAddMould();
            }
            catch (SqlException ex)
            {
                Response.Write(ex.ToString());
                ClientScript.RegisterClientScriptBlock(this.GetType(), "info", "<script language=\"javascript\" type=\"text/javascript\">alert(\"添加失败！\");window.location.href='AddCooperate.aspx';</script>");
            }
        }
    }

    protected void lbt_Command(object sender, CommandEventArgs e)
    {
        Field[] fields = InitFields();
        EnumField[][] efTs = EnumField();

        for (int addIndex = 0; addIndex < AddCount; addIndex++)
        {
            PlaceHolder pHolder = (PlaceHolder)div1.FindControl("placeHolder" + addIndex);
            for (int fieldIndex = 0; fieldIndex < fields.Length; fieldIndex++)
            {
                string strFldName = fields[fieldIndex].fieldName + addIndex;
                switch (fields[fieldIndex].fieldType)
                {
                    case EnumFieldType.enumType:
                        string str1 = "ddl" + strFldName;
                        DropDownList ddl = (DropDownList)pHolder.FindControl(str1);
                        Session[str1] = ddl.Text;
                        continue;

                    case EnumFieldType.boolType:
                        string str2 = "rb" + strFldName + "1";
                        if (((RadioButton)pHolder.FindControl(str2)).Checked == true)
                        {
                            Session[str2] = 1;
                        }
                        else
                        {
                            Session[str2] = 0;
                        }
                        continue;

                    case EnumFieldType.dateType:
                        string str3 = "tbx" + strFldName;
                        Session[str3] = ((TextBox)(pHolder.FindControl(str3))).Text.Trim().ToString();
                        string str3H = "ddl" + strFldName + "H";
                        string str3M = "ddl" + strFldName + "M";
                        Session[str3H] = ((DropDownList)(pHolder.FindControl(str3H))).Text.Trim().ToString();
                        Session[str3M] = ((DropDownList)(pHolder.FindControl(str3M))).Text.Trim().ToString();
                        continue;

                    default:
                        string str4 = "tbx" + strFldName;
                        Session[str4] = ((TextBox)(pHolder.FindControl(str4))).Text.Trim().ToString();
                        continue;
                }
            }
        }
        Session["supPage"] = "AddTask.aspx";
        string strDire = e.CommandArgument.ToString() + "?tbxName=" + e.CommandName;
        Response.Redirect(strDire);
    }

    /// <summary>
    /// 生成添加模板
    /// </summary>
    /// <param name="placeHolder"></param>
    /// <param name="fields"></param>
    public void GeneAddMould()
    {
        div1.Controls.Clear();
        Field[] fields = InitFields();
        EnumField[][] efTs = EnumField();
        strInserts = new string[5];
        for (int addIndex = 0; addIndex < AddCount; addIndex++)
        {
            PlaceHolder placeHolder = new PlaceHolder();
            placeHolder.ID = "placeHolder" + addIndex;
            div1.Controls.Add(placeHolder);
            PlaceHolder pHolder = (PlaceHolder)div1.FindControl("placeHolder" + addIndex);
            if (addIndex != 0)
            {
                Literal ltlHR = new Literal();
                ltlHR.Text = "<HR/>";
                placeHolder.Controls.Add(ltlHR);
            }

            Literal ltlTag = new Literal();
            ltlTag.Text = "第" + (addIndex + 1) + "条";
            placeHolder.Controls.Add(ltlTag);

            for (int fieldIndex = 0; fieldIndex < fields.Length; fieldIndex++)
            {
                //生成换行符
                Label lbl1 = new Label();
                lbl1.Text = "<br/>";
                pHolder.Controls.Add(lbl1);

                //生成字段标签，即显示名称
                Label lblTag = new Label();
                lblTag.Text = fields[fieldIndex].fieldShowName + "：" + " ";
                lblTag.ForeColor = System.Drawing.ColorTranslator.FromHtml(InputParams.lblFieldShowNameColor);
                pHolder.Controls.Add(lblTag);

                string strFldName = fields[fieldIndex].fieldName + addIndex;
                switch (fields[fieldIndex].fieldType)
                {
                    case EnumFieldType.charType:
                        TextBox tbx1 = new TextBox();
                        string str1 = "tbx" + strFldName;
                        tbx1.ID = "tbx" + strFldName;
                        tbx1.Width = InputParams.tbxCharLength;
                        tbx1.Height = InputParams.tbxHeight;
                        tbx1.TextMode = TextBoxMode.MultiLine;
                        tbx1.Wrap = true;
                        tbx1.Style.Add("overflow", "hidden");
                        if (id == 1)
                        {
                            tbx1.Text = Session[str1].ToString();
                        }
                        pHolder.Controls.Add(tbx1);
                        ((TextBox)pHolder.FindControl("tbx" + strFldName)).Attributes.Add("onMouseOver", "this.style.backgroundColor='" + InputParams.tbxOnmouseoverColor + "';");
                        ((TextBox)pHolder.FindControl("tbx" + strFldName)).Attributes.Add("onMouseOut", "this.style.backgroundColor='" + InputParams.tbxOnmouseoutColor + "';");
                        continue;

                    case EnumFieldType.longcharType:
                        TextBox tbx2 = new TextBox();
                        string str2 = "tbx" + strFldName;
                        tbx2.ID = "tbx" + strFldName;
                        tbx2.Width = InputParams.tbxLongCharLength;
                        tbx2.Height = InputParams.tbxLongCharHeight;
                        tbx2.TextMode = TextBoxMode.MultiLine;
                        //tbx2.Wrap = true;
                        //tbx2.Style.Add("overflow", "hidden");
                        if (id == 1)
                        {
                            tbx2.Text = Session[str2].ToString();
                        }
                        pHolder.Controls.Add(tbx2);
                        ((TextBox)pHolder.FindControl("tbx" + strFldName)).Attributes.Add("onMouseOver", "this.style.backgroundColor='" + InputParams.tbxOnmouseoverColor + "';");
                        ((TextBox)pHolder.FindControl("tbx" + strFldName)).Attributes.Add("onMouseOut", "this.style.backgroundColor='" + InputParams.tbxOnmouseoutColor + "';");
                        continue;

                    case EnumFieldType.numberType:
                        TextBox tbx3 = new TextBox();
                        string str3 = "tbx" + strFldName;
                        tbx3.ID = "tbx" + strFldName;
                        tbx3.Width = InputParams.tbxNumberLength;
                        tbx3.Height = InputParams.tbxHeight;
                        if (id == 1)
                        {
                            tbx3.Text = Session[str3].ToString();
                        }
                        pHolder.Controls.Add(tbx3);
                        ((TextBox)pHolder.FindControl("tbx" + strFldName)).Attributes.Add("onMouseOver", "this.style.backgroundColor='" + InputParams.tbxOnmouseoverColor + "';");
                        ((TextBox)pHolder.FindControl("tbx" + strFldName)).Attributes.Add("onMouseOut", "this.style.backgroundColor='" + InputParams.tbxOnmouseoutColor + "';");                
                        continue;

                    case EnumFieldType.doubleType:
                        TextBox tbx13 = new TextBox();
                        string str13 = "tbx" + strFldName;
                        tbx13.ID = "tbx" + strFldName;
                        tbx13.Width = InputParams.tbxNumberLength;
                        tbx13.Height = InputParams.tbxHeight;
                        if (id == 1)
                        {
                            tbx13.Text = Session[str13].ToString();
                        }
                        pHolder.Controls.Add(tbx13);
                        ((TextBox)pHolder.FindControl("tbx" + strFldName)).Attributes.Add("onMouseOver", "this.style.backgroundColor='" + InputParams.tbxOnmouseoverColor + "';");
                        ((TextBox)pHolder.FindControl("tbx" + strFldName)).Attributes.Add("onMouseOut", "this.style.backgroundColor='" + InputParams.tbxOnmouseoutColor + "';");
                        continue;


                    case EnumFieldType.dateType:
                        TextBox tbx4 = new TextBox();
                        string str4 = "tbx" + strFldName;
                        tbx4.Text = DateTime.Now.ToShortDateString().ToString();
                        tbx4.ID = "tbx" + strFldName;
                        tbx4.Width = InputParams.tbxDateLength;
                        tbx4.Height = InputParams.tbxHeight;
                        if (id == 1)
                        {
                            tbx4.Text = Session[str4].ToString();
                        }
                        pHolder.Controls.Add(tbx4);
                        ((TextBox)pHolder.FindControl("tbx" + strFldName)).Attributes.Add("onClick", "javascript:calendar()");
                        ((TextBox)pHolder.FindControl("tbx" + strFldName)).Attributes.Add("onClick", "javascript:calendar()");

                        string str4H = "ddl" + strFldName + "H";
                        string str4M = "ddl" + strFldName + "M";

                        if (iHM == 1)
                        {
                            Literal ltlSpace1 = new Literal();
                            ltlSpace1.Text = "&nbsp;";
                            placeHolder.Controls.Add(ltlSpace1);

                            DropDownList ddlH = new DropDownList();
                            ddlH.ID = "ddl" + strFldName + "H";
                            ddlH.Width = InputParams.ddlTimeWidth;
                            for (int iH = 0; iH < 24; iH++)
                            {
                                ListItem li = new ListItem();
                                li.Text = iH.ToString();
                                li.Value = iH.ToString();
                                if ((id == 1) && (Session[str4H] != null) && (int.Parse(Session[str4H].ToString()) == iH))
                                {
                                    li.Selected = true;

                                }
                                ddlH.Items.Add(li);
                            }

                            placeHolder.Controls.Add(ddlH);

                            Label lblH = new Label();
                            lblH.Text = "时";
                            placeHolder.Controls.Add(lblH);

                            DropDownList ddlM = new DropDownList();
                            ddlM.ID = "ddl" + strFldName + "M";
                            ddlM.Width = InputParams.ddlTimeWidth;

                            for (int iM = 0; iM < 60; iM++)
                            {
                                ListItem li = new ListItem();
                                li.Text = iM.ToString();
                                li.Value = iM.ToString();
                                if ((id == 1) && (Session[str4M] != null) && (int.Parse(Session[str4M].ToString()) == iM))
                                {
                                    li.Selected = true;
                                }
                                ddlM.Items.Add(li);
                            }

                            placeHolder.Controls.Add(ddlM);

                            Label lblM = new Label();
                            lblM.Text = "分";
                            placeHolder.Controls.Add(lblM);
                        }

                        continue;

                    case EnumFieldType.boolType:
                        RadioButton rbY = new RadioButton();
                        RadioButton rbN = new RadioButton();
                        string strY = "rb" + strFldName + "1";
                        string strN = "rb" + strFldName + "2";
                        rbY.ID = "rb" + strFldName + "1";
                        rbY.GroupName = "gn" + strFldName;
                        rbY.Text = "是";
                        if (fields[fieldIndex].fieldName == "interviewee_sex")
                        {
                            rbY.Text = "男";
                        }
                        rbY.ForeColor = System.Drawing.ColorTranslator.FromHtml(InputParams.rbYNColor);
                        rbN.ID = "rb" + strFldName + "2";
                        rbN.GroupName = "gn" + strFldName;
                        rbN.Text = "否";
                        if (fields[fieldIndex].fieldName == "interviewee_sex")
                        {
                            rbN.Text = "女";
                        }
                        rbN.ForeColor = System.Drawing.ColorTranslator.FromHtml(InputParams.rbYNColor);
                        pHolder.Controls.Add(rbY);
                        pHolder.Controls.Add(rbN);

                        if (id == 1)
                        {
                            if (Session[strY].ToString() != null)
                            {
                                if (Session[strY].ToString() == "1")
                                {
                                    ((RadioButton)pHolder.FindControl(strY)).Checked = true;
                                }
                                else
                                {
                                    ((RadioButton)pHolder.FindControl(strN)).Checked = true;

                                }
                            }
                        }
                        continue;

                    case EnumFieldType.picType:
                        TextBox tbx5 = new TextBox();
                        string str5 = "tbx" + strFldName;
                        tbx5.ID = "tbx" + strFldName;
                        tbx5.Width = InputParams.tbxCharLength;
                        tbx5.Height = InputParams.tbxHeight;
                        tbx5.TextMode = TextBoxMode.MultiLine;
                        tbx5.Wrap = true;
                        tbx5.Style.Add("overflow", "hidden");

                        if (id == 1)
                        {
                            tbx5.Text = Session[str5].ToString();
                        }
                        pHolder.Controls.Add(tbx5);
                        ((TextBox)pHolder.FindControl("tbx" + strFldName)).Attributes.Add("onMouseOver", "this.style.backgroundColor='" + InputParams.tbxOnmouseoverColor + "';");
                        ((TextBox)pHolder.FindControl("tbx" + strFldName)).Attributes.Add("onMouseOut", "this.style.backgroundColor='" + InputParams.tbxOnmouseoutColor + "';");
                        continue;

                    case EnumFieldType.enumType:
                        DropDownList ddl = new DropDownList();
                        string str6 = "ddl" + strFldName;
                        ddl.ID = "ddl" + strFldName;
                        int enumT = fields[fieldIndex].enumTag;
                        ddl.Width = InputParams.ddlWidth;
                        ddl.AutoPostBack = false;
                        ListItem liTop = new ListItem();
                        liTop.Text = " 请选择 ";
                        ddl.Items.Add(liTop);

                        if (fields[fieldIndex].fieldName == "area")
                        {
                            SqlConnection conn1 = new SqlConnection(ConnStr);

                            ddl.ClearSelection();
                            //ddl.ID = "ddl" + strFldName;
                            SqlDataAdapter dap = new SqlDataAdapter("SELECT DISTINCT town_id,name  FROM ArTown", conn1);
                            DataTable dt = new DataTable();
                            dap.Fill(dt);
                            ddl.Items.Clear();
                            ddl.DataSource = dt;
                            ddl.DataTextField = "name";
                            ddl.DataValueField = "town_id";
                            ddl.DataBind();

                            ListItem li = new ListItem();
                            li.Text = "请选择";
                            li.Value = "请选择";
                            li.Selected = true;
                            ddl.Items.Add(li);

                        }
                        else
                        {

                            for (int enumLen = 0; enumLen < efTs[enumT].Length; enumLen++)
                            {
                                ListItem li = new ListItem();
                                li.Value = efTs[enumT][enumLen].enumItem.ToString();
                                li.Text = efTs[enumT][enumLen].itemDetail;
                                if (id == 1)
                                {
                                    if ((Session[str6] != null) && (efTs[enumT][enumLen].enumItem.ToString() == Session[str6].ToString()))
                                    {
                                        li.Selected = true;
                                    }
                                }
                                ddl.Items.Add(li);
                            }
                        }
                            pHolder.Controls.Add(ddl);
                            ((DropDownList)pHolder.FindControl("ddl" + strFldName)).Attributes.Add("onMouseOver", "this.style.backgroundColor='" + InputParams.tbxOnmouseoverColor + "';");
                            ((DropDownList)pHolder.FindControl("ddl" + strFldName)).Attributes.Add("onMouseOut", "this.style.backgroundColor='" + InputParams.tbxOnmouseoutColor + "';");
                       
                            continue;
                }
            }
        }
    }



    /// <summary>
    /// 获得添加记录的SQL插入字符串
    /// </summary>
    /// <param name="placeHolder"></param>
    /// <param name="fields"></param>
    /// <returns></returns>
    public string GetInsertStr()
    {
        Field[] fields = InitFields();
        EnumField[][] efTs = EnumField();

        for (int addIndex = 0; addIndex < AddCount; addIndex++)
        {
            PlaceHolder pHolder = (PlaceHolder)div1.FindControl("placeHolder" + addIndex);
            string strIns = "INSERT INTO " + AETable + "(";
            string strVal = " VALUES ( ";

            for (int fieldIndex = 0; fieldIndex < fields.Length; fieldIndex++)
            {
                if (fieldIndex != 0)
                {
                    strIns += "," + fields[fieldIndex].fieldName;
                    if (fieldIndex != fields.Length)
                    {
                        strVal += ",";
                    }
                }
                else
                {
                    strIns += fields[fieldIndex].fieldName;
                }

                string strFldName = fields[fieldIndex].fieldName + addIndex;

                switch (fields[fieldIndex].fieldType)
                {

                    case EnumFieldType.numberType:
                        if (((TextBox)pHolder.FindControl("tbx" + strFldName)).Text != "")
                        {
                           
                                strVal += int.Parse(((TextBox)pHolder.FindControl("tbx" + strFldName)).Text.ToString());
                          
                        }
                        else
                        {
                            strVal += "' '";
                        }
                        continue;
                    case EnumFieldType.doubleType:
                        if (((TextBox)pHolder.FindControl("tbx" + strFldName)).Text != "")
                        {

                            strVal += int.Parse(((TextBox)pHolder.FindControl("tbx" + strFldName)).Text.ToString());

                        }
                        else
                        {
                            strVal += "' '";
                        }
                        continue;


                    case EnumFieldType.dateType:
                        if (((TextBox)pHolder.FindControl("tbx" + strFldName)).Text != "")
                        {
                            string strDataTime = ((TextBox)pHolder.FindControl("tbx" + strFldName)).Text.Trim().ToString();
                            if (iHM == 1)
                            {
                                DropDownList ddlH = (DropDownList)(pHolder.FindControl("ddl" + strFldName + "H"));
                                DropDownList ddlM = (DropDownList)(pHolder.FindControl("ddl" + strFldName + "M"));
                                string strH = "00";
                                if (ddlH.Text != "")
                                {
                                    strH = ddlH.Text.Trim().ToString();
                                }
                                string strM = "00";
                                if (ddlM.Text != "")
                                {
                                    strM = ddlM.Text.Trim().ToString();
                                }
                                strDataTime += " " + strH + ":" + strM + ":" + "00";
                            }
                            DateTime dt = DateTime.Parse(strDataTime);
                            strVal += "'" + dt.ToString() + "'";
                        }
                        continue;

                    case EnumFieldType.enumType:
                        string strCho = "请选择";
                        string strDdl = ((DropDownList)pHolder.FindControl("ddl" + strFldName)).Text.Trim();
                        int iValue = 0;
                        string strText;
                        if (strDdl != strCho.Trim())
                        {
                             strVal +=((DropDownList)pHolder.FindControl("ddl" + strFldName)).SelectedValue.ToString().Trim();
                          }
                         else
                         {
                             return fields[fieldIndex].fieldShowName.ToString();
                             // strVal += iValue.ToString();
                          }
                                continue;

                    case EnumFieldType.boolType:


                        if (((RadioButton)pHolder.FindControl("rb" + strFldName + "1")).Checked)
                        {
                            strVal += "1";
                        }
                        else if (((RadioButton)pHolder.FindControl("rb" + strFldName + "2")).Checked)
                        {
                            strVal += "0";
                        }
                        else
                            return fields[fieldIndex].fieldShowName.ToString();
                        continue;

                    default:
                        if (((TextBox)pHolder.FindControl("tbx" + strFldName)).Text != "")
                        {
                            strVal += "'" + ((TextBox)pHolder.FindControl("tbx" + strFldName)).Text.Trim() + "'";
                        }
                        else
                        {
                            strVal += "' '";
                        }
                        continue;
                }
            }
            strIns += ",create_time,last_follow_time,creator_id,executor_id)";
            strVal += ",'" + DateTime.Now.ToString() + "','" + DateTime.Now.ToString() + "'," + Session["user_id"].ToString() + "," + Session["user_id"].ToString() + ")";
            strInserts[addIndex] = strIns + strVal;
        }

        return "";
    }

    /// <summary>
    /// 向数据库添加记录
    /// </summary>
    /// <param name="strInsert"></param>
    public void Add()
    {
        SqlConnection conn = new SqlConnection(ConnStr);

        if (conn.State.ToString() == "Closed")
        {
            conn.Open();
        }
        for (int addIndex = 0; addIndex < AddCount; addIndex++)
        {
            SqlCommand comm = new SqlCommand(strInserts[addIndex], conn);
            comm.ExecuteNonQuery();
        }
        if (conn.State.ToString() == "Open")
        {
            conn.Close();
        }
    }

}
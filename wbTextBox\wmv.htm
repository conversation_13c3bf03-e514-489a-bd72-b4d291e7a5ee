﻿<html><head><title>插入Windows Media</title>
<meta content="text/html; charset=utf-8" http-equiv=content-type>
<link rel="stylesheet" type="text/css" href="pop.css">
<script type="text/javascript" language="javascript" src="img.js"></script>
<script type="text/javascript">
function ok()
{
	var s=document.getElementById("path").value;
	var w=document.getElementById("width").value;
	var h=document.getElementById("height").value;
	if (s.length<5 )
	{
		alert("请填写文件地址");
	}else{
		s = s +"*"+ w +"*"+ h;
		if (opener){
			opener.WBTB_forwmv(s);
		}else{
			window.returnValue = s;
		}
		window.close();
	}
}
</script>
</head>

<body bgcolor="menu" topmargin=5 leftmargin=5>

<table border="0" cellPadding="3" cellSpacing="0" align="center">
<tr>
<td>
	<fieldset>
	<legend>Windows Media</legend>
	<table>
	<tr><td colspan=2>
	文件类型: avi, wmv, wma, asf, mov
	</td></tr>
	<tr><td colspan="2">
	地址: <input id="path" size="28" value="">
	</td></tr>
	<tr><td>
	宽度: <input id="width" size="7" value="480" onkeypress="event.returnValue=IsDigit();"></TD>
	<td align="right">
	高度: <input id="height" size="7" value="360" onkeypress="event.returnValue=IsDigit();"></TD>
	</tr>
	</table>
	</fieldset>
<tr>
<td align="right">
<button id="Ok" onclick="ok()" type="submit">确定</button>&nbsp; &nbsp;
<button onclick="window.close();">取消</button>
</td></tr>
</table>

</body></html>

<%@ Page Language="C#" AutoEventWireup="true" CodeFile="Default.aspx.cs" Inherits="See" %>

<%@ Register Assembly="WebControlLibrary1" Namespace="WebControlLibrary1" TagPrefix="cc1" %>
<html xmlns="http://www.w3.org/1999/xhtml">
<head id="Head1" runat="server">
    <title>�ͻ�����ϵͳ</title>
<%--    <meta http-equiv="Content-Type" content="text/html; charset=gb2312" />--%>
    <meta charset="gb2312">
    <meta name="viewport" content="width=device-width,height=device-height,initial-scale=1.0,user-scalable=no">
    <%--user-scalable=no ��ֹ�ֶ���������--%>
    <link href="Styles/winiechis.css" rel="stylesheet" type="text/css" />

<%--    <link href="Styles/datepicker-v1.20250113.css" rel="stylesheet" type="text/css">--%>

    <style type="text/css">
        body, table, tr, td {
            font-size: 12px;
            font-family: ����;
        }
        #lkbBrowseUser,#hlk4,#hlk5,#lkbApplyMange {
            font-size: 13px;
        }


        /*#tableBody {
            overflow-x: auto;
            max-width: 100%;*/  /* ʹ����������Ӧ��Ļ */
        /*}*/
        @media (min-width: 769px) {
        body, table, tr, td {
            font-size: 12px;
            font-family: ����;
            position: relative;
            left: 0%;     

        }
        #defTable {
            width:90%;
            margin-left:5%;
        }
        #btnAdd {
            width:80px;
        }
        .btn-more {
            text-decoration: none; 
            border: 1px solid #871f78;  
            display: inline-block; 
            border-radius: 10px;
            background-color: #E6E8FA; 
        }
        }

        @media (max-width: 768px) {
            body, table, tr, td {
                font-size: 12px;
                font-family: ����;
                max-width: 100%;
                overflow-x: auto;
                position: relative;
                margin:0;
                padding:0;
                /*padding:0 0.5px 0;*/
            }

            .pHo2 td {
                line-height: 2.0; /*�и�*/
                white-space: nowrap; /*�Զ�����*/
                /*writing-mode: vertical-lr;*/ /*��ֱ����*/
                vertical-align: middle;
                max-height: 100%;
            }

            .table-container {
                overflow-x: auto;
            }
            /* �����һ�� */
            /*td:nth-child(1) {
            position: sticky;
            left: 0;
            z-index: 2;
            background-color: #fff;
        }*/
            /* ����ڶ��� */
            /*#tableBody td:nth-child(2) {
            position: sticky;
            left: 44px;
            z-index: 1;
            background-color: #E0FFFF;
        }*/
            .btn-logOut {
                font-size: 12pt;
            }

            .btn-more {
/*                position: absolute;*/
                display:inline;
                border-radius: 3vw;
                font-size: 9.7pt; /* �������� */
                font-weight: bold;
                padding: 1% 1% 0.5% 2%;
                width: 22.6vw; /* �̶����ȣ���ѡ�� */
                height: 4.7vw; /* �̶��߶ȣ���ѡ�� */
                margin-top:-1%;
                border: 1px solid #767676;
                float:right;
                text-decoration: none; 
                border: 1px solid #871f78; 
                display: inline-block; 
                background-color: #E6E8FA;  
            }
            #btnSearch {
                font-size: 10pt; 
                width: 18vw;
                height: 5.8vw;
            }
            #btnAdd {
                font-size: 10pt; 
                width: 18vw;
                height: 5.8vw;
            }
            #lkbBrowseUser, #hlk4, #hlk5, #lkbApplyMange {
                font-size: 21px;
            }
            #form1 {
                border: none;
            }

            #defTable, #defTable th, #defTable td {
                width: 100%;
                border: none; /* ǿ���Ƴ����б߿� */
                border-spacing :0;
                margin: 0; 
                padding: 0;
                outline: 0;
                box-shadow: none;
                margin-top:-5%;
            }
            #titleTd {
                font-size:12px;
            }

        }
    </style>
</head>
<%--����--%>

<script src="js/time.js" type="text/javascript"></script>

<%--"ȫѡ"��ѡ��ѡ��Ĵ�������--%>

<script language="javascript" type="text/javascript">
    function selectAll(obj) {
        var theTable = obj.parentElement.parentElement.parentElement;
        var i;
        var j = obj.parentElement.cellIndex;

        for (i = 0; i < theTable.rows.length; i++) {
            var objCheckBox = theTable.rows[i].cells[j].firstChild;
            if (objCheckBox.checked != null) objCheckBox.checked = obj.checked;
        }
    }
    //"����ɸѡ����"��ť��������
	function toggleConditions() {
		var elements = document.getElementsByClassName('additional-condition');
		for (var i = 0; i < elements.length; i++) {
			elements[i].style.display = (elements[i].style.display === 'none') ? 'inline' : 'none';
		}
		document.querySelector('.btn-more').textContent =
			(elements[0].style.display === 'none') ? '����ɸѡ����' : '����ɸѡ����';
    }
    
</script>

<body>


    <form id="form1" method="post" runat="server">
        <div id="div1" runat="server">
            &nbsp;
        </div>
        <table id="defTable"  >
            <tr>
                <td colspan="2" style="text-align: center; height: 50px;">
                    <strong><span style="font-size: 16pt; color: #0000ff">�ͻ�����ϵͳ<br />
                    </span></strong><span style="color: blue"><span style="font-size: 16pt"></span></span>
                    <hr style="width: auto; font-size: 12pt;" />
                </td>
            </tr>
            <tr style="font-size: 12pt">
                <asp:Panel ID="pnlAdminTd" runat="server">
                 <td rowspan="0" style="vertical-align: top;">
                    <div runat="server" ID="phAdminLinksDesk"> 
                    <br />
                    <br />
                    <br />
                    <asp:LinkButton ID="LinkButton1" Font-Size="12pt" Font-Underline="false" ForeColor="blue" Font-Bold="true" Text="�鿴�û�"
                         runat="server" OnClick="lkbBrowseUser_Click" /><br />
                    <br />
                    <br />
                    <asp:HyperLink ID="HyperLink1" Font-Size="12pt" Font-Underline="False" ForeColor="Blue" Font-Bold="true" NavigateUrl="AddUser.aspx"
                         Text="�����û�" runat="server" Width="88px" /><br />
                    <br />
                    <br />
                    <asp:HyperLink ID="HyperLink2" Font-Size="12pt" Font-Underline="False" ForeColor="Blue" Font-Bold="true" NavigateUrl="AddUsers.aspx"
                         Text="��������" runat="server" Width="99px" /><br />
                    <br />
                    <br />
                    <asp:HyperLink ID="HyperLink3" Font-Size="12pt" Font-Underline="False" ForeColor="Blue" Font-Bold="true" NavigateUrl="~/admin/ApplyManage.aspx"
                         Text="��������" runat="server" Width="99px" /><br />
                    <br />
                    <br />
                    </div> 
                </td>
                </asp:Panel>
                <td id="" style="width: auto; height: 55px;padding:0 5px 0;">
                    <span style="font-size: 12pt; color: #0000ff">��ӭ����</span><asp:Label
                        ID="lblWel" runat="server" Font-Size="12pt"></asp:Label>&nbsp;
                        <asp:HyperLink ID="hlk7" Font-Underline="False" ForeColor="Blue" NavigateUrl="Logout.aspx"
                            Text="ע��" runat="server" Font-Size="12pt" Height="13px" Width="34px" CssClass="btn-logOut"/>
                    <div style="float:right">
                        ����,
                        <asp:Label ID="lbUserRole" runat="server" Text="��ͨ��Ա"></asp:Label>
                        &nbsp;
                            <asp:LinkButton ID="lkBtnApplyAdmin" runat="server"  OnClick="lkBtnApplyAdmin_Click">�����Ϊ����Ա</asp:LinkButton>
                    </div>
                    <br />

                    <asp:PlaceHolder ID="placeHolder1" runat="server" />
       <%--             <div>
                        <a href="#" id="btnMore" class="btn-more" onclick="toggleConditions(); return false;">
                        <span class="text">����ɸѡ����</span>
                             </a>
                    </div>--%>
                    <div id="div_executor" runat="server">
                    <br />
                        <asp:Label ID="Label1" runat="server" Text="������:"></asp:Label>
                        <asp:TextBox ID="TextBox1" runat="server" Width="250px"></asp:TextBox>
                        <asp:LinkButton ID="ChooseUsers" runat="server" OnClick="ChooseUsers_Click">����</asp:LinkButton>
                    </div>
                </td>

            </tr>
            <tr style="font-size: 12pt">
                <td style="height: 60px;padding:0 5px 0;">
                    <asp:Button ID="btnSearch" runat="server" Text="��ʼ����" OnClick="btnSearch_Click" 
                        Style="text-decoration: none; border: 1px solid #871f78;  display: inline-block; border-radius: 10px;
                            background-color: #E6E8FA; "/>&nbsp;
                        <hr style="width: auto" />
                </td>
            </tr>
            <tr style="font-size: 12pt">
                <td style="height: 24px; width: auto;padding:0 5px 0;">
                    <asp:Button ID="btnAdd"  runat="server" Text="���ӿͻ�" OnClick="btnAdd_Click" 
                         Style="text-decoration: none; border: 1px solid #871f78;  display: inline-block; border-radius: 10px;
                            background-color: #E6E8FA; "/>

                    <sapn runat="server" id="hideLinks">
                    &nbsp;
                    <asp:Button ID="btnDelete" Width="80" runat="server" Text="����ɾ��" OnClick="btnDelete_Click" CssClass="btn-large" />
                    &nbsp;
                    <asp:Button ID="btnModify" Width="80" runat="server" Text="�����޸�" OnClick="btnModify_Click" CssClass="btn-large" />
                    </sapn>
                  
                </td>
                <td>

                </td>
            </tr>

            <tr style="width: 79px; vertical-align: top; " >
                <td runat="server" ID="phAdminLinks">
                   <div runat="server" >
                    <br/>
                    <asp:LinkButton ID="lkbBrowseUser" Font-Underline="false" ForeColor="blue" Text="�鿴�û�"
                         runat="server" OnClick="lkbBrowseUser_Click" />&nbsp;&nbsp;

                    <asp:HyperLink ID="hlk4" Font-Underline="False" ForeColor="Blue" NavigateUrl="AddUser.aspx"
                         Text="�����û�" runat="server" Width="88px" />&nbsp;&nbsp;

                    <asp:HyperLink ID="hlk5" Font-Underline="False" ForeColor="Blue" NavigateUrl="AddUsers.aspx"
                         Text="��������" runat="server" Width="99px" />&nbsp;&nbsp;

                    <asp:HyperLink ID="lkbApplyMange" Font-Underline="False" ForeColor="Blue" NavigateUrl="~/admin/ApplyManage.aspx"
                         Text="��������" runat="server" Width="99px" />
                    <br/>
                    <br/>
                   </div>
                </td>
            </tr>

            <tr style="font-size: 12pt">
                <td style="height: 20px; width: auto;">
                    <br/>
                    <cc1:WebCustomControl1 ID="WebCustomControl1_1" OnChangePageClick="pager_Click" runat="server" ></cc1:WebCustomControl1>
                    <cc1:WebCustomControl1 ID="WebCustomControl1_3" OnChangePageClick="pager_Click3" runat="server" Visible="False"></cc1:WebCustomControl1>
                </td>
            </tr>
            <tr valign="top" align="center" style="font-size: 12pt">
                <td class="pHo2" valign="top" align="center" style="height: auto; width: auto;">
                    <asp:PlaceHolder ID="placeHolder2" runat="server" />
                </td>
            </tr>
            <tr style="font-size: 12pt" <%--valign="top"--%>>
<%--                <td rowspan="1" style="vertical-align: top; width: 79px"></td>--%>
                <td style="width: auto; height: auto; /*text-align: left*/" <%--valign="top"--%>>
                    <div runat="server" >
                    <cc1:WebCustomControl1 ID="WebCustomControl1_2" runat="server" OnChangePageClick="pager_Click2" />
                     <br/>
                     <br/>

                    </div>
                    <br />
                </td>
            </tr>

            <tr>
                <td id="changePageButtons" runat="server">
                    <div  style="position: fixed; bottom: 0; left: 0; width: 100%; text-align: center; background-color: white; padding: 10px; z-index: 3;">
                    <asp:LinkButton ID="LinkButton2" Font-Underline="false" ForeColor="blue" Font-Bold="true" Text="[��ҳ]" runat="server" OnClick="pager_Click3" 
                        Style="text-decoration: none; border: 1px solid #808080; padding: 2px 8px; display: inline-block; border-radius: 10px;
                            background-color: #ffffff; "/>
                     &nbsp;
                    <asp:LinkButton ID="LinkButton5" Font-Underline="false" ForeColor="blue" Font-Bold="true" Text="[βҳ]" runat="server" OnClick="pager_Click4" 
                        Style="text-decoration: none; border: 1px solid #808080; padding: 2px 8px; display: inline-block; border-radius: 10px;
                            background-color: #ffffff; "/>
                     &nbsp;
                    <asp:LinkButton ID="LinkButton3" Font-Underline="false" ForeColor="blue" Font-Bold="true" Text="[��һҳ]" runat="server" OnClick="pager_Click5" 
                        Style="text-decoration: none; border: 1px solid #808080; padding: 2px 8px; display: inline-block; border-radius: 10px;
                            background-color: #ffffff; "/>
                     &nbsp;
                    <asp:LinkButton ID="LinkButton4" Font-Underline="false" ForeColor="blue" Font-Bold="true" Text="[��һҳ]" runat="server" OnClick="pager_Click6" 
                        Style="text-decoration: none; border: 1px solid #808080; padding: 2px 8px; display: inline-block; border-radius: 10px;
                            background-color: #ffffff; "/>
                    </div>
                </td>

            </tr>
        </table>
    </form>
    <script>

    </script>
</body>
</html>

<%@ Page Language="C#" AutoEventWireup="true" CodeFile="Default.aspx.cs" Inherits="See" %>

<%@ Register Assembly="WebControlLibrary1" Namespace="WebControlLibrary1" TagPrefix="cc1" %>
<html xmlns="http://www.w3.org/1999/xhtml">
<head id="Head1" runat="server">
    <title>�ͻ�����ϵͳ</title>
<%--    <meta http-equiv="Content-Type" content="text/html; charset=gb2312" />--%>
    <meta charset="gb2312">
    <meta name="viewport" content="width=device-width,height=device-height,initial-scale=1.0,user-scalable=no">
    <%--user-scalable=no ��ֹ�ֶ���������--%>
    <link href="Styles/winiechis.css" rel="stylesheet" type="text/css" />
    <link href="Styles/modern.css" rel="stylesheet" type="text/css" />
    <!-- 引入现代化图标库 -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- 引入Google字体 -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

<%--    <link href="Styles/datepicker-v1.20250113.css" rel="stylesheet" type="text/css">--%>

    <style type="text/css">
        /* 现代化基础样式 */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 14px;
            line-height: 1.6;
            color: #2d3748;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        /* 主容器样式 */
        #form1 {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            margin: 20px;
            overflow: hidden;
        }

        /* 表格样式 */
        table, tr, td {
            font-family: inherit;
            font-size: inherit;
        }

        #defTable {
            width: 100%;
            border-collapse: collapse;
        }

        /* 头部样式 */
        .header-section {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }

        .header-section h1 {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        /* 按钮基础样式 */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 12px 24px;
            border: none;
            border-radius: 12px;
            font-size: 14px;
            font-weight: 500;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.3s ease;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(79, 70, 229, 0.4);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(79, 70, 229, 0.6);
        }

        .btn-secondary {
            background: #f8fafc;
            color: #475569;
            border: 2px solid #e2e8f0;
        }

        .btn-secondary:hover {
            background: #e2e8f0;
            transform: translateY(-1px);
        }

        .btn-danger {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
        }

        .btn-danger:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
        }

        /* 桌面端样式 */
        @media (min-width: 769px) {
            #form1 {
                max-width: 1400px;
                margin: 20px auto;
            }

            .btn-more {
                background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 8px;
                font-size: 13px;
                font-weight: 500;
            }

            .btn-more:hover {
                transform: translateY(-1px);
                box-shadow: 0 4px 15px rgba(139, 92, 246, 0.4);
            }

            .admin-links {
                display: flex;
                gap: 20px;
                flex-wrap: wrap;
            }

            .admin-links a {
                color: #4f46e5;
                text-decoration: none;
                font-weight: 500;
                padding: 8px 16px;
                border-radius: 8px;
                transition: all 0.3s ease;
            }

            .admin-links a:hover {
                background: #eef2ff;
                transform: translateY(-1px);
            }
        }

        /* 移动端样式 */
        @media (max-width: 768px) {
            body {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                margin: 0;
                padding: 10px;
            }

            #form1 {
                margin: 0;
                border-radius: 15px;
            }

            .header-section h1 {
                font-size: 22px;
            }

            .pHo2 td {
                line-height: 1.8;
                white-space: nowrap;
                vertical-align: middle;
                padding: 8px 4px;
            }

            .table-container {
                overflow-x: auto;
                border-radius: 12px;
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            }

            .btn-logOut {
                font-size: 12px;
                padding: 6px 12px;
                border-radius: 6px;
            }

            .btn-more {
                background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
                color: white;
                border: none;
                border-radius: 20px;
                font-size: 12px;
                font-weight: 600;
                padding: 8px 16px;
                width: auto;
                height: auto;
                margin: 5px;
                box-shadow: 0 4px 15px rgba(139, 92, 246, 0.3);
                float: right;
            }

            .btn-more:hover {
                transform: translateY(-1px);
                box-shadow: 0 6px 20px rgba(139, 92, 246, 0.4);
            }

            #btnSearch, #btnAdd {
                font-size: 12px;
                width: auto;
                height: auto;
                min-width: 100px;
                padding: 10px 16px;
                margin: 5px;
            }

            .admin-links {
                display: flex;
                flex-wrap: wrap;
                gap: 15px;
                justify-content: center;
                padding: 15px;
            }

            .admin-links a {
                font-size: 16px;
                color: #4f46e5;
                text-decoration: none;
                font-weight: 500;
                padding: 10px 15px;
                background: rgba(255, 255, 255, 0.9);
                border-radius: 10px;
                box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
                transition: all 0.3s ease;
            }

            .admin-links a:hover {
                background: white;
                transform: translateY(-2px);
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
            }

            #defTable {
                width: 100%;
                border-spacing: 0;
            }

            #defTable td {
                padding: 15px 10px;
            }

            /* 固定底部导航 */
            .fixed-bottom-nav {
                position: fixed;
                bottom: 0;
                left: 0;
                right: 0;
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(10px);
                padding: 15px;
                box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
                z-index: 1000;
                display: flex;
                justify-content: space-around;
                gap: 10px;
            }

            .fixed-bottom-nav .btn {
                flex: 1;
                padding: 12px 8px;
                font-size: 12px;
                border-radius: 10px;
            }

            /* 为底部导航留出空间 */
            body {
                padding-bottom: 80px;
            }
        }

        /* 表格美化样式 */
        .data-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        }

        .data-table th {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            padding: 15px 12px;
            text-align: left;
            font-weight: 600;
            font-size: 14px;
            border: none;
        }

        .data-table td {
            padding: 12px;
            border-bottom: 1px solid #f1f5f9;
            font-size: 13px;
            color: #374151;
        }

        .data-table tr:hover {
            background: #f8fafc;
        }

        .data-table tr:last-child td {
            border-bottom: none;
        }

        /* 输入框美化 */
        input[type="text"], input[type="date"], select, textarea {
            padding: 8px 12px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
            background: white;
        }

        input[type="text"]:focus, input[type="date"]:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #4f46e5;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }

        /* 复选框美化 */
        input[type="checkbox"] {
            width: 18px;
            height: 18px;
            accent-color: #4f46e5;
        }

        /* 加载动画 */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #4f46e5;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 状态标签 */
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-pending { background: #fef3c7; color: #92400e; }
        .status-active { background: #d1fae5; color: #065f46; }
        .status-confirmed { background: #dbeafe; color: #1e40af; }
        .status-completed { background: #e0e7ff; color: #3730a3; }
        .status-inactive { background: #fee2e2; color: #991b1b; }

        /* 响应式表格 */
        @media (max-width: 768px) {
            .data-table {
                font-size: 12px;
            }

            .data-table th, .data-table td {
                padding: 8px 6px;
            }
        }
    </style>
</head>
<%--����--%>

<script src="js/time.js" type="text/javascript"></script>

<%--"ȫѡ"��ѡ��ѡ��Ĵ�������--%>

<script language="javascript" type="text/javascript">
    function selectAll(obj) {
        var theTable = obj.parentElement.parentElement.parentElement;
        var i;
        var j = obj.parentElement.cellIndex;

        for (i = 0; i < theTable.rows.length; i++) {
            var objCheckBox = theTable.rows[i].cells[j].firstChild;
            if (objCheckBox.checked != null) objCheckBox.checked = obj.checked;
        }
    }
    //"����ɸѡ����"��ť��������
	function toggleConditions() {
		var elements = document.getElementsByClassName('additional-condition');
		for (var i = 0; i < elements.length; i++) {
			elements[i].style.display = (elements[i].style.display === 'none') ? 'inline' : 'none';
		}
		document.querySelector('.btn-more').textContent =
			(elements[0].style.display === 'none') ? '����ɸѡ����' : '����ɸѡ����';
    }
    
</script>

<body>


    <form id="form1" method="post" runat="server">
        <div id="div1" runat="server">
            &nbsp;
        </div>
        <table id="defTable"  >
            <tr>
                <td colspan="2" class="header-section">
                    <div style="display: flex; align-items: center; justify-content: center; gap: 15px;">
                        <span style="font-size: 32px; color: rgba(255,255,255,0.9);">👥</span>
                        <h1>客户管理系统</h1>
                        <span style="font-size: 32px; color: rgba(255,255,255,0.9);">📈</span>
                    </div>
                    <p style="margin-top: 10px; opacity: 0.9; font-size: 16px;">专业的客户关系管理平台</p>
                </td>
            </tr>
            <tr style="font-size: 12pt">
                <asp:Panel ID="pnlAdminTd" runat="server">
                 <td rowspan="0" style="vertical-align: top;">
                    <div runat="server" ID="phAdminLinksDesk"> 
                    <br />
                    <br />
                    <br />
                    <asp:LinkButton ID="LinkButton1" Font-Size="12pt" Font-Underline="false" ForeColor="blue" Font-Bold="true" Text="�鿴�û�"
                         runat="server" OnClick="lkbBrowseUser_Click" /><br />
                    <br />
                    <br />
                    <asp:HyperLink ID="HyperLink1" Font-Size="12pt" Font-Underline="False" ForeColor="Blue" Font-Bold="true" NavigateUrl="AddUser.aspx"
                         Text="�����û�" runat="server" Width="88px" /><br />
                    <br />
                    <br />
                    <asp:HyperLink ID="HyperLink2" Font-Size="12pt" Font-Underline="False" ForeColor="Blue" Font-Bold="true" NavigateUrl="AddUsers.aspx"
                         Text="��������" runat="server" Width="99px" /><br />
                    <br />
                    <br />
                    <asp:HyperLink ID="HyperLink3" Font-Size="12pt" Font-Underline="False" ForeColor="Blue" Font-Bold="true" NavigateUrl="~/admin/ApplyManage.aspx"
                         Text="��������" runat="server" Width="99px" /><br />
                    <br />
                    <br />
                    </div> 
                </td>
                </asp:Panel>
                <td style="width: auto; padding: 20px; background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);">
                    <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 15px;">
                        <div style="display: flex; align-items: center; gap: 10px;">
                            <span style="font-size: 24px; color: #4f46e5;">👤</span>
                            <span style="font-size: 16px; color: #1e293b; font-weight: 500;">欢迎回来，</span>
                            <asp:Label ID="lblWel" runat="server" Font-Size="16px" ForeColor="#4f46e5" Font-Bold="true"></asp:Label>
                        </div>
                        <div style="display: flex; align-items: center; gap: 15px;">
                            <div style="display: flex; align-items: center; gap: 8px; padding: 8px 12px; background: rgba(79, 70, 229, 0.1); border-radius: 8px;">
                                <span style="color: #4f46e5; font-size: 14px;">🛡️</span>
                                <span style="font-size: 14px; color: #4f46e5;">当前角色：</span>
                                <asp:Label ID="lbUserRole" runat="server" Text="普通用户" Font-Size="14px" ForeColor="#4f46e5" Font-Bold="true"></asp:Label>
                            </div>
                            <asp:LinkButton ID="lkBtnApplyAdmin" runat="server" OnClick="lkBtnApplyAdmin_Click"
                                CssClass="btn btn-secondary" style="font-size: 12px; padding: 6px 12px;"
                                Text="👤 申请管理员" />
                            <asp:HyperLink ID="hlk7" NavigateUrl="Logout.aspx" runat="server"
                                CssClass="btn btn-danger" style="font-size: 12px; padding: 6px 12px;"
                                Text="🚪 注销" />
                        </div>
                    </div>

                    <div style="margin-top: 20px; padding: 20px; background: white; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.05);">
                        <h3 style="color: #1e293b; margin-bottom: 15px; display: flex; align-items: center; gap: 8px;">
                            <span style="color: #4f46e5;">🔍</span>
                            搜索条件
                        </h3>
                        <asp:PlaceHolder ID="placeHolder1" runat="server" />
       <%--             <div>
                        <a href="#" id="btnMore" class="btn-more" onclick="toggleConditions(); return false;">
                        <span class="text">����ɸѡ����</span>
                             </a>
                    </div>--%>
                        <div id="div_executor" runat="server" style="margin-top: 15px; padding: 15px; background: #f8fafc; border-radius: 8px; border-left: 4px solid #4f46e5;">
                            <div style="display: flex; align-items: center; gap: 10px; flex-wrap: wrap;">
                                <span style="color: #4f46e5;">🏷️</span>
                                <asp:Label ID="Label1" runat="server" Text="执行者:" Font-Size="14px" ForeColor="#374151" Font-Weight="500"></asp:Label>
                                <asp:TextBox ID="TextBox1" runat="server"
                                    style="flex: 1; min-width: 200px; padding: 8px 12px; border: 2px solid #e2e8f0; border-radius: 8px; font-size: 14px;"
                                    placeholder="选择执行者..."></asp:TextBox>
                                <asp:LinkButton ID="ChooseUsers" runat="server" OnClick="ChooseUsers_Click"
                                    CssClass="btn btn-secondary" style="font-size: 12px; padding: 8px 16px;"
                                    Text="👥 选择" />
                            </div>
                        </div>
                    </div>
                </td>

            </tr>
            <tr>
                <td style="padding: 20px; text-align: center; background: #f8fafc;">
                    <asp:Button ID="btnSearch" runat="server" Text="🔍 开始搜索" OnClick="btnSearch_Click"
                        CssClass="btn btn-primary" style="font-size: 16px; padding: 12px 30px; min-width: 150px;" />
                    <div style="margin-top: 15px; height: 1px; background: linear-gradient(to right, transparent, #e2e8f0, transparent);"></div>
                </td>
            </tr>
            <tr>
                <td style="padding: 20px; background: white;">
                    <div style="display: flex; gap: 15px; flex-wrap: wrap; align-items: center;">
                        <asp:Button ID="btnAdd" runat="server" Text="➕ 添加客户" OnClick="btnAdd_Click"
                            CssClass="btn btn-primary" style="font-size: 14px; padding: 10px 20px;" />

                        <span runat="server" id="hideLinks" style="display: flex; gap: 10px; flex-wrap: wrap;">
                            <asp:Button ID="btnDelete" runat="server" Text="🗑️ 批量删除" OnClick="btnDelete_Click"
                                CssClass="btn btn-danger" style="font-size: 14px; padding: 10px 20px;" />
                            <asp:Button ID="btnModify" runat="server" Text="✏️ 批量修改" OnClick="btnModify_Click"
                                CssClass="btn btn-secondary" style="font-size: 14px; padding: 10px 20px;" />
                        </span>

                        <div style="margin-left: auto; display: flex; align-items: center; gap: 10px; color: #64748b; font-size: 14px;">
                            <span>ℹ️</span>
                            <span>选择记录后进行批量操作</span>
                        </div>
                    </div>
                </td>
            </tr>

            <tr>
                <td runat="server" ID="phAdminLinks" style="padding: 20px; background: linear-gradient(135deg, #eef2ff 0%, #e0e7ff 100%);">
                    <div style="text-align: center;">
                        <h4 style="color: #4f46e5; margin-bottom: 20px; display: flex; align-items: center; justify-content: center; gap: 8px;">
                            <i class="fas fa-cog"></i>
                            管理员功能
                        </h4>
                        <div class="admin-links">
                            <asp:LinkButton ID="lkbBrowseUser" runat="server" OnClick="lkbBrowseUser_Click"
                                Text="👥 查看用户" />

                            <asp:HyperLink ID="hlk4" NavigateUrl="AddUser.aspx" runat="server"
                                Text="👤 添加用户" />

                            <asp:HyperLink ID="hlk5" NavigateUrl="AddUsers.aspx" runat="server"
                                Text="📥 批量导入" />

                            <asp:HyperLink ID="lkbApplyMange" NavigateUrl="~/admin/ApplyManage.aspx" runat="server"
                                Text="📋 申请管理" />
                        </div>
                    </div>
                </td>
            </tr>

            <tr>
                <td style="padding: 15px; background: #f8fafc; text-align: center;">
                    <div style="display: inline-block; background: white; padding: 10px 20px; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.05);">
                        <cc1:WebCustomControl1 ID="WebCustomControl1_1" OnChangePageClick="pager_Click" runat="server"></cc1:WebCustomControl1>
                        <cc1:WebCustomControl1 ID="WebCustomControl1_3" OnChangePageClick="pager_Click3" runat="server" Visible="False"></cc1:WebCustomControl1>
                    </div>
                </td>
            </tr>
            <tr>
                <td style="padding: 20px; background: white;">
                    <div class="table-container" style="background: white; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 15px rgba(0,0,0,0.05);">
                        <asp:PlaceHolder ID="placeHolder2" runat="server" />
                    </div>
                </td>
            </tr>
            <tr>
                <td style="padding: 15px; background: #f8fafc; text-align: center;">
                    <div style="display: inline-block; background: white; padding: 15px 25px; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.05);">
                        <cc1:WebCustomControl1 ID="WebCustomControl1_2" runat="server" OnChangePageClick="pager_Click2" />
                    </div>
                </td>
            </tr>

            <tr>
                <td id="changePageButtons" runat="server">
                    <div class="fixed-bottom-nav">
                        <asp:LinkButton ID="LinkButton2" runat="server" OnClick="pager_Click3" CssClass="btn btn-secondary"
                            Text="⏮️ 首页" />

                        <asp:LinkButton ID="LinkButton3" runat="server" OnClick="pager_Click5" CssClass="btn btn-secondary"
                            Text="⬅️ 上页" />

                        <asp:LinkButton ID="LinkButton4" runat="server" OnClick="pager_Click6" CssClass="btn btn-secondary"
                            Text="下页 ➡️" />

                        <asp:LinkButton ID="LinkButton5" runat="server" OnClick="pager_Click4" CssClass="btn btn-secondary"
                            Text="尾页 ⏭️" />
                    </div>
                </td>
            </tr>
        </table>
    </form>
    <script>
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 添加加载动画
            showLoadingAnimation();

            // 美化表格
            beautifyTables();

            // 添加按钮点击效果
            addButtonEffects();

            // 隐藏加载动画
            setTimeout(hideLoadingAnimation, 500);
        });

        // 显示加载动画
        function showLoadingAnimation() {
            const overlay = document.createElement('div');
            overlay.className = 'loading-overlay';
            overlay.innerHTML = '<div class="spinner"></div>';
            overlay.id = 'loadingOverlay';
            document.body.appendChild(overlay);
        }

        // 隐藏加载动画
        function hideLoadingAnimation() {
            const overlay = document.getElementById('loadingOverlay');
            if (overlay) {
                overlay.style.opacity = '0';
                setTimeout(() => overlay.remove(), 300);
            }
        }

        // 美化表格
        function beautifyTables() {
            const tables = document.querySelectorAll('table');
            tables.forEach(table => {
                if (!table.classList.contains('data-table')) {
                    table.classList.add('data-table');
                }
            });
        }

        // 添加按钮点击效果
        function addButtonEffects() {
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach(button => {
                button.addEventListener('click', function(e) {
                    // 创建波纹效果
                    const ripple = document.createElement('span');
                    const rect = this.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    const x = e.clientX - rect.left - size / 2;
                    const y = e.clientY - rect.top - size / 2;

                    ripple.style.cssText = `
                        position: absolute;
                        width: ${size}px;
                        height: ${size}px;
                        left: ${x}px;
                        top: ${y}px;
                        background: rgba(255, 255, 255, 0.5);
                        border-radius: 50%;
                        transform: scale(0);
                        animation: ripple 0.6s linear;
                        pointer-events: none;
                    `;

                    this.style.position = 'relative';
                    this.style.overflow = 'hidden';
                    this.appendChild(ripple);

                    setTimeout(() => ripple.remove(), 600);
                });
            });
        }

        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes ripple {
                to {
                    transform: scale(4);
                    opacity: 0;
                }
            }

            .fade-in-up {
                animation: fadeInUp 0.6s ease-out;
            }

            @keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
        `;
        document.head.appendChild(style);

        // 平滑滚动到顶部
        function scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }

        // 表单验证增强
        function validateForm() {
            const inputs = document.querySelectorAll('input[required], select[required]');
            let isValid = true;

            inputs.forEach(input => {
                if (!input.value.trim()) {
                    input.classList.add('is-invalid');
                    isValid = false;
                } else {
                    input.classList.remove('is-invalid');
                    input.classList.add('is-valid');
                }
            });

            return isValid;
        }

        // 显示提示消息
        function showAlert(message, type = 'info') {
            const alert = document.createElement('div');
            alert.className = `alert alert-${type} fade-in`;
            alert.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : type === 'danger' ? 'times-circle' : 'info-circle'}"></i>
                ${message}
                <button type="button" onclick="this.parentElement.remove()" style="float: right; background: none; border: none; font-size: 18px; cursor: pointer;">&times;</button>
            `;

            document.body.insertBefore(alert, document.body.firstChild);

            setTimeout(() => {
                if (alert.parentElement) {
                    alert.remove();
                }
            }, 5000);
        }
    </script>
</body>
</html>

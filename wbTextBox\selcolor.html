﻿<HTML>
<HEAD>
<TITLE>Choose Color</TITLE>
<meta http-equiv="content-type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="pop.css">

<script language="javascript">
var SelRGB = '#000000';
var DrRGB = '';
var SelGRAY = '120';
var oColorTable, oGrayTable;
var oSelColor, oShowColor, oRGB, oGRAY;

var hexch = new Array('0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F');

function ToHex(n)
{	var h, l;

	n = Math.round(n);
	l = n % 16;
	h = Math.floor((n / 16)) % 16;
	return (hexch[h] + hexch[l]);
}

function DoColor(c, l)
{
	var r, g, b;
  r = '0x' + c.substring(1, 3);
  g = '0x' + c.substring(3, 5);
  b = '0x' + c.substring(5, 7);
  
  if(l > 120)
  {
    l = l - 120;

    r = (r * (120 - l) + 255 * l) / 120;
    g = (g * (120 - l) + 255 * l) / 120;
    b = (b * (120 - l) + 255 * l) / 120;
  }else
  {
    r = (r * l) / 120;
    g = (g * l) / 120;
    b = (b * l) / 120;
  }

  return '#' + ToHex(r) + ToHex(g) + ToHex(b);
}

function EndColor()
{
	var i;

	if(DrRGB != SelRGB)
	{
		DrRGB = SelRGB;
		for(i = 0; i <= 30; i ++)
		oGrayTable.rows[i].bgColor = DoColor(SelRGB, 240 - i * 8);
	}
	
	if (document.all){
		oSelColor.value = DoColor(oRGB.innerText, oGRAY.innerText);
	}else{
		oSelColor.value = DoColor(oRGB.innerHTML, oGRAY.innerHTML);
	}
	oShowColor.bgColor = oSelColor.value;
}

function CtableClick(evnt)
{
	if (document.all){
		SelRGB=event.srcElement.bgColor.toUpperCase();
	}else{
		SelRGB=evnt.target.bgColor.toUpperCase();
	}
	EndColor();
}

function CtableOver(evnt)
{
  //SelRGB = e.srcElement.bgColor;
	if (document.all){
		oRGB.innerText = event.srcElement.bgColor.toUpperCase();
	}else{
		oRGB.innerHTML = evnt.target.bgColor.toUpperCase();
	}
	EndColor();
}

function CtableOut()
{
	if (document.all){
		oRGB.innerText = SelRGB;
	}else{
		oRGB.innerHTML = SelRGB;
	}
	EndColor();
}

function GtableClick(evnt)
{
	if (document.all){
		SelGRAY = event.srcElement.title;
	}else{
		SelGRAY = evnt.target.title;
	}
	EndColor();
}

function GtableOver(evnt)
{
	if(document.all){
		oGRAY.innerText = event.srcElement.title;
	}else{
		oGRAY.innerHTML = evnt.target.title;
	}
	EndColor();
}

function GtableOut()
{
	if (document.all){
		oGRAY.innerText = SelGRAY;
	}else{
		oGRAY.innerHTML = SelGRAY;
	}
	EndColor();
}

function SelColorBlur()
{
	SelRGB = oSelColor.value;
	if (document.all){
		oRGB.innerText = SelRGB;
	}else{
		oRGB.innerHTML = SelRGB;
	}
	EndColor();
}

function InitDocument()
{
	oSelColor=document.getElementById("SelColor");
	oShowColor=document.getElementById("ShowColor");
	oRGB=document.getElementById("RGB");
	oGRAY=document.getElementById("GRAY");
	
	oColorTable = document.getElementById("ColorTable");
	oGrayTable = document.getElementById("GrayTable");
	
	oColorTable.onclick=CtableClick;
	oColorTable.onmouseover=CtableOver;
	oColorTable.onmouseout=CtableOut;
	oGrayTable.onclick=GtableClick;
	oGrayTable.onmouseover=GtableOver;
	oGrayTable.onmouseout=GtableOut;
	oSelColor.onblur=SelColorBlur;
}

function ok(){
	if (opener){
		if (opener.WBTB_ExecuteCommand){
			command = new String(launchParameters['commandName']);
			opener.WBTB_ExecuteCommand(command,'',oSelColor.value);
		}else if(opener.setColor){
			opener.setColor(oSelColor.value);
		}
	}else{
		window.returnValue = oSelColor.value;
	}
	window.close();
}


</script>
<!--
<script language="javascript" for="ColorTable" event="onclick">
  SelRGB = event.srcElement.bgColor;
  EndColor();
</script>

<script language="javascript" for="ColorTable" event="onmouseover">
  oRGB.innerText = event.srcElement.bgColor.toUpperCase();
  EndColor();
</script>

<script language="javascript" for="ColorTable" event="onmouseout">
  oRGB.innerText = SelRGB;
  EndColor();
</script>

<script language="javascript" for="GrayTable" event="onclick">
  SelGRAY = event.srcElement.title;
  EndColor();
</script>

<script language="javascript" for="GrayTable" event="onmouseover">
  oGRAY.innerText = event.srcElement.title;
  EndColor();
</script>

<script language="javascript" for="GrayTable" event="onmouseout">
  oGRAY.innerText = SelGRAY;
  EndColor();
</script>

-->
<script language="javascript" >
</script>

</head>

<body bgcolor="menu" onload="InitDocument()">

<div align="center"><center><table border="0" cellspacing="10" cellpadding="0"><tr><td>
<table ID="ColorTable" border="0" cellspacing="0" cellpadding="0" style='cursor:hand'>
<script language="javascript">
function wc(r, g, b, n)
{
	r = ((r * 16 + r) * 3 * (15 - n) + 0x80 * n) / 15;
	g = ((g * 16 + g) * 3 * (15 - n) + 0x80 * n) / 15;
	b = ((b * 16 + b) * 3 * (15 - n) + 0x80 * n) / 15;

	document.write('<td bgcolor=#' + ToHex(r) + ToHex(g) + ToHex(b) + ' height=8 width=8></TD>');
}

var cnum = new Array(1, 0, 0, 1, 1, 0, 0, 1, 0, 0, 1, 1, 0, 0, 1, 1, 0, 1, 1, 0, 0);

  for(i = 0; i < 16; i ++)
  {
     document.write('<TR>');
     for(j = 0; j < 30; j ++)
     {
     	n1 = j % 5;
     	n2 = Math.floor(j / 5) * 3;
     	n3 = n2 + 3;

     	wc((cnum[n3] * n1 + cnum[n2] * (5 - n1)),
     		(cnum[n3 + 1] * n1 + cnum[n2 + 1] * (5 - n1)),
     		(cnum[n3 + 2] * n1 + cnum[n2 + 2] * (5 - n1)), i);
     }

     document.writeln('</tr>');
  }
</script>
</table></td><td>
<table id="GrayTable" border="0" cellspacing="0" cellpadding="0" style='cursor:hand'>
<script language="javascript">
  for(i = 255; i >= 0; i -= 8.5)
     document.write('<tr bgcolor=#' + ToHex(i) + ToHex(i) + ToHex(i) + '><td title=' + Math.floor(i * 16 / 17) + ' height=4 width=20></td></tr>');
</script>
</table>
</td></tr>
</table>
</center></div>

<div align="center"><center>
<table border="0" cellspacing="10" cellpadding="0" width="100%">
<tr><td rowspan="2" align="center" width=70>
<table ID="ShowColor" bgcolor="#000000" border="1" width="50" height="40" cellspacing="0" cellpadding="0">
<tr><td></td></tr>
</table>
</td>
<td rowspan="2">Base : <span id="RGB">#000000</span><br>
Bright: <span id="GRAY">120</span><br>
Code : <input type="text" size="7" maxlength="7" ID="SelColor" value="#000000"></td>
<td width="50"><button id="Ok" onclick="ok()" type="submit">OK</button></td></tr>
<tr><td width="50"><button onclick="window.close();">Cancel</button></td></tr>
</table></center></div>

</body>   
</html>
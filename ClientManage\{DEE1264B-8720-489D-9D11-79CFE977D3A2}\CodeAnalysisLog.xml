﻿<?xml version="1.0" encoding="utf-8"?>
<?xml-stylesheet type="text/xsl" href="d:\program files (x86)\microsoft visual studio\2019\community\team tools\static analysis tools\fxcop\Xml\CodeAnalysisReport.xsl"?>
<FxCopReport Version="16.0">
 <Targets>
  <Target Name="C:\Users\<USER>\AppData\Local\Temp\tmp7267.tmp.cadir\bin\App_Code.dll">
   <Modules>
    <Module Name="app_code.dll">
     <Namespaces>
      <Namespace Name="">
       <Types>
        <Type Name="Common" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Members>
          <Member Name="#Accord(System.Int32)" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="检查 SQL 查询是否存在安全漏洞" Category="Microsoft.Security" CheckId="CA2100" Status="Active" Created="2024-11-16 08:40:12Z" FixCategory="DependsOnFix">
             <Issue Name="WithNonLiterals" Certainty="75" Level="Warning" Path="d:\Code\ASP\Salemax\ClientManage\App_Code" File="Common.cs" Line="1071">传递给 'Common.Accord(int)' 中的 'SqlCommand.SqlCommand(string, SqlConnection)' 的查询字符串可能包含以下变量 'task_id', 'RecordIDs[i]'。如果其中的任意变量可能来自用户输入，请考虑使用存储过程或参数化 SQL 查询，而不是通过字符串串联来生成查询。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#DeleteLastExecute(System.Int32)" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="检查 SQL 查询是否存在安全漏洞" Category="Microsoft.Security" CheckId="CA2100" Status="Active" Created="2024-11-16 08:40:12Z" FixCategory="DependsOnFix">
             <Issue Name="WithNonLiterals" Certainty="75" Level="Warning" Path="d:\Code\ASP\Salemax\ClientManage\App_Code" File="Common.cs" Line="1161">传递给 'Common.DeleteLastExecute(int)' 中的 'SqlCommand.SqlCommand(string, SqlConnection)' 的查询字符串可能包含以下变量 'RecordIDs[i]', 'RecordIDs[i]', 'RecordIDs[(RecordIDs.Length - 2)]', 'task_id'。如果其中的任意变量可能来自用户输入，请考虑使用存储过程或参数化 SQL 查询，而不是通过字符串串联来生成查询。</Issue>
             <Issue Name="WithNonLiterals" Certainty="75" Level="Warning" Path="d:\Code\ASP\Salemax\ClientManage\App_Code" File="Common.cs" Line="1145">传递给 'Common.DeleteLastExecute(int)' 中的 'SqlCommand.SqlCommand(string, SqlConnection)' 的查询字符串可能包含以下变量 'this.GetLastRecordID(task_id)'。如果其中的任意变量可能来自用户输入，请考虑使用存储过程或参数化 SQL 查询，而不是通过字符串串联来生成查询。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#ExecuteNonQuery(System.String,System.Data.SqlClient.SqlParameter[])" Kind="Method" Static="True" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="检查 SQL 查询是否存在安全漏洞" Category="Microsoft.Security" CheckId="CA2100" Status="Active" Created="2024-11-16 08:40:12Z" FixCategory="DependsOnFix">
             <Issue Name="WithNonLiterals" Certainty="75" Level="Warning" Path="d:\Code\ASP\Salemax\ClientManage\App_Code" File="Common.cs" Line="1566">传递给 'Common.ExecuteNonQuery(string, SqlParameter[])' 中的 'SqlCommand.SqlCommand(string, SqlConnection)' 的查询字符串可能包含以下变量 'query'。如果其中的任意变量可能来自用户输入，请考虑使用存储过程或参数化 SQL 查询，而不是通过字符串串联来生成查询。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#ExecuteQuery(System.String,System.Data.SqlClient.SqlParameter[])" Kind="Method" Static="True" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="检查 SQL 查询是否存在安全漏洞" Category="Microsoft.Security" CheckId="CA2100" Status="Active" Created="2024-11-16 08:40:12Z" FixCategory="DependsOnFix">
             <Issue Name="WithNonLiterals" Certainty="75" Level="Warning" Path="d:\Code\ASP\Salemax\ClientManage\App_Code" File="Common.cs" Line="1500">传递给 'Common.ExecuteQuery(string, SqlParameter[])' 中的 'SqlCommand.SqlCommand(string, SqlConnection)' 的查询字符串可能包含以下变量 'query'。如果其中的任意变量可能来自用户输入，请考虑使用存储过程或参数化 SQL 查询，而不是通过字符串串联来生成查询。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#ExecuteScalar(System.String,System.Data.SqlClient.SqlParameter[])" Kind="Method" Static="True" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="检查 SQL 查询是否存在安全漏洞" Category="Microsoft.Security" CheckId="CA2100" Status="Active" Created="2024-11-16 08:40:12Z" FixCategory="DependsOnFix">
             <Issue Name="WithNonLiterals" Certainty="75" Level="Warning" Path="d:\Code\ASP\Salemax\ClientManage\App_Code" File="Common.cs" Line="1535">传递给 'Common.ExecuteScalar(string, SqlParameter[])' 中的 'SqlCommand.SqlCommand(string, SqlConnection)' 的查询字符串可能包含以下变量 'query'。如果其中的任意变量可能来自用户输入，请考虑使用存储过程或参数化 SQL 查询，而不是通过字符串串联来生成查询。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#GetAttributeFromTable(System.String,System.Int32,System.String,System.String)" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="检查 SQL 查询是否存在安全漏洞" Category="Microsoft.Security" CheckId="CA2100" Status="Active" Created="2024-11-16 08:40:12Z" FixCategory="DependsOnFix">
             <Issue Name="WithNonLiterals" Certainty="75" Level="Warning" Path="d:\Code\ASP\Salemax\ClientManage\App_Code" File="Common.cs" Line="54">传递给 'Common.GetAttributeFromTable(string, int, string, string)' 中的 'SqlCommand.SqlCommand(string, SqlConnection)' 的查询字符串可能包含以下变量 'attributeName', 'table', 'idName', 'id'。如果其中的任意变量可能来自用户输入，请考虑使用存储过程或参数化 SQL 查询，而不是通过字符串串联来生成查询。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#InsertMessage(System.Int32,System.DateTime,System.String)" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="检查 SQL 查询是否存在安全漏洞" Category="Microsoft.Security" CheckId="CA2100" Status="Active" Created="2024-11-16 08:40:12Z" FixCategory="DependsOnFix">
             <Issue Name="WithNonLiterals" Certainty="75" Level="Warning" Path="d:\Code\ASP\Salemax\ClientManage\App_Code" File="Common.cs" Line="1343">传递给 'Common.InsertMessage(int, DateTime, string)' 中的 'SqlCommand.SqlCommand(string, SqlConnection)' 的查询字符串可能包含以下变量 'executorID', '7', 'message', 'noteState', 'dt', 'delayTime'。如果其中的任意变量可能来自用户输入，请考虑使用存储过程或参数化 SQL 查询，而不是通过字符串串联来生成查询。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#SetPassword(System.Int32,System.String)" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="检查 SQL 查询是否存在安全漏洞" Category="Microsoft.Security" CheckId="CA2100" Status="Active" Created="2024-11-16 08:40:12Z" FixCategory="DependsOnFix">
             <Issue Name="WithNonLiterals" Certainty="75" Level="Warning" Path="d:\Code\ASP\Salemax\ClientManage\App_Code" File="Common.cs" Line="653">传递给 'Common.SetPassword(int, string)' 中的 'SqlCommand.SqlCommand(string, SqlConnection)' 的查询字符串可能包含以下变量 'password', 'user_id'。如果其中的任意变量可能来自用户输入，请考虑使用存储过程或参数化 SQL 查询，而不是通过字符串串联来生成查询。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#ShowReport(System.Int32)" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="检查 SQL 查询是否存在安全漏洞" Category="Microsoft.Security" CheckId="CA2100" Status="Active" Created="2024-11-16 08:40:12Z" FixCategory="DependsOnFix">
             <Issue Name="WithNonLiterals" Certainty="75" Level="Warning" Path="d:\Code\ASP\Salemax\ClientManage\App_Code" File="Common.cs" Line="1416">传递给 'Common.ShowReport(int)' 中的 'SqlCommand.SqlCommand(string, SqlConnection)' 的查询字符串可能包含以下变量 'executeRecord[i]'。如果其中的任意变量可能来自用户输入，请考虑使用存储过程或参数化 SQL 查询，而不是通过字符串串联来生成查询。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#TaskAddRecord(System.Int32,System.Int32)" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="检查 SQL 查询是否存在安全漏洞" Category="Microsoft.Security" CheckId="CA2100" Status="Active" Created="2024-11-16 08:40:12Z" FixCategory="DependsOnFix">
             <Issue Name="WithNonLiterals" Certainty="75" Level="Warning" Path="d:\Code\ASP\Salemax\ClientManage\App_Code" File="Common.cs" Line="952">传递给 'Common.TaskAddRecord(int, int)' 中的 'SqlCommand.SqlCommand(string, SqlConnection)' 的查询字符串可能包含以下变量 'dr["ExecuteRecord"]', 'LastRecordID', 'task_id'。如果其中的任意变量可能来自用户输入，请考虑使用存储过程或参数化 SQL 查询，而不是通过字符串串联来生成查询。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#UpdateFollowNumber(System.Int32)" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="检查 SQL 查询是否存在安全漏洞" Category="Microsoft.Security" CheckId="CA2100" Status="Active" Created="2024-11-16 08:40:12Z" FixCategory="DependsOnFix">
             <Issue Name="WithNonLiterals" Certainty="75" Level="Warning" Path="d:\Code\ASP\Salemax\ClientManage\App_Code" File="Common.cs" Line="987">传递给 'Common.UpdateFollowNumber(int)' 中的 'SqlCommand.SqlCommand(string, SqlConnection)' 的查询字符串可能包含以下变量 'total', 'id'。如果其中的任意变量可能来自用户输入，请考虑使用存储过程或参数化 SQL 查询，而不是通过字符串串联来生成查询。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
       </Types>
      </Namespace>
     </Namespaces>
    </Module>
   </Modules>
  </Target>
  <Target Name="C:\Users\<USER>\AppData\Local\Temp\tmp7267.tmp.cadir\bin\App_Web_bwkguky4.dll">
   <Modules>
    <Module Name="app_web_bwkguky4.dll">
     <Namespaces>
      <Namespace Name="">
       <Types>
        <Type Name="AddRecord" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Members>
          <Member Name="#Button1_Click(System.Object,System.EventArgs)" Kind="Method" Static="False" Accessibility="Family" ExternallyVisible="True">
           <Messages>
            <Message TypeName="检查 SQL 查询是否存在安全漏洞" Category="Microsoft.Security" CheckId="CA2100" Status="Active" Created="2024-11-16 08:40:12Z" FixCategory="DependsOnFix">
             <Issue Name="WithNonLiterals" Certainty="75" Level="Warning" Path="d:\Code\ASP\Salemax\ClientManage" File="AddCooperateRecord.aspx.cs" Line="37">传递给 'AddRecord.Button1_Click(object, EventArgs)' 中的 'SqlCommand.SqlCommand(string, SqlConnection)' 的查询字符串可能包含以下变量 'DateTime.Now', 'this.Request.Params["id"]'。如果其中的任意变量可能来自用户输入，请考虑使用存储过程或参数化 SQL 查询，而不是通过字符串串联来生成查询。</Issue>
             <Issue Name="WithNonLiterals" Certainty="75" Level="Warning" Path="d:\Code\ASP\Salemax\ClientManage" File="AddCooperateRecord.aspx.cs" Line="33">传递给 'AddRecord.Button1_Click(object, EventArgs)' 中的 'SqlCommand.SqlCommand(string, SqlConnection)' 的查询字符串可能包含以下变量 'this.Request.Params["id"]', 'this.TextBox1.Text', 'DateTime.Now', 'this.Session["user_id"]'。如果其中的任意变量可能来自用户输入，请考虑使用存储过程或参数化 SQL 查询，而不是通过字符串串联来生成查询。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="ChooseCooperation" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Members>
          <Member Name="#GetSearchResultTable(System.String,System.String,System.String)" Kind="Method" Static="False" Accessibility="Family" ExternallyVisible="True">
           <Messages>
            <Message TypeName="检查 SQL 查询是否存在安全漏洞" Category="Microsoft.Security" CheckId="CA2100" Status="Active" Created="2024-11-16 08:40:12Z" FixCategory="DependsOnFix">
             <Issue Name="WithNonLiterals" Certainty="75" Level="Warning" Path="d:\Code\ASP\Salemax\ClientManage" File="ChooseCooperation.aspx.cs" Line="1055">传递给 'ChooseCooperation.GetSearchResultTable(string, string, string)' 中的 'SqlDataAdapter.SqlDataAdapter(string, string)' 的查询字符串可能包含以下变量 'this.ViewState["subSearchStr"]', 'sortExpression', 'sortDirection', 'searchStr'。如果其中的任意变量可能来自用户输入，请考虑使用存储过程或参数化 SQL 查询，而不是通过字符串串联来生成查询。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="See" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Members>
          <Member Name="#GetSearchResultTable(System.String,System.String,System.String)" Kind="Method" Static="False" Accessibility="Family" ExternallyVisible="True">
           <Messages>
            <Message TypeName="检查 SQL 查询是否存在安全漏洞" Category="Microsoft.Security" CheckId="CA2100" Status="Active" Created="2024-11-16 08:40:12Z" FixCategory="DependsOnFix">
             <Issue Name="WithNonLiterals" Certainty="75" Level="Warning" Path="d:\Code\ASP\Salemax\ClientManage" File="SeeCooperate.aspx.cs" Line="1243">传递给 'See.GetSearchResultTable(string, string, string)' 中的 'SqlDataAdapter.SqlDataAdapter(string, string)' 的查询字符串可能包含以下变量 'this.ViewState["subSearchStr"]', 'sortExpression', 'sortDirection', 'searchStr'。如果其中的任意变量可能来自用户输入，请考虑使用存储过程或参数化 SQL 查询，而不是通过字符串串联来生成查询。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="SeeTask" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Members>
          <Member Name="#GenerateSeeMould(System.String)" Kind="Method" Static="False" Accessibility="Family" ExternallyVisible="True">
           <Messages>
            <Message TypeName="检查 SQL 查询是否存在安全漏洞" Category="Microsoft.Security" CheckId="CA2100" Status="Active" Created="2024-11-16 08:40:12Z" FixCategory="DependsOnFix">
             <Issue Name="WithNonLiterals" Certainty="75" Level="Warning" Path="d:\Code\ASP\Salemax\ClientManage" File="SeeDetail.aspx.cs" Line="112">传递给 'SeeTask.GenerateSeeMould(string)' 中的 'SqlDataAdapter.SqlDataAdapter(string, SqlConnection)' 的查询字符串可能包含以下变量 'strSql'。如果其中的任意变量可能来自用户输入，请考虑使用存储过程或参数化 SQL 查询，而不是通过字符串串联来生成查询。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
       </Types>
      </Namespace>
     </Namespaces>
    </Module>
   </Modules>
  </Target>
  <Target Name="C:\Users\<USER>\AppData\Local\Temp\tmp7267.tmp.cadir\bin\App_Web_hl5nk45m.dll">
   <Modules>
    <Module Name="app_web_hl5nk45m.dll">
     <Namespaces>
      <Namespace Name="">
       <Types>
        <Type Name="ChooseClient" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Members>
          <Member Name="#GetSearchResultTable(System.String,System.String,System.String)" Kind="Method" Static="False" Accessibility="Family" ExternallyVisible="True">
           <Messages>
            <Message TypeName="检查 SQL 查询是否存在安全漏洞" Category="Microsoft.Security" CheckId="CA2100" Status="Active" Created="2024-11-16 08:40:12Z" FixCategory="DependsOnFix">
             <Issue Name="WithNonLiterals" Certainty="75" Level="Warning" Path="d:\Code\ASP\Salemax\ClientManage" File="ChooseClient.aspx.cs" Line="1061">传递给 'ChooseClient.GetSearchResultTable(string, string, string)' 中的 'SqlDataAdapter.SqlDataAdapter(string, string)' 的查询字符串可能包含以下变量 'this.ViewState["subSearchStr"]', 'sortExpression', 'sortDirection', 'searchStr'。如果其中的任意变量可能来自用户输入，请考虑使用存储过程或参数化 SQL 查询，而不是通过字符串串联来生成查询。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="ChooseUser" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Members>
          <Member Name="#GetSearchResultTable(System.String,System.String,System.String)" Kind="Method" Static="False" Accessibility="Family" ExternallyVisible="True">
           <Messages>
            <Message TypeName="检查 SQL 查询是否存在安全漏洞" Category="Microsoft.Security" CheckId="CA2100" Status="Active" Created="2024-11-16 08:40:12Z" FixCategory="DependsOnFix">
             <Issue Name="WithNonLiterals" Certainty="75" Level="Warning" Path="d:\Code\ASP\Salemax\ClientManage" File="ChooseUser.aspx.cs" Line="1057">传递给 'ChooseUser.GetSearchResultTable(string, string, string)' 中的 'SqlDataAdapter.SqlDataAdapter(string, string)' 的查询字符串可能包含以下变量 'this.ViewState["subSearchStr"]', 'sortExpression', 'sortDirection', 'searchStr'。如果其中的任意变量可能来自用户输入，请考虑使用存储过程或参数化 SQL 查询，而不是通过字符串串联来生成查询。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="See" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Members>
          <Member Name="#GetSearchResultTable(System.String,System.String,System.String)" Kind="Method" Static="False" Accessibility="Family" ExternallyVisible="True">
           <Messages>
            <Message TypeName="检查 SQL 查询是否存在安全漏洞" Category="Microsoft.Security" CheckId="CA2100" Status="Active" Created="2024-11-16 08:40:12Z" FixCategory="DependsOnFix">
             <Issue Name="WithNonLiterals" Certainty="75" Level="Warning" Path="d:\Code\ASP\Salemax\ClientManage" File="Copy of SeeCooperate.aspx.cs" Line="930">传递给 'See.GetSearchResultTable(string, string, string)' 中的 'SqlDataAdapter.SqlDataAdapter(string, string)' 的查询字符串可能包含以下变量 'this.ViewState["subSearchStr"]', 'sortExpression', 'sortDirection', 'searchStr'。如果其中的任意变量可能来自用户输入，请考虑使用存储过程或参数化 SQL 查询，而不是通过字符串串联来生成查询。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
       </Types>
      </Namespace>
     </Namespaces>
    </Module>
   </Modules>
  </Target>
  <Target Name="C:\Users\<USER>\AppData\Local\Temp\tmp7267.tmp.cadir\bin\App_Web_jhw0ehpe.dll">
   <Modules>
    <Module Name="app_web_jhw0ehpe.dll">
     <Namespaces>
      <Namespace Name="">
       <Types>
        <Type Name="AddUser" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Members>
          <Member Name="#Add()" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="检查 SQL 查询是否存在安全漏洞" Category="Microsoft.Security" CheckId="CA2100" Status="Active" Created="2024-11-16 08:40:12Z" FixCategory="DependsOnFix">
             <Issue Name="WithNonLiterals" Certainty="75" Level="Warning" Path="d:\Code\ASP\Salemax\ClientManage" File="AddUser.aspx.cs" Line="562">传递给 'AddUser.Add()' 中的 'SqlCommand.SqlCommand(string, SqlConnection)' 的查询字符串可能包含以下变量 'this.strInserts[addIndex]'。如果其中的任意变量可能来自用户输入，请考虑使用存储过程或参数化 SQL 查询，而不是通过字符串串联来生成查询。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="Login" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Members>
          <Member Name="#btnLogin_Click(System.Object,System.EventArgs)" Kind="Method" Static="False" Accessibility="Family" ExternallyVisible="True">
           <Messages>
            <Message TypeName="检查 SQL 查询是否存在安全漏洞" Category="Microsoft.Security" CheckId="CA2100" Status="Active" Created="2024-11-16 08:40:12Z" FixCategory="DependsOnFix">
             <Issue Name="WithNonLiterals" Certainty="75" Level="Warning" Path="d:\Code\ASP\Salemax\ClientManage" File="Login.aspx.cs" Line="47">传递给 'Login.btnLogin_Click(object, EventArgs)' 中的 'SqlCommand.SqlCommand(string, SqlConnection)' 的查询字符串可能包含以下变量 'this.tbxUserName.Text', 'this.tbxUserName.Text', 'this.tbxPassword.Text'。如果其中的任意变量可能来自用户输入，请考虑使用存储过程或参数化 SQL 查询，而不是通过字符串串联来生成查询。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="ModifyCIients" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Members>
          <Member Name="#GeneModifyMould()" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="检查 SQL 查询是否存在安全漏洞" Category="Microsoft.Security" CheckId="CA2100" Status="Active" Created="2024-11-16 08:40:12Z" FixCategory="DependsOnFix">
             <Issue Name="WithNonLiterals" Certainty="75" Level="Warning" Path="d:\Code\ASP\Salemax\ClientManage" File="ModifyCIients.aspx.cs" Line="211">传递给 'ModifyCIients.GeneModifyMould()' 中的 'SqlDataAdapter.SqlDataAdapter(string, SqlConnection)' 的查询字符串可能包含以下变量 'fields[fieldIndex].fieldName'。如果其中的任意变量可能来自用户输入，请考虑使用存储过程或参数化 SQL 查询，而不是通过字符串串联来生成查询。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#Update()" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="检查 SQL 查询是否存在安全漏洞" Category="Microsoft.Security" CheckId="CA2100" Status="Active" Created="2024-11-16 08:40:12Z" FixCategory="DependsOnFix">
             <Issue Name="WithNonLiterals" Certainty="75" Level="Warning" Path="d:\Code\ASP\Salemax\ClientManage" File="ModifyCIients.aspx.cs" Line="946">传递给 'ModifyCIients.Update()' 中的 'SqlCommand.SqlCommand(string, SqlConnection)' 的查询字符串可能包含以下变量 'this.Request.Params["recordId"]', 'new Common().GetNickname(int.Parse(this.Session["user_id"].ToString()))', 'DateTime.Now', 'this.Session["user_id"]'。如果其中的任意变量可能来自用户输入，请考虑使用存储过程或参数化 SQL 查询，而不是通过字符串串联来生成查询。</Issue>
             <Issue Name="WithNonLiterals" Certainty="75" Level="Warning" Path="d:\Code\ASP\Salemax\ClientManage" File="ModifyCIients.aspx.cs" Line="940">传递给 'ModifyCIients.Update()' 中的 'SqlCommand.SqlCommand(string, SqlConnection)' 的查询字符串可能包含以下变量 'this.strUpdates[modifyIndex]'。如果其中的任意变量可能来自用户输入，请考虑使用存储过程或参数化 SQL 查询，而不是通过字符串串联来生成查询。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="See" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Members>
          <Member Name="#GetSearchResultTable(System.String,System.String,System.String)" Kind="Method" Static="False" Accessibility="Family" ExternallyVisible="True">
           <Messages>
            <Message TypeName="检查 SQL 查询是否存在安全漏洞" Category="Microsoft.Security" CheckId="CA2100" Status="Active" Created="2024-11-16 08:40:12Z" FixCategory="DependsOnFix">
             <Issue Name="WithNonLiterals" Certainty="75" Level="Warning" Path="d:\Code\ASP\Salemax\ClientManage" File="Default.aspx.cs" Line="1431">传递给 'See.GetSearchResultTable(string, string, string)' 中的 'SqlDataAdapter.SqlDataAdapter(string, string)' 的查询字符串可能包含以下变量 'this.ViewState["subSearchStr"]', 'sortExpression', 'sortDirection', 'searchStr'。如果其中的任意变量可能来自用户输入，请考虑使用存储过程或参数化 SQL 查询，而不是通过字符串串联来生成查询。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="SeeTask" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Members>
          <Member Name="#GenerateSeeMould(System.String)" Kind="Method" Static="False" Accessibility="Family" ExternallyVisible="True">
           <Messages>
            <Message TypeName="检查 SQL 查询是否存在安全漏洞" Category="Microsoft.Security" CheckId="CA2100" Status="Active" Created="2024-11-16 08:40:12Z" FixCategory="DependsOnFix">
             <Issue Name="WithNonLiterals" Certainty="75" Level="Warning" Path="d:\Code\ASP\Salemax\ClientManage" File="SeeCooperateDetail.aspx.cs" Line="87">传递给 'SeeTask.GenerateSeeMould(string)' 中的 'SqlDataAdapter.SqlDataAdapter(string, SqlConnection)' 的查询字符串可能包含以下变量 'strSql'。如果其中的任意变量可能来自用户输入，请考虑使用存储过程或参数化 SQL 查询，而不是通过字符串串联来生成查询。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
       </Types>
      </Namespace>
     </Namespaces>
    </Module>
   </Modules>
  </Target>
  <Target Name="C:\Users\<USER>\AppData\Local\Temp\tmp7267.tmp.cadir\bin\App_Web_ke0ep3m5.dll">
   <Modules>
    <Module Name="app_web_ke0ep3m5.dll">
     <Namespaces>
      <Namespace Name="">
       <Types>
        <Type Name="AddRecord" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Members>
          <Member Name="#Button1_Click(System.Object,System.EventArgs)" Kind="Method" Static="False" Accessibility="Family" ExternallyVisible="True">
           <Messages>
            <Message TypeName="检查 SQL 查询是否存在安全漏洞" Category="Microsoft.Security" CheckId="CA2100" Status="Active" Created="2024-11-16 08:40:12Z" FixCategory="DependsOnFix">
             <Issue Name="WithNonLiterals" Certainty="75" Level="Warning" Path="d:\Code\ASP\Salemax\ClientManage" File="AddRecord.aspx.cs" Line="60">传递给 'AddRecord.Button1_Click(object, EventArgs)' 中的 'SqlCommand.SqlCommand(string, SqlConnection)' 的查询字符串可能包含以下变量 'this.FollowTime.Text', 'this.Request.Params["id"]'。如果其中的任意变量可能来自用户输入，请考虑使用存储过程或参数化 SQL 查询，而不是通过字符串串联来生成查询。</Issue>
             <Issue Name="WithNonLiterals" Certainty="75" Level="Warning" Path="d:\Code\ASP\Salemax\ClientManage" File="AddRecord.aspx.cs" Line="49">传递给 'AddRecord.Button1_Click(object, EventArgs)' 中的 'SqlCommand.SqlCommand(string, SqlConnection)' 的查询字符串可能包含以下变量 'this.Request.Params["id"]', 'this.TextBox1.Text', 'DateTime.Parse(this.FollowTime.Text)', 'this.Session["user_id"]'。如果其中的任意变量可能来自用户输入，请考虑使用存储过程或参数化 SQL 查询，而不是通过字符串串联来生成查询。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="BrowseUsers" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Members>
          <Member Name="#GetSearchResultTable(System.String,System.String,System.String)" Kind="Method" Static="False" Accessibility="Family" ExternallyVisible="True">
           <Messages>
            <Message TypeName="检查 SQL 查询是否存在安全漏洞" Category="Microsoft.Security" CheckId="CA2100" Status="Active" Created="2024-11-16 08:40:12Z" FixCategory="DependsOnFix">
             <Issue Name="WithNonLiterals" Certainty="75" Level="Warning" Path="d:\Code\ASP\Salemax\ClientManage" File="BrowseUsers.aspx.cs" Line="819">传递给 'BrowseUsers.GetSearchResultTable(string, string, string)' 中的 'SqlDataAdapter.SqlDataAdapter(string, string)' 的查询字符串可能包含以下变量 'this.ViewState["subSearchStr"]', 'sortExpression', 'sortDirection', 'searchStr'。如果其中的任意变量可能来自用户输入，请考虑使用存储过程或参数化 SQL 查询，而不是通过字符串串联来生成查询。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#lkbDeleteUser_Command(System.Object,System.Web.UI.WebControls.CommandEventArgs)" Kind="Method" Static="False" Accessibility="Family" ExternallyVisible="True">
           <Messages>
            <Message TypeName="检查 SQL 查询是否存在安全漏洞" Category="Microsoft.Security" CheckId="CA2100" Status="Active" Created="2024-11-16 08:40:12Z" FixCategory="DependsOnFix">
             <Issue Name="WithNonLiterals" Certainty="75" Level="Warning" Path="d:\Code\ASP\Salemax\ClientManage" File="BrowseUsers.aspx.cs" Line="1171">传递给 'BrowseUsers.lkbDeleteUser_Command(object, CommandEventArgs)' 中的 'SqlCommand.SqlCommand(string, SqlConnection)' 的查询字符串可能包含以下变量 'e.CommandName'。如果其中的任意变量可能来自用户输入，请考虑使用存储过程或参数化 SQL 查询，而不是通过字符串串联来生成查询。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="Modify" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Members>
          <Member Name="#GeneModifyMould()" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="检查 SQL 查询是否存在安全漏洞" Category="Microsoft.Security" CheckId="CA2100" Status="Active" Created="2024-11-16 08:40:12Z" FixCategory="DependsOnFix">
             <Issue Name="WithNonLiterals" Certainty="75" Level="Warning" Path="d:\Code\ASP\Salemax\ClientManage" File="ModifyCooperate.aspx.cs" Line="229">传递给 'Modify.GeneModifyMould()' 中的 'SqlDataAdapter.SqlDataAdapter(string, SqlConnection)' 的查询字符串可能包含以下变量 'fields[fieldIndex].fieldName'。如果其中的任意变量可能来自用户输入，请考虑使用存储过程或参数化 SQL 查询，而不是通过字符串串联来生成查询。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#Update()" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="检查 SQL 查询是否存在安全漏洞" Category="Microsoft.Security" CheckId="CA2100" Status="Active" Created="2024-11-16 08:40:12Z" FixCategory="DependsOnFix">
             <Issue Name="WithNonLiterals" Certainty="75" Level="Warning" Path="d:\Code\ASP\Salemax\ClientManage" File="ModifyCooperate.aspx.cs" Line="858">传递给 'Modify.Update()' 中的 'SqlCommand.SqlCommand(string, SqlConnection)' 的查询字符串可能包含以下变量 'this.strUpdates[modifyIndex]'。如果其中的任意变量可能来自用户输入，请考虑使用存储过程或参数化 SQL 查询，而不是通过字符串串联来生成查询。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="ModifyUsers" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Members>
          <Member Name="#GeneModifyMould()" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="检查 SQL 查询是否存在安全漏洞" Category="Microsoft.Security" CheckId="CA2100" Status="Active" Created="2024-11-16 08:40:12Z" FixCategory="DependsOnFix">
             <Issue Name="WithNonLiterals" Certainty="75" Level="Warning" Path="d:\Code\ASP\Salemax\ClientManage" File="ModifyUsers.aspx.cs" Line="199">传递给 'ModifyUsers.GeneModifyMould()' 中的 'SqlDataAdapter.SqlDataAdapter(string, SqlConnection)' 的查询字符串可能包含以下变量 'fields[fieldIndex].fieldName', 'this.Request.Params["UserID"]'。如果其中的任意变量可能来自用户输入，请考虑使用存储过程或参数化 SQL 查询，而不是通过字符串串联来生成查询。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#Update()" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="检查 SQL 查询是否存在安全漏洞" Category="Microsoft.Security" CheckId="CA2100" Status="Active" Created="2024-11-16 08:40:12Z" FixCategory="DependsOnFix">
             <Issue Name="WithNonLiterals" Certainty="75" Level="Warning" Path="d:\Code\ASP\Salemax\ClientManage" File="ModifyUsers.aspx.cs" Line="564">传递给 'ModifyUsers.Update()' 中的 'SqlCommand.SqlCommand(string, SqlConnection)' 的查询字符串可能包含以下变量 'this.strUpdates'。如果其中的任意变量可能来自用户输入，请考虑使用存储过程或参数化 SQL 查询，而不是通过字符串串联来生成查询。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="Register" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Members>
          <Member Name="#BindGroups()" Kind="Method" Static="False" Accessibility="Private" ExternallyVisible="False">
           <Messages>
            <Message TypeName="检查 SQL 查询是否存在安全漏洞" Category="Microsoft.Security" CheckId="CA2100" Status="Active" Created="2024-11-16 08:40:12Z" FixCategory="DependsOnFix">
             <Issue Name="WithNonLiterals" Certainty="75" Level="Warning" Path="d:\Code\ASP\Salemax\ClientManage" File="Register.aspx.cs" Line="45">传递给 'Register.BindGroups()' 中的 'SqlCommand.SqlCommand(string, SqlConnection)' 的查询字符串可能包含以下变量 'this.txtCompanySearch.Text'。如果其中的任意变量可能来自用户输入，请考虑使用存储过程或参数化 SQL 查询，而不是通过字符串串联来生成查询。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
       </Types>
      </Namespace>
     </Namespaces>
    </Module>
   </Modules>
  </Target>
  <Target Name="C:\Users\<USER>\AppData\Local\Temp\tmp7267.tmp.cadir\bin\App_Web_pusriod4.dll">
   <Modules>
    <Module Name="app_web_pusriod4.dll">
     <Namespaces>
      <Namespace Name="">
       <Types>
        <Type Name="ADD" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Members>
          <Member Name="#Add()" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="检查 SQL 查询是否存在安全漏洞" Category="Microsoft.Security" CheckId="CA2100" Status="Active" Created="2024-11-16 08:40:12Z" FixCategory="DependsOnFix">
             <Issue Name="WithNonLiterals" Certainty="75" Level="Warning" Path="d:\Code\ASP\Salemax\ClientManage" File="ADD.aspx.cs" Line="637">传递给 'ADD.Add()' 中的 'SqlCommand.SqlCommand(string, SqlConnection)' 的查询字符串可能包含以下变量 'new Common().GetMaxFollowClientID()', 'new Common().GetNickname(int.Parse(this.Session["user_id"].ToString()))', 'DateTime.Now', 'this.Session["user_id"]'。如果其中的任意变量可能来自用户输入，请考虑使用存储过程或参数化 SQL 查询，而不是通过字符串串联来生成查询。</Issue>
             <Issue Name="WithNonLiterals" Certainty="75" Level="Warning" Path="d:\Code\ASP\Salemax\ClientManage" File="ADD.aspx.cs" Line="631">传递给 'ADD.Add()' 中的 'SqlCommand.SqlCommand(string, SqlConnection)' 的查询字符串可能包含以下变量 'this.strInserts[addIndex]'。如果其中的任意变量可能来自用户输入，请考虑使用存储过程或参数化 SQL 查询，而不是通过字符串串联来生成查询。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="AddUsers" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Members>
          <Member Name="#Add()" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="检查 SQL 查询是否存在安全漏洞" Category="Microsoft.Security" CheckId="CA2100" Status="Active" Created="2024-11-16 08:40:12Z" FixCategory="DependsOnFix">
             <Issue Name="WithNonLiterals" Certainty="75" Level="Warning" Path="d:\Code\ASP\Salemax\ClientManage" File="AddUsers.aspx.cs" Line="602">传递给 'AddUsers.Add()' 中的 'SqlCommand.SqlCommand(string, SqlConnection)' 的查询字符串可能包含以下变量 'this.strInserts[addIndex]'。如果其中的任意变量可能来自用户输入，请考虑使用存储过程或参数化 SQL 查询，而不是通过字符串串联来生成查询。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="Modify" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Members>
          <Member Name="#GeneModifyMould()" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="检查 SQL 查询是否存在安全漏洞" Category="Microsoft.Security" CheckId="CA2100" Status="Active" Created="2024-11-16 08:40:12Z" FixCategory="DependsOnFix">
             <Issue Name="WithNonLiterals" Certainty="75" Level="Warning" Path="d:\Code\ASP\Salemax\ClientManage" File="Modify.aspx.cs" Line="243">传递给 'Modify.GeneModifyMould()' 中的 'SqlDataAdapter.SqlDataAdapter(string, SqlConnection)' 的查询字符串可能包含以下变量 'fields[fieldIndex].fieldName', 'this.Session["user_id"]'。如果其中的任意变量可能来自用户输入，请考虑使用存储过程或参数化 SQL 查询，而不是通过字符串串联来生成查询。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#Update()" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="检查 SQL 查询是否存在安全漏洞" Category="Microsoft.Security" CheckId="CA2100" Status="Active" Created="2024-11-16 08:40:12Z" FixCategory="DependsOnFix">
             <Issue Name="WithNonLiterals" Certainty="75" Level="Warning" Path="d:\Code\ASP\Salemax\ClientManage" File="Modify.aspx.cs" Line="971">传递给 'Modify.Update()' 中的 'SqlCommand.SqlCommand(string, SqlConnection)' 的查询字符串可能包含以下变量 'this.Request.Params["recordId"]', 'new Common().GetNickname(int.Parse(this.Session["user_id"].ToString()))', 'DateTime.Now', 'this.Session["user_id"]'。如果其中的任意变量可能来自用户输入，请考虑使用存储过程或参数化 SQL 查询，而不是通过字符串串联来生成查询。</Issue>
             <Issue Name="WithNonLiterals" Certainty="75" Level="Warning" Path="d:\Code\ASP\Salemax\ClientManage" File="Modify.aspx.cs" Line="965">传递给 'Modify.Update()' 中的 'SqlCommand.SqlCommand(string, SqlConnection)' 的查询字符串可能包含以下变量 'this.strUpdates[modifyIndex]'。如果其中的任意变量可能来自用户输入，请考虑使用存储过程或参数化 SQL 查询，而不是通过字符串串联来生成查询。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
       </Types>
      </Namespace>
     </Namespaces>
    </Module>
   </Modules>
  </Target>
  <Target Name="C:\Users\<USER>\AppData\Local\Temp\tmp7267.tmp.cadir\bin\App_Web_ymc0pnnf.dll">
   <Modules>
    <Module Name="app_web_ymc0pnnf.dll">
     <Namespaces>
      <Namespace Name="">
       <Types>
        <Type Name="AddMould" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Members>
          <Member Name="#Add()" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="检查 SQL 查询是否存在安全漏洞" Category="Microsoft.Security" CheckId="CA2100" Status="Active" Created="2024-11-16 08:40:12Z" FixCategory="DependsOnFix">
             <Issue Name="WithNonLiterals" Certainty="75" Level="Warning" Path="d:\Code\ASP\Salemax\ClientManage\Mould" File="AddMould.aspx.cs" Line="548">传递给 'AddMould.Add()' 中的 'SqlCommand.SqlCommand(string, SqlConnection)' 的查询字符串可能包含以下变量 'this.strInserts[addIndex]'。如果其中的任意变量可能来自用户输入，请考虑使用存储过程或参数化 SQL 查询，而不是通过字符串串联来生成查询。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="ChooseMould" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Members>
          <Member Name="#GetSearchResultTable(System.String,System.String,System.String)" Kind="Method" Static="False" Accessibility="Family" ExternallyVisible="True">
           <Messages>
            <Message TypeName="检查 SQL 查询是否存在安全漏洞" Category="Microsoft.Security" CheckId="CA2100" Status="Active" Created="2024-11-16 08:40:12Z" FixCategory="DependsOnFix">
             <Issue Name="WithNonLiterals" Certainty="75" Level="Warning" Path="d:\Code\ASP\Salemax\ClientManage\Mould" File="ChooseMould.aspx.cs" Line="852">传递给 'ChooseMould.GetSearchResultTable(string, string, string)' 中的 'SqlDataAdapter.SqlDataAdapter(string, string)' 的查询字符串可能包含以下变量 'this.ViewState["subSearchStr"]', 'sortExpression', 'sortDirection', 'searchStr'。如果其中的任意变量可能来自用户输入，请考虑使用存储过程或参数化 SQL 查询，而不是通过字符串串联来生成查询。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="ModifyMould" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Members>
          <Member Name="#GeneModifyMould()" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="检查 SQL 查询是否存在安全漏洞" Category="Microsoft.Security" CheckId="CA2100" Status="Active" Created="2024-11-16 08:40:12Z" FixCategory="DependsOnFix">
             <Issue Name="WithNonLiterals" Certainty="75" Level="Warning" Path="d:\Code\ASP\Salemax\ClientManage\Mould" File="ModifyMould.aspx.cs" Line="178">传递给 'ModifyMould.GeneModifyMould()' 中的 'SqlDataAdapter.SqlDataAdapter(string, SqlConnection)' 的查询字符串可能包含以下变量 'fields[fieldIndex].fieldName'。如果其中的任意变量可能来自用户输入，请考虑使用存储过程或参数化 SQL 查询，而不是通过字符串串联来生成查询。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#Update()" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="检查 SQL 查询是否存在安全漏洞" Category="Microsoft.Security" CheckId="CA2100" Status="Active" Created="2024-11-16 08:40:12Z" FixCategory="DependsOnFix">
             <Issue Name="WithNonLiterals" Certainty="75" Level="Warning" Path="d:\Code\ASP\Salemax\ClientManage\Mould" File="ModifyMould.aspx.cs" Line="599">传递给 'ModifyMould.Update()' 中的 'SqlCommand.SqlCommand(string, SqlConnection)' 的查询字符串可能包含以下变量 'this.strUpdates[modifyIndex]'。如果其中的任意变量可能来自用户输入，请考虑使用存储过程或参数化 SQL 查询，而不是通过字符串串联来生成查询。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="SearchMould" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Members>
          <Member Name="#GetSearchResultTable(System.String,System.String,System.String)" Kind="Method" Static="False" Accessibility="Family" ExternallyVisible="True">
           <Messages>
            <Message TypeName="检查 SQL 查询是否存在安全漏洞" Category="Microsoft.Security" CheckId="CA2100" Status="Active" Created="2024-11-16 08:40:12Z" FixCategory="DependsOnFix">
             <Issue Name="WithNonLiterals" Certainty="75" Level="Warning" Path="d:\Code\ASP\Salemax\ClientManage\Mould" File="SearchMould.aspx.cs" Line="1087">传递给 'SearchMould.GetSearchResultTable(string, string, string)' 中的 'SqlDataAdapter.SqlDataAdapter(string, string)' 的查询字符串可能包含以下变量 'this.ViewState["subSearchStr"]', 'sortExpression', 'sortDirection', 'searchStr'。如果其中的任意变量可能来自用户输入，请考虑使用存储过程或参数化 SQL 查询，而不是通过字符串串联来生成查询。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="SingleSearchMould" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Members>
          <Member Name="#GetSearchResultTable(System.String,System.String,System.String)" Kind="Method" Static="False" Accessibility="Family" ExternallyVisible="True">
           <Messages>
            <Message TypeName="检查 SQL 查询是否存在安全漏洞" Category="Microsoft.Security" CheckId="CA2100" Status="Active" Created="2024-11-16 08:40:12Z" FixCategory="DependsOnFix">
             <Issue Name="WithNonLiterals" Certainty="75" Level="Warning" Path="d:\Code\ASP\Salemax\ClientManage\Mould" File="SingleSearchMould.aspx.cs" Line="795">传递给 'SingleSearchMould.GetSearchResultTable(string, string, string)' 中的 'SqlDataAdapter.SqlDataAdapter(string, string)' 的查询字符串可能包含以下变量 'this.ViewState["subSearchStr"]', 'sortExpression', 'sortDirection', 'searchStr'。如果其中的任意变量可能来自用户输入，请考虑使用存储过程或参数化 SQL 查询，而不是通过字符串串联来生成查询。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
       </Types>
      </Namespace>
     </Namespaces>
    </Module>
   </Modules>
  </Target>
 </Targets>
 <Rules>
  <Rule TypeName="检查 SQL 查询是否存在安全漏洞" Category="Microsoft.Security" CheckId="CA2100">
   <Name>检查 SQL 查询是否存在安全漏洞</Name>
   <Description>通过用户输入生成的 SQL 命令字符串容易受到 SQL 注入式攻击。Microsoft SQL Server 和其他数据库服务器支持存储过程和参数化 SQL 查询，这将降低受到注入式攻击的风险。</Description>
   <Resolution Name="WithNonLiterals">传递给 {1} 中的 {0} 的查询字符串可能包含以下变量 {2}。如果其中的任意变量可能来自用户输入，请考虑使用存储过程或参数化 SQL 查询，而不是通过字符串串联来生成查询。</Resolution>
   <Owner>RuleOwner</Owner>
   <Url>http://msdn.microsoft.com/library/ms182310.aspx</Url>
   <Email />
   <MessageLevel Certainty="75">Warning</MessageLevel>
   <File Name="dataflowrules.dll" Version="********" />
  </Rule>
 </Rules>
 <Localized>
  <String Key="Category">类别</String>
  <String Key="Certainty">确定性</String>
  <String Key="CollapseAll">全部折叠</String>
  <String Key="CheckId">检查 ID</String>
  <String Key="Error">错误</String>
  <String Key="Errors">错误</String>
  <String Key="ExpandAll">全部展开</String>
  <String Key="Help">帮助</String>
  <String Key="Line">行</String>
  <String Key="Messages">消息</String>
  <String Key="LocationNotStoredInPdb">[位置未存储在 Pdb 中]</String>
  <String Key="Project">项目</String>
  <String Key="Resolution">解析</String>
  <String Key="Rule">规则</String>
  <String Key="RuleFile">规则文件</String>
  <String Key="RuleDescription">规则说明</String>
  <String Key="Source">源</String>
  <String Key="Status">状态</String>
  <String Key="Target">目标</String>
  <String Key="Warning">警告</String>
  <String Key="Warnings">警告</String>
  <String Key="ReportTitle">代码分析报告</String>
 </Localized>
</FxCopReport>

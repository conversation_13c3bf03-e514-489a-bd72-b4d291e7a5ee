﻿using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class TestPage : System.Web.UI.Page
{
    protected void Page_Load(object sender, EventArgs e)
    {
        if (InputParams.isDebug)
        {
            TestFun();
            if (IsPostBack)
            {
                t1.Attributes["value"] = t1.Text;
            }
        }
        else
        {
            Response.StatusCode = 404;
            Response.End();
        }
    }

    void TestFun()
    {
        Response.Write(Request.Path);
    }

    protected void Button1_Click(object sender, EventArgs e)
    {
        Literal1.Text = t1.Text;
    }
}
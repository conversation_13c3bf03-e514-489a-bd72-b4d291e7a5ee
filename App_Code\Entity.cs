﻿using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections;

/// <summary>
/// 数据类型
/// </summary>
public enum EnumFieldType
{
    charType = 11,//字符型
    longcharType = 12,//长字符型
    numberType = 13,//数值型
    dateType = 14,//日期型
    boolType = 15,//布尔型
    picType = 16,//图像型
    enumType = 17,//枚举型
    nameType =18,  //用户执行者
    doubleType=19  //浮点型

}

/// <summary>
/// 排序类型
/// </summary>
public enum EnumSortType
{
    NoSort = 0,//不排序
    ASC = 1,//升序
    DESC = 2//降序
}


/// <summary>
/// 枚举类型
/// </summary>
public struct EnumField
{
    public int enumItem;//对应的整型数值
    public string itemDetail;//该整型数值对应的枚举内容,具体的描述信息

    public EnumField(int eItem, string iDetail)
    {
        enumItem = eItem;
        itemDetail = iDetail;
    }
}

/// <summary>
/// 定义评价的枚举类型
/// </summary>
public enum EnumEvaluate
{
    Excellence = 1,//优秀
    Well = 2,//良好
    SoSo = 3,//基本完成
    UnFinish = 4,//未达标
    VeryBad=5  //非常不达标

}
/// <summary>
///定义表中的字段
/// </summary>

public struct Field
{
    public int position;//字段在页面中显示的次序
    public string fieldName; //字段在数据库中的名称
    public string fieldShowName;//字段显示的名称
    public int fieldTableShowLength;//字段显示在客户端表格中的宽度
    public EnumFieldType fieldType;//字段的数据类型，是枚举类型
    public EnumSortType sortType;//该字段的排序方式，是枚举类型
    public int enumTag; //如果某个字段类型是enumType（枚举类型），则此成员标志着该字段对应的枚举数组的索引
    public string linkPage;//如果需要对某个字段执行查找操作，可通过此项设置查找链接的页面
    public int sortOrder; //该字段排序的次序
    public string attentionMessage;//创建对象时，如果该字段未填写出现的提示信息,添加记录时，如果该成员不为空，则该字段为必填项
    public bool show; // 是否显示该字段,默认显示

    public Field(int pos, string fldName, string fldShowName, int fldTableShowLength, EnumFieldType fldType, EnumSortType sType, int eTag, string lkPage, int sOrder, string attMessage)
    {
        position = pos;
        fieldName = fldName;
        fieldShowName = fldShowName;
        fieldTableShowLength = fldTableShowLength;
        fieldType = fldType;
        sortType = sType;
        enumTag = eTag;
        linkPage = lkPage;
        sortOrder = sOrder;
        attentionMessage = attMessage;
        show = true;
    }
}


/// <summary>
///表之间的连接
/// </summary>
public struct Join
{
    public string joinField;//当前表的字段
    public string joinRTable;//当前表要连接的表
    public string joinRField;//被连接的表的连接字段

    public Join(string jonField, string jonRTable, string jonRField)
    {
        joinField = jonField;
        joinRTable = jonRTable;
        joinRField = jonRField;
    }
}


/// <summary>
///定义 表
/// </summary>
public struct Table
{
    public string tableName;//表名
    public Join join;//表的连接信息
    public Field[] fields;//表中的字段

    public Table(string tblName, Join jon, Field[] flds)
    {
        tableName = tblName;
        join = jon;
        fields = flds;
    }
}


/// <summary>
/// 主要的输入参数
/// </summary>
public class InputParams
{

    #region  定义多表查找，批量删除操作的连接参数
    //部署服务器数据库连接参数
    public const string DataBaseServer = "127.0.0.1";
    public const string userName = "sa";
    public const string password = "******";
    public const string dbName = "5city";

    // 测试服务器数据库连接参数
    //public const string DataBaseServer = "10.1.1.27";
    //public const string userName = "sa";
    //public const string password = "******";
    //public const string dbName = "5city";

    //public const string connectionStr = " Data Source=" + DataBaseServer + "; uid=" + userName + "; pwd=" + password + "; DATABASE=" + dbName;
    public static readonly string connectionStr;

    public static readonly bool isDebug;
    // 静态构造函数，在类首次被使用时执行一次
    static InputParams()
    {
        string value = Environment.GetEnvironmentVariable("NUMBER_OF_PROCESSORS");
        if (value != null && value.ToLower().Equals("1"))
        {
            connectionStr = " Data Source=127.0.0.1; uid=sa; pwd=******; DATABASE=5city";
            isDebug = false;
        }
        else
        {
            connectionStr = " Data Source=47.97.252.186; uid=sa; pwd=******; DATABASE=5city";
            isDebug = true;
        }
    }

    #endregion

    #region 显示参数定义
    public const int userSize = 50;//将查看用户的记录显示50条
    public const int pageSize = 20;//查找结果输出表中每页显示的记录数目
    public const int itemSize = 5;//查找结果输出表中每页显示的记录数目
    public const string lblHeadColor = "#42426f";//头部标签的前景色
    public const string lblFieldShowNameColor = "#871f78";//字段显示名称显示的前景色
    public const string rbYNColor = "#97694f";//单选按钮标签内容的前景色

    public const string tbxOnmouseoverColor = " #EFFFFF";//鼠标放上去时文本框颜色
    public const string tbxOnmouseoutColor = "#FFFFFF";//鼠标移走时文本框颜色
    public const string resultTableColor = "#adeaea";//查找结果表的背景色


    public const string rowColor1 = "#E0FFFF";//定义查找结果表的相邻两行的背景色
    public const string rowColor2 = "#E8EBFC";
    public const string rowColor3 = "#E2EAF4";//定义手机端主页表格显示颜色
    public const string rowColorLongChar = "#FFFFFF";//长字符串的背景色

    public const int tbxCompanyNameLength = 200;
    //查找的字段为字符型时，填写查找条件公司名称的文本框的显示宽度
    public const int tbxCharLength = 250;
    //查找的字段为字符型时，填写查找条件的文本框的显示宽度
    public const int tbxLongCharLength = 500;
    //查找的字段为长字符型时，填写查找条件的文本框的显示宽度
    public const int tbxPicLength = 160;
    //查找的字段为图像时，填写查找条件的文本框的显示宽度（查找内容为图像地址的相关信息）
    public const int tbxNumberLength = 100;
    //查找的字段为数值型时，填写查找条件的文本框的显示宽度
    public const int tbxDateLength = 120;
    //查找的字段为日期型时，填写查找条件的文本框的显示宽度
    public const int ddlWidth = 140;
    //下拉列表框的显示宽度
    public const int ddlHeight = 60;
    //下拉列表框的显示高度
    public const int ddlTimeWidth = 60;
    //对时、分查找时，从下拉列表选择时和分，以上变量为这些时分下拉列表的宽度 
    public const int ckbLength = 30;
    //复选框的宽度
    public const int tbxHeight = 22;
    //文本框的高度
    public const int tbxLongCharHeight = 40;
    //长字符串的高度
    public const int btnLength = 100;
    //按钮的长度
    public const int picTableHeight = 80;
    //图片显示的高度
    public const int longCharShowHeight = 20;
    //长字符串显示的高度
    public const int lkbSearch = 7;
    //查找链接的显示宽度
    public const int lkbModify = 7;
    //修改链接的显示宽度
    public const int lkbReport = 7;
    //汇报链接的显示宽度
    public const int lkbExamine = 7;
    //审核链接的显示宽度
    public const int lkbDelete = 7;
    //删除链接的显示宽度
    public const int lkbClose = 7;
    //删除链接的显示宽度
    public const int lkbSelect= 100;
    //选择链接的显示宽度
    #endregion

    #region 一般常量定义
    // 验证码过期时间
    public const int verificationCodeTimeoutMinutes = 5;
    #endregion

    #region 定义任务的状态    
    public const int EXECUTING = 0; //执行中
    public const int PAUSED = 1; //已暂停
    public const int REPORTED = 5; //已汇报,待审核
    public const int AUDITED = 6; //已审核,待关闭
    public const int CLOSED = 7;//已关闭 
    #endregion
}

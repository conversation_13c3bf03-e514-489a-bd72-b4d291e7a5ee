﻿using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Data.SqlClient;

public partial class ChooseCooperation : System.Web.UI.Page
{
    /// <summary>
    /// 声明全局变量
    /// </summary>
    int recordCount;
    int pageCount;
    int fieldsCount;
    int longNum;
    int pagerIndex;
    int rowSpan;
    int colSpan;
    int[] picLongchar;
    int[] fieldTableShowLength;
    string subSearchStr;
    int lineLength = 1;
    int group_id = 0;
    //为页面美观，自适应的改变字符型及长字符型字段查找的输入文本框的长度
    public const int tbxCharLength = 250;
    public const int tbxLongCharLength = 400;
    public const string ChooseTable = "Cooperation";
    public const string ChooseTableKey = "id";
    public const string ReturnField = "firm_name";
    readonly string ConnStr = InputParams.connectionStr;
    int iHM = 1;

    /// <summary>
    /// 实体初始化
    /// </summary>
    /// <returns></returns>
    /// 

    public EnumField[][] EnumField()
    {
        EnumField[] enum0 = { new EnumField(1, "男"), new EnumField(2, "女") };
        EnumField[][] efTs = { enum0 };
        return efTs;
    }

    public Table[] Tables()
    {
        Field fld0 = new Field(0, "firm_name", "公司名", 80, EnumFieldType.charType, 0, 0, "", 0, "");
        Field fld1 = new Field(1, "telephone", "联系电话", 100, EnumFieldType.charType, 0, 0, "", 0, "");
        Field fld2 = new Field(2, "last_follow_time", "最后联系时间", 100, EnumFieldType.dateType, 0, 0, "", 0, "");
        Field fld3 = new Field(3, "executor_id", "跟踪者", 110, EnumFieldType.charType, 0, 0, "", 0, "");
        Field[] flds = { fld0, fld1, fld2, fld3 };
        string tblName = "Cooperation";
        Join jon = new Join();
        Table table = new Table(tblName, jon, flds);
        Table[] tables = { table };
        return tables;
    }


    /// <summary>
    /// 页面加载
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void Page_Load(object sender, EventArgs e)
    {
        WebCustomControl1_1.PageSize = InputParams.userSize;
        WebCustomControl1_1.ItemSize = InputParams.itemSize;

        WebCustomControl1_2.PageSize = InputParams.userSize;
        WebCustomControl1_2.ItemSize = InputParams.itemSize;


        GenerateSearchMould();
        string searchStr = "";
        string sortExpression = "";
        string sortDirection = "";
        pagerIndex = 1;

        if (Request.Cookies["user"] != null)
        {
            HttpCookie cookie = Request.Cookies["user"];
            // group_id = int.Parse(cookie.Values["group_id"].ToString());
        }

        if (!Page.IsPostBack)
        {
            searchStr = GetSearchStr();
            ViewState["searchStr"] = searchStr;
            ViewState["subSearchStr"] = subSearchStr;
        }
        if (ViewState["sortExpression"] != null)
        {
            sortExpression = ViewState["sortExpression"].ToString();
            sortDirection = ViewState["sortDirection"].ToString();
        }

        if (ViewState["searchStr"] != null)
        {
            searchStr = ViewState["searchStr"].ToString();
        }

        try
        {

            DataTable dt = GetSearchResultTable(searchStr, sortExpression, sortDirection);
            if ((ViewState["pagerIndex"] != null) && (pageCount >= int.Parse(ViewState["pagerIndex"].ToString())))
            {
                pagerIndex = int.Parse(ViewState["pagerIndex"].ToString());
            }
            else
            {
                pagerIndex = 1;

            }
            ViewState["pagerIndex"] = pagerIndex;
            ShowResult(dt, sortExpression, sortDirection);
        }
        catch (SqlException ex)
        {
            Response.Write(ex.ToString());
        }

    }

    /// <summary>
    /// 选择单个用户
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void lkbSelect_Command(object sender, CommandEventArgs e)
    {
        //string ModifyParams; //定义从修改页面的参数
        string ModifyParams;//提取获得参数
        string ModifyUrl;        //定义修改任务页面后的地址

        string strTaskID = e.CommandArgument.ToString();
        string[] arrStrTaskID = strTaskID.Split(' ');
        int taskID = int.Parse(arrStrTaskID[0].ToString());
        string supPage = Session["supPage"].ToString();
        string[] arrReturnField = new string[1];
        string[] arrKey = new string[1];

        SqlConnection conn = new SqlConnection(ConnStr);
        conn.Open();


        string strSql;
        strSql = "SELECT " + ReturnField + " FROM " + ChooseTable + " WHERE " + ChooseTableKey + "=" + "" + taskID + "";
        SqlCommand comm = new SqlCommand(strSql, conn);
        SqlDataReader dr = comm.ExecuteReader();

        if (dr.Read())
        {
            arrKey[0] = arrStrTaskID[0].ToString();
            arrReturnField[0] = dr[0].ToString();
            Session["CooperateID"] = arrStrTaskID[0].ToString();//用来传递任务的用户名ID
            Session["firm_name"] = arrReturnField[0];//用来传递已查找到的用户名
        }
        if (supPage == "ModifyRecommend.aspx")
        {
            supPage = supPage + "?id=1";
            Session["cooperate_name"] = arrReturnField[0];//用来传递已查找到的用户名
           Session["tbxCooperateMemberNames"] = arrReturnField;
           Session["tbxCooperateMemberIDs"] = arrKey;
                                  
        }

        dr.Close();
       
        Response.Redirect(supPage);



    }


    /// <summary>
    /// 修改单个用户
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>

    //protected void lkbModify_Command(object sender, CommandEventArgs e)
    //{
    //}





    /// <summary>
    /// 分页事件处理
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void pager1_Click(object sender, EventArgs e)
    {
        pagerIndex = WebCustomControl1_1.CurrentPageIndex;
        WebCustomControl1_2.CurrentPageIndex = pagerIndex;

        ViewState["pagerIndex"] = pagerIndex;

        string sortExpression = "";
        string sortDirection = "";
        string searchStr = ViewState["searchStr"].ToString();

        if (ViewState["sortExpression"] != null)
        {
            sortExpression = ViewState["sortExpression"].ToString();
            sortDirection = ViewState["sortDirection"].ToString();
        }
        GenerateSearchMould();
        DataTable dt = GetSearchResultTable(searchStr, sortExpression, sortDirection);
        ShowResult(dt, sortExpression, sortDirection);
    }

    protected void pager2_Click(object sender, EventArgs e)
    {
        pagerIndex = WebCustomControl1_2.CurrentPageIndex;
        WebCustomControl1_1.CurrentPageIndex = pagerIndex;

        ViewState["pagerIndex"] = pagerIndex;

        string sortExpression = "";
        string sortDirection = "";
        string searchStr = ViewState["searchStr"].ToString();

        if (ViewState["sortExpression"] != null)
        {
            sortExpression = ViewState["sortExpression"].ToString();
            sortDirection = ViewState["sortDirection"].ToString();
        }
        GenerateSearchMould();
        DataTable dt = GetSearchResultTable(searchStr, sortExpression, sortDirection);
        ShowResult(dt, sortExpression, sortDirection);
    }

    /// <summary>
    /// 排序事件处理
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void lkb_Command(object sender, CommandEventArgs e)
    {
        string searchStr = ViewState["subSearchStr"].ToString();
        string sortExpression = e.CommandName;
        string sortDirection;

        if ((ViewState["sortExpression"] != null) && (ViewState["sortExpression"].ToString() == e.CommandName))
        {
            if ((ViewState["sortDirection"] != null) && (ViewState["sortDirection"].ToString() != "ASC"))
            {
                sortDirection = "ASC";
            }
            else
            {
                sortDirection = "DESC";
            }
        }
        else
        {
            sortDirection = "ASC";
            ViewState["sortExpression"] = e.CommandName;
        }

        ViewState["sortDirection"] = sortDirection;
        GenerateSearchMould();
        DataTable dt = GetSearchResultTable(searchStr, sortExpression, sortDirection);
        ShowResult(dt, sortExpression, sortDirection);


        WebCustomControl1_1.CurrentPageIndex = pagerIndex;
        WebCustomControl1_1.TotalRecord = recordCount;

        WebCustomControl1_2.CurrentPageIndex = pagerIndex;
        WebCustomControl1_2.TotalRecord = recordCount;
    }

    /// <summary>
    /// 查找事件处理
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void btnSearch_Click(object sender, EventArgs e)
    {
        string searchStr = GetSearchStr();
        ViewState["searchStr"] = searchStr;
        ViewState["subSearchStr"] = subSearchStr;

        string sortExpression = "";
        string sortDirection = "";

        GenerateSearchMould();

        DataTable dt = GetSearchResultTable(searchStr, sortExpression, sortDirection);
        ShowResult(dt, sortExpression, sortDirection);
    }



    /// <summary>
    ///批处理选择并返回
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void btnChoose_Click(object sender, EventArgs e)
    {
        int chooseCount = 0;
        //从添加任务页面或管理员首页过来的
        {

            for (int i = 0; i < placeHolder2.Controls.Count; i++)
            {
                string str = this.placeHolder2.Controls[i].GetType().ToString();
                if (str == "System.Web.UI.WebControls.CheckBox")
                {
                    string strCkb = placeHolder2.Controls[i].ID.ToString();
                    if (((CheckBox)placeHolder2.FindControl(strCkb)).Checked)
                    {
                        chooseCount++;
                    }
                }
            }

            int[] ckbsChoose = new int[chooseCount];
            int chooseIndex = 0;
            for (int i = 0; i < placeHolder2.Controls.Count; i++)
            {
                string str = this.placeHolder2.Controls[i].GetType().ToString();
                if (str == "System.Web.UI.WebControls.CheckBox")
                {
                    string strCkb = placeHolder2.Controls[i].ID.ToString();
                    if (((CheckBox)placeHolder2.FindControl(strCkb)).Checked)
                    {
                        string[] arrCkb = strCkb.Split(' ');
                        int RecordId = int.Parse(arrCkb[0]);
                        ckbsChoose[chooseIndex] = RecordId;
                        chooseIndex++;
                    }
                }
            }

            string supPage = Session["supPage"].ToString();

            if (ckbsChoose.Length > 0)
            {
                SqlConnection conn = new SqlConnection(ConnStr);
                conn.Open();
                string strSql;
                string[] arrReturnField = new string[ckbsChoose.Length];
                string[] arrKey = new string[ckbsChoose.Length];

                SqlCommand comm;
                SqlDataReader dr;
                for (int choIndex = 0; choIndex < ckbsChoose.Length; choIndex++)
                {
                    strSql = "SELECT " + ReturnField + " FROM " + ChooseTable + " WHERE " + ChooseTableKey + "=" + "'" + int.Parse(ckbsChoose[choIndex].ToString()) + "'";
                    comm = new SqlCommand(strSql, conn);
                    dr = comm.ExecuteReader();


                    if (dr.Read())
                    {
                        arrKey[choIndex] = ckbsChoose[choIndex].ToString();
                        arrReturnField[choIndex] = dr[0].ToString();
                    }

                    dr.Close();
                    if (arrKey.Length == 0)
                    {
                        ClientScript.RegisterClientScriptBlock(this.GetType(), "info", "<script language=\"javascript\" type=\"text/javascript\">alert(\"您没有选择用户！\");</script>");

                    }
                }



                if (supPage.Trim().ToString() == "AddTask.aspx")
                {
                    string tbxName = Request.QueryString["tbxName"].ToString();
                    Session[tbxName + "0"] = arrReturnField;
                    string tbxName2 = tbxName + "02";
                    Session[tbxName2] = arrKey;
                }
                else
                    if (supPage.Trim().ToString() == "ModifyTasks.aspx")
                    {
                        try
                        {
                            // string tbxName = Request.QueryString["tbxName"].ToString();
                            Session["ModifyTasksChooseNames"] = arrReturnField;
                            //string tbxName2 = tbxName + "02";
                            Session["ModifyTasksIDs"] = arrKey;
                        }
                        catch
                        {
                        }
                    }

                    else
                        if (supPage.Trim().ToString() == "AddTasks.aspx")
                        {
                            string tbxName = Request.QueryString["tbxName"].ToString();
                            int addIndex = int.Parse(Session["addIndex"].ToString());
                            int AddCount = int.Parse(Session["AddCount"].ToString());

                            for (int aIndex = addIndex; aIndex <= AddCount; aIndex++)
                            {
                                string str1 = tbxName + aIndex;
                                Session[str1] = arrReturnField;
                                string str2 = tbxName + aIndex + "2";
                                Session[str2] = arrKey;
                            }

                        }
                        else
                            if ((supPage.Trim().ToString() == "Default.aspx"))
                            {
                                Session["tbxMemberNames"] = arrReturnField;
                                Session["tbxMemberIDs"] = arrKey;
                            }
                            else
                                if ((supPage.Trim().ToString() == "SeeReport.aspx"))
                                {
                                    Session["tbxMemberNames2"] = arrReturnField;
                                    Session["tbxMemberIDs2"] = arrKey;
                                }
                                else
                                    if ((supPage.Trim().ToString() == "SeeRecommendList.aspx"))
                                    {
                                        Session["tbxCooperateMemberNames"] = arrReturnField;
                                        Session["tbxCooperateMemberIDs"] = arrKey;
                                    }
                string strDire = supPage;//定义返回地址
                if (supPage.Trim().ToString() == "ModifyTasks.aspx")//若是修改任务页面来的id=2
                    strDire += "?id=2";
                else
                    strDire += "?id=1";
                Response.Redirect(strDire);

            }
        }
    }

    /// <summary>
    /// 生成查找模板
    /// </summary>
    protected void GenerateSearchMould()
    {
        placeHolder1.Controls.Clear();
        EnumField[][] efTs = EnumField();
        Table[] tables = Tables();
        Label lblHr1 = new Label();
        lblHr1.Text = "<hr/>";
        placeHolder1.Controls.Add(lblHr1);

        Label lblBr1 = new Label();
        lblBr1.Text = "<br/>";
        placeHolder1.Controls.Add(lblBr1);

        fieldsCount = 0;
        for (int tableIndex = 0; tableIndex < tables.Length; tableIndex++)
        {
            for (int fieldIndex = 0; fieldIndex < tables[tableIndex].fields.Length; fieldIndex++)
            {
                fieldsCount++;
            }
        }

        picLongchar = new int[fieldsCount];
        fieldTableShowLength = new int[fieldsCount];
        Label lblHead = new Label();
        lblHead.Text = " 设置查找条件：<br/>";
        lblHead.ForeColor = System.Drawing.ColorTranslator.FromHtml(InputParams.lblHeadColor);
        placeHolder1.Controls.Add(lblHead);

        int iLine = 0;
        int iPosition;

        #region forTable
        for (int tableIndex = 0; tableIndex < tables.Length; tableIndex++)
        {
            #region forField
            for (int fieldIndex = 0; fieldIndex < tables[tableIndex].fields.Length; fieldIndex++)
            {
                iPosition = tables[tableIndex].fields[fieldIndex].position;

                if ((iLine != 0) && (iLine % lineLength) == 0)
                {
                    Label lblBrs = new Label();
                    lblBrs.Text = "<br/>";
                    placeHolder1.Controls.Add(lblBrs);
                }
                iLine++;
                if (iLine % lineLength == 0)
                {
                    Literal ltlSpaces = new Literal();
                    ltlSpaces.Text = "&nbsp; &nbsp; &nbsp; ";
                    placeHolder1.Controls.Add(ltlSpaces);
                }

                Label lbl = new Label();
                lbl.Text = tables[tableIndex].fields[fieldIndex].fieldShowName + "：";
                lbl.ForeColor = System.Drawing.ColorTranslator.FromHtml(InputParams.lblFieldShowNameColor);
                placeHolder1.Controls.Add(lbl);

                fieldTableShowLength[iLine - 1] = int.Parse(tables[tableIndex].fields[fieldIndex].fieldTableShowLength.ToString());

                #region switch
                string strFldName = tables[tableIndex].fields[fieldIndex].fieldName + iLine.ToString();

                switch (tables[tableIndex].fields[fieldIndex].fieldType)
                {
                    case EnumFieldType.charType:
                        picLongchar[iPosition] = 11;

                        if (tables[tableIndex].fields[fieldIndex].fieldName == "executor_id")
                        {
                            DropDownList ddl22 = new DropDownList();
                            ddl22.ID = "ddl" + strFldName;
                            // int enumT = tables[tableIndex].fields[fieldIndex].enumTag;
                            ddl22.Width = InputParams.ddlWidth;
                            ddl22.AutoPostBack = false;

                            SqlConnection conn1 = new SqlConnection(ConnStr);

                            SqlDataAdapter dap = new SqlDataAdapter("SELECT DISTINCT nickname,user_id FROM Users where  group_id=1", conn1);
                            DataTable dt = new DataTable();
                            dap.Fill(dt);
                            ddl22.DataSource = dt;
                            ddl22.DataTextField = "nickname";
                            ddl22.DataValueField = "user_id";
                            ddl22.DataBind();
                            ListItem li = new ListItem();
                            li.Text = "请选择";
                            li.Value = "请选择";
                            li.Selected = true;
                            ddl22.Items.Add(li);
                            placeHolder1.Controls.Add(ddl22);
                        }
                        else
                        {
                            TextBox tbx1 = new TextBox();
                            tbx1.ID = "tbx" + strFldName;
                            tbx1.ToolTip = "不同的关键字之间请以空格隔开！";
                            tbx1.Width = tbxCharLength;
                            tbx1.Height = InputParams.tbxHeight;
                            tbx1.TextMode = TextBoxMode.MultiLine;
                            tbx1.Wrap = true;
                            tbx1.Style.Add("overflow", "hidden");
                            placeHolder1.Controls.Add(tbx1);
                        }

                        //((TextBox)placeHolder1.FindControl("tbx" + strFldName)).Attributes.Add("onMouseOver", "this.style.backgroundColor='" + InputParams.tbxOnmouseoverColor + "';");
                        //((TextBox)placeHolder1.FindControl("tbx" + strFldName)).Attributes.Add("onMouseOut", "this.style.backgroundColor='" + InputParams.tbxOnmouseoutColor + "';");
                        continue;

                    case EnumFieldType.longcharType:
                        picLongchar[iPosition] = 12;
                        TextBox tbx2 = new TextBox();
                        tbx2.ID = "tbx" + strFldName;
                        tbx2.ToolTip = "请输入字段包含的文字，不同的关键字之间请以空格隔开！";
                        tbx2.Width = InputParams.tbxLongCharLength;
                        tbx2.Height = InputParams.tbxHeight;
                        tbx2.TextMode = TextBoxMode.MultiLine;
                        placeHolder1.Controls.Add(tbx2);
                        //((TextBox)placeHolder1.FindControl("tbx" + strFldName)).Attributes.Add("onMouseOver", "this.style.backgroundColor='" + InputParams.tbxOnmouseoverColor + "';");
                        //((TextBox)placeHolder1.FindControl("tbx" + strFldName)).Attributes.Add("onMouseOut", "this.style.backgroundColor='" + InputParams.tbxOnmouseoutColor + "';");
                        continue;

                    case EnumFieldType.numberType:
                        picLongchar[iPosition] = 13;
                        TextBox tbx3 = new TextBox();
                        tbx3.ID = "tbx" + strFldName + "1";
                        tbx3.Width = InputParams.tbxNumberLength;
                        tbx3.Height = InputParams.tbxHeight;

                        Label lblDao1 = new Label();
                        lblDao1.Text = "到";
                        lblDao1.ForeColor = System.Drawing.ColorTranslator.FromHtml(InputParams.lblFieldShowNameColor);
                        TextBox tbx4 = new TextBox();
                        tbx4.ID = "tbx" + strFldName + "2";
                        tbx4.Width = InputParams.tbxNumberLength;
                        tbx4.Height = InputParams.tbxHeight;

                        placeHolder1.Controls.Add(tbx3);
                        placeHolder1.Controls.Add(lblDao1);
                        placeHolder1.Controls.Add(tbx4);

                        //((TextBox)placeHolder1.FindControl("tbx" + strFldName + "1")).Attributes.Add("onMouseOver", "this.style.backgroundColor='" + InputParams.tbxOnmouseoverColor + "';");
                        //((TextBox)placeHolder1.FindControl("tbx" + strFldName + "1")).Attributes.Add("onMouseOut", "this.style.backgroundColor='" + InputParams.tbxOnmouseoutColor + "';");
                        //((TextBox)placeHolder1.FindControl("tbx" + strFldName + "2")).Attributes.Add("onMouseOver", "this.style.backgroundColor='" + InputParams.tbxOnmouseoverColor + "';");
                        //((TextBox)placeHolder1.FindControl("tbx" + strFldName + "2")).Attributes.Add("onMouseOut", "this.style.backgroundColor='" + InputParams.tbxOnmouseoutColor + "';");
                        continue;

                    case EnumFieldType.dateType:
                        picLongchar[iPosition] = 14;
                        TextBox tbx5 = new TextBox();
                        tbx5.ID = "tbx" + iLine.ToString() + strFldName + "1";
                        tbx5.Width = InputParams.tbxDateLength;
                        tbx5.Height = InputParams.tbxHeight;
                        placeHolder1.Controls.Add(tbx5);

                        if (iHM == 1)
                        {
                            Literal ltlSpace1 = new Literal();
                            ltlSpace1.Text = "&nbsp;";
                            placeHolder1.Controls.Add(ltlSpace1);

                            DropDownList ddlH1 = new DropDownList();
                            ddlH1.ID = "ddl" + iLine.ToString() + strFldName + "H1";
                            ddlH1.Width = InputParams.ddlTimeWidth;
                            for (int iH = 0; iH <= 24; iH++)
                            {
                                ListItem li = new ListItem();
                                if (iH == 0)
                                {
                                    li.Text = "";
                                }
                                else
                                {
                                    li.Text = iH.ToString();
                                    li.Value = iH.ToString();
                                }
                                ddlH1.Items.Add(li);
                            }
                            placeHolder1.Controls.Add(ddlH1);

                            Label lblH1 = new Label();
                            lblH1.Text = "时";
                            placeHolder1.Controls.Add(lblH1);

                            DropDownList ddlM1 = new DropDownList();
                            ddlM1.ID = "ddl" + iLine.ToString() + strFldName + "M1";
                            ddlM1.Width = InputParams.ddlTimeWidth;

                            for (int iM = 0; iM <= 60; iM++)
                            {
                                ListItem li = new ListItem();
                                if (iM == 0)
                                {
                                    li.Text = "";
                                }
                                else
                                {
                                    li.Text = iM.ToString();
                                    li.Value = iM.ToString();
                                }
                                ddlM1.Items.Add(li);
                            }
                            placeHolder1.Controls.Add(ddlM1);

                            Label lblM1 = new Label();
                            lblM1.Text = "分";
                            placeHolder1.Controls.Add(lblM1);

                            Literal ltlSpace2 = new Literal();
                            ltlSpace2.Text = "&nbsp;";
                            placeHolder1.Controls.Add(ltlSpace2);

                            Label lblDao2 = new Label();
                            lblDao2.Text = "到";
                            lblDao2.ForeColor = System.Drawing.ColorTranslator.FromHtml(InputParams.lblFieldShowNameColor);
                            placeHolder1.Controls.Add(lblDao2);


                            Literal ltlSpace3 = new Literal();
                            ltlSpace3.Text = "&nbsp;";
                            placeHolder1.Controls.Add(ltlSpace3);

                            TextBox tbx6 = new TextBox();
                            tbx6.ID = "tbx" + iLine.ToString() + strFldName + "2";
                            tbx6.Width = InputParams.tbxDateLength;
                            tbx6.Height = InputParams.tbxHeight;
                            placeHolder1.Controls.Add(tbx6);

                            Literal ltlSpace4 = new Literal();
                            ltlSpace4.Text = "&nbsp;";
                            placeHolder1.Controls.Add(ltlSpace4);

                            DropDownList ddlH2 = new DropDownList();
                            ddlH2.ID = "ddl" + iLine.ToString() + strFldName + "H2";
                            ddlH2.Width = InputParams.ddlTimeWidth;
                            for (int iH = 0; iH <= 24; iH++)
                            {
                                ListItem li = new ListItem();
                                if (iH == 0)
                                {
                                    li.Text = "";
                                }
                                else
                                {
                                    li.Text = iH.ToString();
                                    li.Value = iH.ToString();
                                }
                                ddlH2.Items.Add(li);
                            }
                            placeHolder1.Controls.Add(ddlH2);

                            Label lblH2 = new Label();
                            lblH2.Text = "时";
                            placeHolder1.Controls.Add(lblH2);

                            DropDownList ddlM2 = new DropDownList();
                            ddlM2.ID = "ddl" + iLine.ToString() + strFldName + "M2";

                            ddlM2.Width = InputParams.ddlTimeWidth;
                            for (int iM = 0; iM <= 60; iM++)
                            {
                                ListItem li = new ListItem();
                                if (iM == 0)
                                {
                                    li.Text = "";
                                }
                                else
                                {
                                    li.Text = iM.ToString();
                                    li.Value = iM.ToString();
                                }
                                ddlM2.Items.Add(li);
                            }
                            placeHolder1.Controls.Add(ddlM2);

                            Label lblM2 = new Label();
                            lblM2.Text = "分";
                            placeHolder1.Controls.Add(lblM2);

                            Literal ltlSpace5 = new Literal();
                            ltlSpace5.Text = "&nbsp;";
                            placeHolder1.Controls.Add(ltlSpace5);
                        }
                        else
                        {
                            Label lblDao2 = new Label();
                            lblDao2.Text = "到";
                            lblDao2.ForeColor = System.Drawing.ColorTranslator.FromHtml(InputParams.lblFieldShowNameColor);
                            placeHolder1.Controls.Add(lblDao2);

                            TextBox tbx6 = new TextBox();
                            tbx6.ID = "tbx" + iLine.ToString() + strFldName + "2";
                            tbx6.Width = InputParams.tbxDateLength;
                            tbx6.Height = InputParams.tbxHeight;
                            placeHolder1.Controls.Add(tbx6);
                        }

                        ((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString() + strFldName + "1")).Attributes.Add("onClick", "javascript:calendar()");
                        ((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString() + strFldName + "2")).Attributes.Add("onClick", "javascript:calendar()");
                        continue;

                    case EnumFieldType.boolType:
                        picLongchar[iPosition] = 15;
                        RadioButton rbY = new RadioButton();
                        RadioButton rbN = new RadioButton();

                        rbY.ID = "rb" + strFldName + "1";
                        rbY.GroupName = "gn" + tables[tableIndex].fields[fieldIndex].fieldName;
                        rbY.Text = "是";
                        rbY.ForeColor = System.Drawing.ColorTranslator.FromHtml(InputParams.rbYNColor);
                        placeHolder1.Controls.Add(rbY);

                        rbN.ID = "rb" + strFldName + "2";
                        rbN.GroupName = "gn" + tables[tableIndex].fields[fieldIndex].fieldName;
                        rbN.Text = "否";
                        rbN.ForeColor = System.Drawing.ColorTranslator.FromHtml(InputParams.rbYNColor);
                        placeHolder1.Controls.Add(rbN);
                        //((RadioButton)placeHolder1.FindControl("rb" + strFldName + "1")).Attributes.Add("onMouseOver", "this.style.backgroundColor='" + InputParams.tbxOnmouseoverColor + "';");
                        //((RadioButton)placeHolder1.FindControl("rb" + strFldName + "1")).Attributes.Add("onMouseOut", "this.style.backgroundColor='" + InputParams.tbxOnmouseoutColor + "';");
                        //((RadioButton)placeHolder1.FindControl("rb" + strFldName + "2")).Attributes.Add("onMouseOver", "this.style.backgroundColor='" + InputParams.tbxOnmouseoverColor + "';");
                        //((RadioButton)placeHolder1.FindControl("rb" + strFldName + "2")).Attributes.Add("onMouseOut", "this.style.backgroundColor='" + InputParams.tbxOnmouseoutColor + "';");
                        continue;

                    case EnumFieldType.picType:
                        picLongchar[iPosition] = 16;
                        TextBox tbx7 = new TextBox();
                        tbx7.ID = "tbx" + strFldName;
                        tbx7.ToolTip = "请输入图片路径！";
                        tbx7.Width = tbxCharLength;
                        tbx7.Height = InputParams.tbxHeight;
                        tbx7.TextMode = TextBoxMode.MultiLine;
                        tbx7.Wrap = true;
                        tbx7.Style.Add("overflow", "hidden");
                        placeHolder1.Controls.Add(tbx7);
                        //((TextBox)placeHolder1.FindControl("tbx" + strFldName)).Attributes.Add("onMouseOver", "this.style.backgroundColor='" + InputParams.tbxOnmouseoverColor + "';");
                        //((TextBox)placeHolder1.FindControl("tbx" + strFldName)).Attributes.Add("onMouseOut", "this.style.backgroundColor='" + InputParams.tbxOnmouseoutColor + "';");
                        continue;

                    case EnumFieldType.enumType:
                        picLongchar[iPosition] = tables[tableIndex].fields[fieldIndex].enumTag;
                        DropDownList ddl = new DropDownList();
                        ddl.ID = "ddl" + strFldName;
                        int enumT = tables[tableIndex].fields[fieldIndex].enumTag;
                        ddl.Width = InputParams.ddlWidth;
                        ddl.AutoPostBack = false;
                        placeHolder1.Controls.Add(ddl);

                        ListItem liTop = new ListItem();
                        liTop.Text = " 请选择 ";
                        ddl.Items.Add(liTop);
                        for (int enumLen = 0; enumLen < efTs[enumT].Length; enumLen++)
                        {
                            ListItem li = new ListItem();
                            li.Value = efTs[enumT][enumLen].enumItem.ToString();
                            li.Text = efTs[enumT][enumLen].itemDetail;
                            ddl.Items.Add(li);
                        }
                        //((DropDownList)placeHolder1.FindControl("ddl" + strFldName)).Attributes.Add("onMouseOver", "this.style.backgroundColor='" + InputParams.tbxOnmouseoverColor + "';");
                        //((DropDownList)placeHolder1.FindControl("ddl" + strFldName)).Attributes.Add("onMouseOut", "this.style.backgroundColor='" + InputParams.tbxOnmouseoutColor + "';");
                        continue;
                }

                #endregion switch
            }
            #endregion forField
        }
        #endregion  forTable
        longNum = 0;
        for (int i = 0; i < picLongchar.Length; i++)
        {
            if (picLongchar[i] == 12)
            {
                longNum++;
            }
        }
        rowSpan = longNum + 1;
        colSpan = fieldsCount - longNum;

    }


    /// <summary>
    /// 获得查找字符串
    /// </summary>
    /// <returns></returns>
    protected string GetSearchStr()
    {
        Table[] tables = Tables();

        string strSearch;
        string strSelect = "SELECT DISTINCT ";
        string strFrom = " FROM ";
        string strWhere = " WHERE  1=1";
        string strOrderBy = "";
        int tag = 0;
        int iposition;
        string[] arrSearchStr = new string[fieldsCount];

        #region forTable
        int iLine = 0;

        for (int tableIndex = 0; tableIndex < tables.Length; tableIndex++)
        {
            if (tableIndex == 0)
            {
                strFrom += tables[tableIndex].tableName;
            }
            else
            {
                strFrom += "," + tables[tableIndex].tableName;
            }
            if (tables[tableIndex].join.joinField != null)
            {
                strWhere += " AND " + tables[tableIndex].tableName + "." + tables[tableIndex].join.joinField + "=" + tables[tableIndex].join.joinRTable + "." + tables[tableIndex].join.joinRField;
            }

            #region forField
            for (int fieldIndex = 0; fieldIndex < tables[tableIndex].fields.Length; fieldIndex++)
            {
                iLine++;
                iposition = tables[tableIndex].fields[fieldIndex].position;
                if (tables[tableIndex].fields[fieldIndex].sortType != EnumSortType.NoSort)
                {
                    if (tag == 0)
                    {
                        strOrderBy += " ORDER BY " + tables[tableIndex].tableName + "." + tables[tableIndex].fields[fieldIndex].fieldName + " " + tables[tableIndex].fields[fieldIndex].sortType;
                        tag = 1;
                    }
                    else
                        if (tag == 1)
                        {
                            strOrderBy += " ," + tables[tableIndex].tableName + "." + tables[tableIndex].fields[fieldIndex].fieldName + " " + tables[tableIndex].fields[fieldIndex].sortType;
                        }
                }

                if (iposition == 0)
                {
                    arrSearchStr[iposition] = " " + tables[tableIndex].tableName + "." + tables[tableIndex].fields[fieldIndex].fieldName + " AS " + "'" + tables[tableIndex].fields[fieldIndex].fieldShowName + "'";
                }
                else
                {
                    arrSearchStr[iposition] = ",";
                    arrSearchStr[iposition] += " " + tables[tableIndex].tableName + "." + tables[tableIndex].fields[fieldIndex].fieldName + " AS " + "'" + tables[tableIndex].fields[fieldIndex].fieldShowName + "'";
                }

                string strFldName = tables[tableIndex].fields[fieldIndex].fieldName + iLine.ToString();
                switch (tables[tableIndex].fields[fieldIndex].fieldType)
                {

                    case EnumFieldType.numberType:
                        if ((((TextBox)placeHolder1.FindControl("tbx" + strFldName + "1")).Text != "") && (((TextBox)placeHolder1.FindControl("tbx" + strFldName + "2")).Text != ""))
                        {
                            strWhere += " AND ";
                            strWhere += tables[tableIndex].tableName + "." + tables[tableIndex].fields[fieldIndex].fieldName + " >= " + float.Parse(((TextBox)placeHolder1.FindControl("tbx" + strFldName + "1")).Text);
                            strWhere += " AND ";
                            strWhere += tables[tableIndex].tableName + "." + tables[tableIndex].fields[fieldIndex].fieldName + " <= " + float.Parse(((TextBox)placeHolder1.FindControl("tbx" + strFldName + "2")).Text);
                        }
                        continue;

                    case EnumFieldType.dateType:
                        if ((((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString() + strFldName + "1")).Text != "") && (((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString() + strFldName + "2")).Text != ""))
                        {
                            DropDownList ddlH1 = (DropDownList)placeHolder1.FindControl("ddl" + iLine.ToString() + strFldName + "H1");
                            DropDownList ddlH2 = (DropDownList)placeHolder1.FindControl("ddl" + iLine.ToString() + strFldName + "H2");
                            DropDownList ddlM1 = (DropDownList)placeHolder1.FindControl("ddl" + iLine.ToString() + strFldName + "M1");
                            DropDownList ddlM2 = (DropDownList)placeHolder1.FindControl("ddl" + iLine.ToString() + strFldName + "M2");

                            int iH1 = 0;
                            int iH2 = 0;
                            int iM1 = 0;
                            int iM2 = 0;

                            if (iHM == 1)
                            {
                                if (ddlH1.Text != "")
                                {
                                    iH1 = int.Parse(ddlH1.Text.ToString());
                                }
                                if (ddlH2.Text != "")
                                {
                                    iH2 = int.Parse(ddlH2.Text.ToString());
                                }
                                if (ddlM1.Text != "")
                                {
                                    iM1 = int.Parse(ddlM1.Text.ToString());
                                }
                                if (ddlM2.Text != "")
                                {
                                    iM2 = int.Parse(ddlM2.Text.ToString());
                                }
                            }
                            string strDataTime1 = ((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString() + strFldName + "1")).Text.ToString() + " " + iH1 + ":" + iM1 + ":" + "00";
                            string strDataTime2 = ((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString() + strFldName + "2")).Text.ToString() + " " + iH2 + ":" + iM2 + ":" + "00";

                            strWhere += " AND ";
                            strWhere += tables[tableIndex].tableName + "." + tables[tableIndex].fields[fieldIndex].fieldName + " >= " + "'" + DateTime.Parse(strDataTime1) + "'";
                            strWhere += " AND ";
                            strWhere += tables[tableIndex].tableName + "." + tables[tableIndex].fields[fieldIndex].fieldName + "<= " + "'" + DateTime.Parse(strDataTime2) + "'";

                        }
                        continue;


                    case EnumFieldType.enumType:
                        string strCho = "请选择";
                        string strDdl = ((DropDownList)placeHolder1.FindControl("ddl" + strFldName)).Text.Trim();
                        if (strDdl != strCho.Trim())
                        {
                            int enumItem = int.Parse(strDdl);
                            strWhere += " AND ";
                            strWhere += tables[tableIndex].tableName + "." + tables[tableIndex].fields[fieldIndex].fieldName + " =" + enumItem;
                        }
                        continue;

                    case EnumFieldType.boolType:

                        if (((RadioButton)placeHolder1.FindControl("rb" + strFldName + "1")).Checked)
                        {
                            strWhere += " AND ";
                            strWhere += tables[tableIndex].tableName + "." + tables[tableIndex].fields[fieldIndex].fieldName + " =1";
                        }
                        else
                            if (((RadioButton)placeHolder1.FindControl("rb" + strFldName + "2")).Checked)
                            {
                                strWhere += " AND ";
                                strWhere += tables[tableIndex].tableName + "." + tables[tableIndex].fields[fieldIndex].fieldName + " =0";
                            }
                        continue;

                    default:
                        if (tables[tableIndex].fields[fieldIndex].fieldName == "executor_id")
                        {
                            if (((DropDownList)placeHolder1.FindControl("ddl" + strFldName)).Text.Trim() != "请选择")
                            {
                                string strDdl_executor = ((DropDownList)placeHolder1.FindControl("ddl" + strFldName)).SelectedValue.ToString().Trim();
                                strWhere += " AND " + tables[tableIndex].tableName + "." + tables[tableIndex].fields[fieldIndex].fieldName + " =" + strDdl_executor;
                            }
                        }
                        else
                            if (((TextBox)placeHolder1.FindControl("tbx" + strFldName)).Text != "")
                            {
                                strWhere += " AND " + tables[tableIndex].tableName + "." + tables[tableIndex].fields[fieldIndex].fieldName + " like" + "'%";
                                string str = ((TextBox)placeHolder1.FindControl("tbx" + strFldName)).Text;
                                string[] strs = str.Split(' ');
                                for (int i = 0; i < strs.Length; i++)
                                {
                                    if (i != strs.Length - 1)
                                    {
                                        strWhere += strs[i].ToString();
                                        strWhere += "%";
                                    }
                                    else
                                    {
                                        strWhere += strs[i].ToString();
                                        strWhere += "%'";
                                    }
                                }
                            }
                        continue;
                }
            }
            #endregion forField
        }
        #endregion forTable
        int arrSearchStrIndex;
        for (arrSearchStrIndex = 0; arrSearchStrIndex < arrSearchStr.Length; arrSearchStrIndex++)
        {
            strSelect += arrSearchStr[arrSearchStrIndex];
        }

        strSearch = strSelect + " ," + ChooseTable + "." + ChooseTableKey + " " + strFrom + strWhere + " " + strOrderBy;
        subSearchStr = strSelect + " ," + ChooseTable + "." + ChooseTableKey + " " + strFrom + strWhere;

        return strSearch;
    }


    /// <summary>
    /// 生成查找结果表
    /// </summary>
    /// <param name="searchStr"></param>
    /// <param name="sortExpression"></param>
    /// <param name="sortDirection"></param>
    /// <returns></returns>
    protected DataTable GetSearchResultTable(string searchStr, string sortExpression, string sortDirection)
    {
        string connStr = ConnStr;
        SqlConnection conn = new SqlConnection(connStr);
        SqlDataAdapter da;
        if (conn.State.ToString() == "Closed")
        {
            conn.Open();
        }
        if (sortExpression != "")
        {
            searchStr = ViewState["subSearchStr"].ToString() + " ORDER BY " + sortExpression + " " + sortDirection;
        }

        da = new SqlDataAdapter(searchStr, connStr);

        DataTable dt = new DataTable();
        da.Fill(dt);

        recordCount = dt.Rows.Count;
        if (recordCount % InputParams.pageSize == 0)
        {
            pageCount = recordCount / InputParams.pageSize;
        }
        else
        {
            pageCount = (recordCount / InputParams.pageSize) + 1;
        }

        if (conn.State.ToString() == "Open")
        {
            conn.Close();
        }
        return dt;
    }


    /// <summary>
    /// 将查找结果表输出
    /// </summary>
    /// <param name="dt"></param>
    /// <param name="sortExpression"></param>
    /// <param name="sortDirection"></param>
    protected void ShowResult(DataTable dt, string sortExpression, string sortDirection)
    {
        placeHolder2.Controls.Clear();
        placeHolder2.EnableViewState = true;
        EnumField[][] efTs = EnumField();

        //生成表头strTop
        //string str1 = "<table id='tableBody'   border='1' style='background-color:" + InputParams.resultTableColor + "'>" + "<tr style='color:Blue'>" + "<td  style='color:#871f78; width:" + InputParams.ckbLength + "'>";
        string str1 = "<table id='tableBody'   border='1'>" + "<tr style='color:Blue'>" + "<td  style='width:" + InputParams.ckbLength + "'>";

        Literal ltl1 = new Literal();
        ltl1.Text = str1;
        placeHolder2.Controls.Add(ltl1);

        Literal ltl2 = new Literal();
        ltl2.Text = "<input id='CheckAll' type='checkbox' onclick='selectAll(this);'/>全选</td>";
        placeHolder2.Controls.Add(ltl2);

        string lkbStr = "";
        for (int ColIndex = 0; ColIndex < fieldsCount; ColIndex++)
        {

            if (picLongchar[ColIndex] != 12)
            {
                //lkbStr = "<td style='font-size:15px; width:" + fieldTableShowLength[ColIndex];
                lkbStr = "<td style=' width:" + fieldTableShowLength[ColIndex];

                Literal ltl3 = new Literal();
                ltl3.Text = lkbStr + "'>";
                placeHolder2.Controls.Add(ltl3);

                LinkButton lkb = new LinkButton();
                lkb.CommandName = dt.Columns[ColIndex].Caption;
                lkb.Command += new CommandEventHandler(lkb_Command);
                lkb.ID = "lkb" + dt.Columns[ColIndex].Caption;
                placeHolder2.Controls.Add(lkb);

                if (dt.Columns[ColIndex].Caption == sortExpression)
                {
                    if (sortDirection == "ASC")
                    {
                        lkb.Text = dt.Columns[ColIndex].Caption + "↑";
                    }
                    else
                        if (sortDirection == "DESC")
                        {
                            lkb.Text = dt.Columns[ColIndex].Caption + "↓";
                        }
                }
                else
                {
                    lkb.Text = dt.Columns[ColIndex].Caption;
                }

                Literal ltl4 = new Literal();
                ltl4.Text = "</td>";
                placeHolder2.Controls.Add(ltl4);
            }
        }

        Literal ltl5 = new Literal();
        ltl5.Text = "<td width='" + InputParams.lkbSelect + "'>选择用户</td></tr>";
        placeHolder2.Controls.Add(ltl5);

        //生成表的内容
        if (dt.Rows.Count != 0)
        {
            string strRowColor = "#FFFFFF";

            if (pagerIndex != pageCount)
            {
                for (int rdCount = 0; rdCount < InputParams.userSize; rdCount++)
                {
                    if (rdCount % 2 == 0)
                    {
                        strRowColor = InputParams.rowColor1;
                    }

                    else
                    {
                        strRowColor = InputParams.rowColor2;
                    }

                    try
                    {
                        Literal ltl11 = new Literal();
                        ltl11.Text = "<tr style ='background-color :" + strRowColor + "'>" + "<td  rowspan='" + rowSpan.ToString() + "'>";
                        placeHolder2.Controls.Add(ltl11);

                        CheckBox ckb = new CheckBox();
                        ckb.ID = dt.Rows[(pagerIndex - 1) * InputParams.userSize + rdCount][fieldsCount].ToString() + " " + rdCount;
                        string strLinkID = ckb.ID;
                        placeHolder2.Controls.Add(ckb);

                        Literal ltl12 = new Literal();
                        ltl12.Text = " </td>";
                        placeHolder2.Controls.Add(ltl12);
                        AddRow(dt, rdCount, efTs, strLinkID);

                    }
                    catch
                    {
                        goto label1;   //如果没有50行,后面空白就不再显示了,
                    }
                }
            label1: Response.Write("<br/>");
            }
            else
            {
                for (int rdCount = 0; rdCount < recordCount - (pagerIndex - 1) * InputParams.userSize; rdCount++)
                {
                    if (rdCount % 2 == 0)
                    {
                        strRowColor = InputParams.rowColor1;
                    }

                    else
                    {
                        strRowColor = InputParams.rowColor2;
                    }
                    Literal ltl11 = new Literal();
                    ltl11.Text = "<tr style ='background-color :" + strRowColor + "'>" + "<td  rowspan='" + rowSpan.ToString() + "'>";
                    placeHolder2.Controls.Add(ltl11);

                    CheckBox ckb = new CheckBox();
                    ckb.ID = dt.Rows[(pagerIndex - 1) * InputParams.pageSize + rdCount][fieldsCount].ToString() + " " + rdCount;
                    string strLinkID = ckb.ID;

                    placeHolder2.Controls.Add(ckb);

                    Literal ltl12 = new Literal();
                    ltl12.Text = " </td>";
                    placeHolder2.Controls.Add(ltl12);
                    AddRow(dt, rdCount, efTs, strLinkID);

                }
            }

            Literal ltl6 = new Literal();
            ltl6.Text = "</table>";
            placeHolder2.Controls.Add(ltl6);

            WebCustomControl1_1.TotalRecord = recordCount;
            WebCustomControl1_2.TotalRecord = recordCount;

        }
    }



    /// <summary>
    /// 向页面输出的表格中添加一行
    /// </summary>
    /// <param name="dt"></param>
    /// <param name="rdCount"></param>
    /// <param name="efTs"></param>
    void AddRow(DataTable dt, int rdCount, EnumField[][] efTs, string strLinkID)//strLinkID是用来连接一行中的关键字
    {

        string strLine1 = "";
        string strLine2 = "";

        string strRowColor = "#FFFFFF";
        if (rdCount % 2 == 0)
        {
            strRowColor = InputParams.rowColor1;
        }

        else
        {
            strRowColor = InputParams.rowColor2;
        }
        for (int ColIndex = 0; ColIndex < fieldsCount; ColIndex++)
        {
            switch (picLongchar[ColIndex])
            {
                case 11:
                    if (dt.Columns[ColIndex].ColumnName == "跟踪者")
                    {
                        strLine1 += "<td>" + (new Common()).GetNickname(int.Parse(dt.Rows[(pagerIndex - 1) * InputParams.pageSize + rdCount][ColIndex].ToString())) + "</td>";
                    }
                    else
                        strLine1 += "<td>" + dt.Rows[(pagerIndex - 1) * InputParams.pageSize + rdCount][ColIndex] + "</td>";
                    continue;
                case 12:
                    strLine2 += "<tr style ='background-color :" + strRowColor + "'>";
                    strLine2 += "<td style='height:" + InputParams.longCharShowHeight + "'  colspan='" + colSpan.ToString() + "'>" + dt.Columns[ColIndex].Caption + "：" + dt.Rows[(pagerIndex - 1) * InputParams.pageSize + rdCount][ColIndex] + "</td></tr>";
                    continue;

                case 13:
                    strLine1 += "<td>" + dt.Rows[(pagerIndex - 1) * InputParams.pageSize + rdCount][ColIndex] + "</td>";
                    continue;

                case 14:
                    strLine1 += "<td>" + dt.Rows[(pagerIndex - 1) * InputParams.pageSize + rdCount][ColIndex] + "</td>";
                    continue;

                case 15:
                    strLine1 += "<td>" + dt.Rows[(pagerIndex - 1) * InputParams.pageSize + rdCount][ColIndex] + "</td>";
                    continue;
                case 16:
                    strLine1 += "<td><img  alt=''  height='" + InputParams.picTableHeight + "'  src='" + dt.Rows[(pagerIndex - 1) * InputParams.pageSize + rdCount][ColIndex] + "'/></td>";
                    continue;

                default:
                    int enumItem = int.Parse(dt.Rows[(pagerIndex - 1) * InputParams.pageSize + rdCount][ColIndex].ToString());
                    int enumT = picLongchar[ColIndex];
                    int enumLen;
                    for (enumLen = 0; enumLen < efTs[enumT].Length; enumLen++)
                    {
                        if (efTs[enumT][enumLen].enumItem == enumItem)
                        {
                            break;
                        }
                    }
                    if (enumLen == efTs[enumT].Length)
                    {
                        enumLen = enumLen - 1;
                    }
                    strLine1 += "<td>" + efTs[enumT][enumLen].itemDetail + "</td>";
                    continue;
            }
        }

        //添加用户的选择按键
        Literal ltlSelect = new Literal();
        ltlSelect.Text = strLine1 + "<td  rowspan='" + rowSpan.ToString() + "'>";
        placeHolder2.Controls.Add(ltlSelect);

        LinkButton lkbSelect = new LinkButton();
        lkbSelect.ID = "lkbSelect " + strLinkID;
        lkbSelect.Text = "选定并返回";
        lkbSelect.Command += new CommandEventHandler(lkbSelect_Command);
        lkbSelect.CommandArgument = strLinkID;
        placeHolder2.Controls.Add(lkbSelect);

        Literal ltl16 = new Literal();
        ltl16.Text = "</td></tr>" + strLine2;
        placeHolder2.Controls.Add(ltl16);

    }
}
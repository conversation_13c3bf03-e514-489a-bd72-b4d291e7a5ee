﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Web;

/// <summary>
/// Logger 的摘要说明
/// </summary>
public class Logger
{
    public Logger()
    {
        //
        // TODO: 在此处添加构造函数逻辑
        //
    }

    static readonly string logFile;

    static Logger()
    {
        string LogPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Logs");
        Directory.CreateDirectory(LogPath);
        logFile = Path.Combine(LogPath, "log.txt");
    }


    public static void Log(string message, params string[] otherMessages)
    {
        using (StreamWriter writer = new StreamWriter(logFile, true))
        {
            writer.WriteLine(string.Format("{0}:\t{1}\t{2}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), message, string.Join(",", otherMessages)));
        }
    }
}
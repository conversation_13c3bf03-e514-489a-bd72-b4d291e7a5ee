﻿/// <summary>
/// R 的摘要说明
/// </summary>
public class R
{
    // 使用公共自动属性并添加初始化值
    public int Code { get; private set; }
    public string Msg { get; private set; }
    public object Data { get; private set; }

    // 默认构造函数
    public R() { }

    // 带参数构造函数
    public R(int code, string msg, object data = null)
    {
        Code = code;
        Msg = msg;
        Data = data;
    }

    // 静态方法：成功响应，不带数据
    public static R Ok()
    {
        return new R { Code = 0, Msg = "ok" };
    }

    // 静态方法：成功响应，带自定义消息
    public static R Ok(string msg)
    {
        return new R { Code = 0, Msg = msg };
    }

    // 静态方法：成功响应，带数据
    public static R Ok<T>(T data)
    {
        return new R { Code = 0, Msg = "ok", Data = data };
    }

    // 静态方法：失败响应，不带数据
    public static R Fail()
    {
        return new R { Code = -1, Msg = "fail" };
    }

    // 静态方法：失败响应，带自定义消息
    public static R Fail(string msg)
    {
        return new R { Code = -1, Msg = msg };
    }

    // 静态方法：失败响应，带错误代码和消息
    public static R Fail(int code, string msg)
    {
        return new R { Code = code, Msg = msg };
    }

    // 重写 ToString 方法，方便调试输出
    public override string ToString()
    {
        return  string.Format("Code: {0}, Msg: {1}, Data: {2}",Code,Msg,Data);
    }
}

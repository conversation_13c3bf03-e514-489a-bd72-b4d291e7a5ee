﻿// 输入框html内容预览

function hp_ie_y(e){
	var t=e.offsetTop;  
	while(e=e.offsetParent){  
		t+=e.offsetTop;  
	}  
	return t;  
}  
function hp_ie_x(e){
	var l=e.offsetLeft;  
	while(e=e.offsetParent){  
		l+=e.offsetLeft;  
	}  
	return l;  
}
function HtmlPreview(objName)
{
	var obj = document.getElementById(objName);
	var div = document.getElementById("divHtml");
	var x, y;
	if(document.all){
		div.style.top = hp_ie_y(obj);
		div.style.left = hp_ie_x(obj);
	}
	div.style.display="";
	div.style.width="auto";
	div.style.height="auto";
	div.style.background="#EEEEEE";
	div.style.border="3px solid #DDDDDD";
	div.style.padding="10px";
	div.innerHTML=obj.value;
}

document.write("<div id=\"divHtml\" name=\"divHtml\" style=\"display:none;position:absolute;text-align:left\" onclick=\"divHtml.style.display='none'\" title=\"点击关闭\"></div>");

// 重定textarea大小
function ResizeArea(objName, resize)
{
	var obj = document.getElementById(objName);
	if (obj.rows<=4 && resize<0 || resize==0){
		return;
	}
	if (resize>0){
		obj.style.width = "90%";
	}
	obj.rows = obj.rows + resize;
}
﻿using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Data.SqlClient;
using System.Collections.Generic;
using Newtonsoft.Json;

public partial class AddRecord : System.Web.UI.Page
{
    readonly string ConnStr = InputParams.connectionStr;
    private class ImageData
    {
        public string name { get; set; }
        public string type { get; set; }
        public string data { get; set; }
    }


    protected void Page_Load(object sender, EventArgs e)
    {   
        if (Request.Params["id"] == null)
            Response.Redirect("Default.aspx");
        if (!IsPostBack)
        {
            //if (FollowTime.Text == "")
            //{
            //    FollowTime.Text = DateTime.Now.ToString();

            //    //DateTime nextFollowTime = DateTime.Now;
            //    //if (TextBox2.Text != "")
            //    //{
            //    //    nextFollowTime = nextFollowTime.AddDays(int.Parse(TextBox2.Text));
            //    //    NextFollowTime.Text = nextFollowTime.ToString();
            //    //}
            //}
            //FollowTime.Attributes.Add("onClick", "javascript:calendar()");

            // 初始加载时检查是否为空
        } 
    }
    //实时更新跟踪时间方法
    protected void txtDays_TextChanged(object sender, EventArgs e)
    {
        if (int.TryParse(txtDays.Text, out int days))
        {
            DateTime trackDate = DateTime.Now.AddDays(days);
            lblResult.Text = "下次跟踪时间：" + trackDate.ToString("yyyy-MM-dd HH:mm:ss");
        }
        else
        {
            lblResult.Text = "请输入有效天数";
        }
    }
    // 验证天数输入
    private bool ValidateDaysInput()
    {
        // 检查是否为空
        if (string.IsNullOrWhiteSpace(txtDays.Text))
        {
            lblError.Visible = true;
            return false;
        }

        // 检查是否为有效数字
        if (!int.TryParse(txtDays.Text, out int days) || days <= 0)
        {
            lblError.Text = "请输入有效天数（正整数）";
            lblError.Visible = true;
            return false;
        }

        lblError.Visible = false;
        return true;
    }
    //protected void Button1_Click(object sender, EventArgs e)
    //{
    //	// 首先验证天数输入
    //	if (!ValidateDaysInput())
    //	{
    //		// 验证失败，显示错误信息并返回
    //		lblError.Visible = true;
    //		return;
    //	}

    //	// 清除之前的错误信息
    //	lblError.Visible = false;

    //	SqlConnection conn = new SqlConnection(ConnStr);
    //	if (conn.State.ToString() == "Closed")
    //	{
    //		conn.Open();
    //	}

    //	try
    //	{
    //		string strSql = "";
    //		string strSql1 = "";
    //		//if (FollowTime.Text == "")
    //		//{
    //		//    FollowTime.Text = DateTime.Now.ToString();
    //		//}
    //		strSql = "insert into FollowRecord(follow_client_id,description,follow_time,follow_executor) values(" + Request.Params["id"].ToString() + ",'" + TextBox1.Text.Trim() + "','" + DateTime.Now + "'," + Session["user_id"].ToString() + ")";

    //		SqlCommand comm = new SqlCommand(strSql, conn);
    //		comm.ExecuteNonQuery();

    //		DateTime nowFollowTime = DateTime.Now;
    //		DateTime nextFollowTime = DateTime.Now;
    //		string strNextFollow = "";
    //		if (txtDays.Text != "")
    //		{
    //			nextFollowTime = nextFollowTime.AddDays(int.Parse(txtDays.Text));
    //			strNextFollow = "', next_follow_time='" + nextFollowTime;
    //		}
    //		strSql1 = "update FollowClient set last_follow_time='" + nowFollowTime.ToString() + strNextFollow + "' where id=" + Request.Params["id"].ToString();
    //		SqlCommand comm1 = new SqlCommand(strSql1, conn);
    //		comm1.ExecuteNonQuery();

    //		//更新的跟踪记录的次数
    //		(new Common()).UpdateFollowNumber(int.Parse(Request.Params["id"].ToString()));


    //		if (conn.State.ToString() == "Open")
    //		{
    //			conn.Close();
    //		}

    //		Response.Redirect("SeeDetail.aspx?TaskID=" + Request.Params["id"].ToString());
    //	}
    //	catch (Exception ee)
    //	{
    //		//ClientScript.RegisterClientScriptBlock(this.GetType(), "info", "<script language=\"javascript\" type=\"text/javascript\">alert(\"输入日期类型不对！\");</script>");
    //		Response.Write(ee.ToString());
    //	}
    //}
    protected void Button1_Click(object sender, EventArgs e)
    {
        // 首先验证天数输入
        if (!ValidateDaysInput())
        {
            // 验证失败，显示错误信息并返回
            lblError.Visible = true;
            return;
        }
        // 清除之前的错误信息
        lblError.Visible = false;

        SqlConnection conn = new SqlConnection(ConnStr);
        if (conn.State.ToString() == "Closed")
        {
            conn.Open();
        }

        try
        {
            string strSql = "";
            string strSql1 = "";
            //if (FollowTime.Text == "")
            //{
            //    FollowTime.Text = DateTime.Now.ToString();
            //}
            strSql = "insert into FollowRecord(follow_client_id,description,follow_time,follow_executor) values(" + Request.Params["id"].ToString() + ",'" + TextBox1.Text.Trim() + "','" + DateTime.Now + "'," + Session["user_id"].ToString() + ")";

            SqlCommand comm = new SqlCommand(strSql, conn);
            comm.ExecuteNonQuery();

            DateTime nowFollowTime = DateTime.Now;
            DateTime nextFollowTime = DateTime.Now;
            string strNextFollow = "";
            if (txtDays.Text != "")
            {
                nextFollowTime = nextFollowTime.AddDays(int.Parse(txtDays.Text));
                strNextFollow = "', next_follow_time='" + nextFollowTime;
            }
            strSql1 = "update FollowClient set last_follow_time='" + nowFollowTime.ToString() + strNextFollow + "' where id=" + Request.Params["id"].ToString();
            SqlCommand comm1 = new SqlCommand(strSql1, conn);
            comm1.ExecuteNonQuery();

            //更新的跟踪记录的次数
            (new Common()).UpdateFollowNumber(int.Parse(Request.Params["id"].ToString()));


            if (conn.State.ToString() == "Open")
            {
                conn.Close();
            }

            Response.Redirect("SeeDetail.aspx?TaskID=" + Request.Params["id"].ToString());
        }
        catch (Exception ee)
        {
            //ClientScript.RegisterClientScriptBlock(this.GetType(), "info", "<script language=\"javascript\" type=\"text/javascript\">alert(\"输入日期类型不对！\");</script>");
            Response.Write(ee.ToString());
        }
    }
    protected void btnSubmit_Click(object sender, EventArgs e)
    {
        // 首先验证天数输入
        if (!ValidateDaysInput())
        {
            // 验证失败，显示错误信息并返回
            lblError.Visible = true;
            return;
        }

        // 清除之前的错误信息
        lblError.Visible = false;

        using (SqlConnection conn = new SqlConnection(ConnStr))
        {
            conn.Open();
            SqlTransaction transaction = conn.BeginTransaction();

            try
            {
                // 插入随访记录
                string followSql = @"INSERT INTO FollowRecord (follow_client_id, description, follow_time, follow_executor) 
                                    OUTPUT INSERTED.ID 
                                    VALUES (@clientId, @description, @followTime, @executor)";

                using (SqlCommand cmd = new SqlCommand(followSql, conn, transaction))
                {
                    cmd.Parameters.AddWithValue("@clientId", Request.Params["id"]);
                    cmd.Parameters.AddWithValue("@description", TextBox1.Text.Trim());
                    cmd.Parameters.AddWithValue("@followTime", DateTime.Now);
                    cmd.Parameters.AddWithValue("@executor", Session["user_id"]);

                    int recordId = (int)cmd.ExecuteScalar();

                    // 处理图片上传
                    SaveImages(recordId, hdnImageData.Value, conn, transaction);
                }

                // 新增代码开始 - 更新客户随访时间信息
                DateTime nowFollowTime = DateTime.Now;
                DateTime nextFollowTime = DateTime.Now;
                string strNextFollow = "";

                // 假设 txtDays 是页面上用于输入下次随访天数的文本框
                if (!string.IsNullOrEmpty(txtDays.Text) && int.TryParse(txtDays.Text, out int days))
                {
                    nextFollowTime = nextFollowTime.AddDays(days);
                    strNextFollow = ", next_follow_time=@nextFollowTime";
                }
                else
                {
                    strNextFollow = ", next_follow_time=NULL";
                }

                string updateClientSql = "UPDATE FollowClient SET last_follow_time=@lastFollowTime" + strNextFollow +
                                        " WHERE id=@clientId";

                using (SqlCommand comm1 = new SqlCommand(updateClientSql, conn, transaction))
                {
                    comm1.Parameters.AddWithValue("@lastFollowTime", nowFollowTime);
                    comm1.Parameters.AddWithValue("@clientId", Request.Params["id"]);

                    if (!string.IsNullOrEmpty(txtDays.Text) && int.TryParse(txtDays.Text, out int parsedDays))
                    {
                        comm1.Parameters.AddWithValue("@nextFollowTime", nextFollowTime);
                    }

                    comm1.ExecuteNonQuery();
                }

            // 更新跟踪记录次数 (假设Common类已存在)
            (new Common()).UpdateFollowNumber(int.Parse(Request.Params["id"].ToString()), conn, transaction);
            // 新增代码结束
            transaction.Commit();
            transaction = null;
            Response.Redirect("SeeDetail.aspx?TaskID=" + Request.Params["id"].ToString());
                // 提交后关闭连接并重定向
                //    if (conn.State.ToString() == "Open")
                //{
                //    conn.Close();
                //}

            }
            catch (Exception ex)
            {
                if (transaction != null) {
                    transaction.Rollback();
                }
				Response.Write(ex.ToString());
                //Response.Redirect("SeeDetail.aspx?TaskID=" + Request.Params["id"].ToString());
                //Response.Write($"<script>alert('保存失败: {ex.Message}');</script>");
            }
            finally
            {
                // 确保事务被正确释放
                if (transaction != null)
                {
                    transaction.Dispose();
                }
                if (conn.State.ToString() == "Open")
                {
                    conn.Close();
                }
            }
        }
    }


    private void SaveImages(int recordId, string imageDataJson, SqlConnection conn, SqlTransaction transaction)
    {
        if (string.IsNullOrEmpty(imageDataJson)) return;

        // 解析上传的图片数据
        var images = JsonConvert.DeserializeObject<List<ImageData>>(imageDataJson);

        foreach (var img in images)
        {
            string imgSql = @"INSERT INTO FollowRecordImages (record_id, image_data, file_name, content_type,created_at) 
                              VALUES (@recordId, @imageData, @fileName, @contentType,@created)";

            using (SqlCommand cmd = new SqlCommand(imgSql, conn, transaction))
            {
                cmd.Parameters.AddWithValue("@recordId", recordId);
                cmd.Parameters.AddWithValue("@imageData", Convert.FromBase64String(img.data));
                cmd.Parameters.AddWithValue("@fileName", img.name);
                cmd.Parameters.AddWithValue("@contentType", img.type);
                cmd.Parameters.AddWithValue("@created", DateTime.Now);
                cmd.ExecuteNonQuery();
            }
        }
    }


}

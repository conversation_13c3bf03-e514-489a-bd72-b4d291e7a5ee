﻿using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Data.SqlClient;

public partial class AddRecord : System.Web.UI.Page
{
    const string connStr = " Data Source=" + InputParams.DataBaseServer + "; uid=" + InputParams.userName + "; pwd=" + InputParams.password + "; DATABASE=" + InputParams.dbName;
 
    protected void Page_Load(object sender, EventArgs e)
    {   
        if (Request.Params["id"] == null)
            Response.Redirect("Default.aspx");
        if (!IsPostBack)
        FollowTime.Text = "";
       // FollowTime.Text = DateTime.Now.ToShortDateString();
        //FollowTime.Attributes.Add("onClick", "javascript:calendar()");

    }
    protected void Button1_Click(object sender, EventArgs e)
    {

        SqlConnection conn = new SqlConnection(connStr);
        if (conn.State.ToString() == "Closed")
        {
            conn.Open();
        }

        try
        {
            string strSql = "";
            string strSql1="";
            if (FollowTime.Text!="")
            {
                strSql = "insert into FollowRecord(follow_client_id,description,follow_time,follow_executor) values(" + Request.Params["id"].ToString() + ",'" + TextBox1.Text.Trim() + "','" + DateTime.Parse(FollowTime.Text) + "'," + Session["user_id"].ToString() + ")";
            }
            else
            {
                strSql = "insert into FollowRecord(follow_client_id,description,follow_time,follow_executor) values(" + Request.Params["id"].ToString() + ",'" + TextBox1.Text.Trim() + "','" + DateTime.Now.ToString() + "'," + Session["user_id"].ToString() + ")";
            }
            SqlCommand comm = new SqlCommand(strSql, conn);
            comm.ExecuteNonQuery();

            if(TextBox3.Text != "")
            strSql1 = "update FollowClient set next_follow_time='" + DateTime.Now.AddDays(int.Parse(TextBox3.Text)).ToString()
                      + "',last_follow_time='" + DateTime.Now.ToString() + "' where id=" + Request.Params["id"].ToString();
            else
            strSql1 = "update FollowClient set last_follow_time='" + DateTime.Now.ToString() + "' where id=" + Request.Params["id"].ToString();
         
            SqlCommand comm1 = new SqlCommand(strSql1, conn);
            comm1.ExecuteNonQuery();

            //更新的跟踪记录的次数
            (new Common()).UpdateFollowNumber(int.Parse(Request.Params["id"].ToString()));
        

            if (conn.State.ToString() == "Open")
            {
                conn.Close();
            }

            Response.Redirect("SeeDetail.aspx?TaskID="+Request.Params["id"].ToString());
        }
        catch
        {
            ClientScript.RegisterClientScriptBlock(this.GetType(), "info", "<script language=\"javascript\" type=\"text/javascript\">alert(\"输入日期类型不对！\");</script>");

        }
    }
}

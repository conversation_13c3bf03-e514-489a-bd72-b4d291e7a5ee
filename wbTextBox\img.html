﻿<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="pop.css">
<script type="text/javascript" language="javascript" src="img.js"></script>

<script type="text/javascript" language="javascript">
<!--
var sAction = "INSERT";
var sTitle = "插入";

var oControl;
var oSeletion;
var sRangeType;

var sFromUrl = "http://";
var sAlt = "";
var sBorder = "0";
var sBorderColor = "#000000";
var sFilter = "";
var sAlign = "";
var sWidth = "";
var sHeight = "";
var sVSpace = "";
var sHSpace = "";

var sCheckFlag = "url";

var oFilter, oAlign, oFromurl, oAlt, oBorder, oBordercolor, oBordercolor, oWidth, oHeight, oVspace, oHspace, oCheckfromfile;
var oCheckfromurl, oFile, oUpload, oOk, oProcessing;

var wbtbWin;
if (window.opener){
	wbtbWin=launchParameters["wbtb"];
	oSelection = wbtbWin.document.getElementById("WBTB_Composition").contentWindow.document.selection.createRange();
	sRangeType = wbtbWin.document.getElementById("WBTB_Composition").contentWindow.document.selection.type;
}else{
	wbtbWin=dialogArguments;
	oSelection = wbtbWin.WBTB_Composition.document.selection.createRange();
	sRangeType = wbtbWin.WBTB_Composition.document.selection.type;
}


//if (window.opener){
	if (sRangeType == "Control") {
		if (oSelection.item(0).tagName == "IMG"){
			sAction = "MODI";
			sTitle = "修改";
			sCheckFlag = "url";
			oControl = oSelection.item(0);
			sFromUrl = oControl.src;
			sAlt = oControl.alt;
			sBorder = oControl.border;
			sBorderColor = oControl.style.borderColor;
			sFilter = oControl.style.filter;
			sAlign = oControl.align;
			sWidth = oControl.width;
			sHeight = oControl.height;
			sVSpace = oControl.vspace;
			sHSpace = oControl.hspace;
		}
	}
//}


document.write("<title>" + sTitle + "图片</title>");


// 初始值
function InitDocument(){
	oFromurl=document.getElementById("d_fromurl");
	oAlt=document.getElementById("d_alt");
	oBorder=document.getElementById("d_border");
	oBordercolor=document.getElementById("d_bordercolor");
	oWidth=document.getElementById("d_width");
	oHeight=document.getElementById("d_height");
	oVspace=document.getElementById("d_vspace");
	oHspace=document.getElementById("d_hspace");
	oCheckfromfile=document.getElementById("d_checkfromfile");
	oCheckfromurl=document.getElementById("d_checkfromurl");
	oFile=document.getElementById("d_file").contentWindow;
	oFilter=document.getElementById("d_filter");
	oAlign=document.getElementById("d_align");
	oProcessing=document.getElementById("divProcessing");
	oOk=document.getElementById("Ok");

	SearchSelectValue(oFilter, sFilter);
	SearchSelectValue(oAlign, sAlign.toLowerCase());

	oFromurl.value = sFromUrl;
	oAlt.value = sAlt;
	oBorder.value = sBorder;
	oBordercolor.value = sBorderColor;
	document.getElementById("s_bordercolor").style.backgroundColor = sBorderColor;
	oWidth.value = sWidth;
	oHeight.value = sHeight;
	oVspace.value = sVSpace;
	oHspace.value = sHSpace;
}


// 图片来源单选点击事件
function RadioClick(what){
	//oUpload=oFile.oUpload;
	if (what=="url"){
		oCheckfromfile.checked=false;
		oFromurl.disabled=false;
		oCheckfromurl.checked=true;
		//oUpload.disabled=true;
	}else{
		oCheckfromurl.checked=false;
		//oUpload.disabled=false;
		oCheckfromfile.checked=true;
		oFromurl.disabled=true;
	}
}


// 改变窗口大小
var b_imgWin = false;
function WindowResize(ord,oInput)
{
	if (ord)
	{
		if (window.opener){
			//window.resizeTo("22em","20em");
		}else{
			window.dialogWidth = "22em";
		}
		document.getElementById("t_preview").style.display="none";
		b_imgWin = false;
	}else{
		if (!b_imgWin)
		{
			if (window.opener){
				//window.resizeTo("40em","20em");
			}else{
				window.dialogWidth = "40em";
				window.dialogLeft = parseInt(window.dialogLeft) - 110;
			}
			document.getElementById("t_preview").style.display="";
			b_imgWin = true;
		}
		document.getElementById("i_preview").src=oInput.value;
	}
	return true;
}

// 上传帧调入完成时执行
function UploadLoaded(){
	// 初始radio
	RadioClick(sCheckFlag);
}

// 上传错误
function UploadError(sErrDesc){
	AbleItems();
	RadioClick('file');
	oProcessing.style.display="none";
	try {
		BaseAlert(oUpload,sErrDesc);
	}
	catch(e){}
}

// 文件上传完成时执行,带入上传文件名
function UploadSaved(sFileName){
	oFromurl.value = GetHttpUrl(sFileName);
	wbtbWin.AddUpload(sFileName);
	ReturnValue();
}

// 本窗口返回值
function ReturnValue(){
	sFromUrl = oFromurl.value;
	sAlt = oAlt.value;
	sBorder = oBorder.value;
	sBorderColor = oBordercolor.value;
	sFilter = oFilter.value;
	sAlign = oAlign.value;
	sWidth = oWidth.value;
	sHeight = oHeight.value;
	sVSpace = oVspace.value;
	sHSpace = oHspace.value;
	
	if (sFromUrl=="" || sFromUrl=="http://") {window.close();return;};	
	
	if (sAction == "MODI") {
		oControl.src = sFromUrl;
		oControl.alt = sAlt;
		oControl.border = sBorder;
		oControl.style.borderColor = sBorderColor;
		oControl.style.filter = sFilter;
		oControl.align = sAlign;
		if (sWidth != "") {oControl.width = sWidth} else oControl.removeAttribute("width") ;
		if (sHeight != "") {oControl.height = sHeight} else oControl.removeAttribute("height");
		oControl.vspace = sVSpace;
		oControl.hspace = sHSpace;
	}else{
		var sHTML = '<img src="'+sFromUrl+'" ';
		if (sAlt != "") {
			sHTML += ' alt="'+sAlt+'" ';
		}
		if (sBorder != "") {
			sHTML+= ' border="'+sBorder+'" ';
		}
		if (sFilter != "" || sBorder != "0") {
			sHTML += ' style="filter:'+sFilter+';border-color:'+sBorderColor+'" ';
		}
		if (sAlign != "") {
			sHTML += ' align="'+sAlign+'"';
		}
		if (sWidth!=""){
			sHTML=sHTML+' width="'+sWidth+'"';
		}
		if (sHeight!=""){
			sHTML=sHTML+' height="'+sHeight+'"';
		}
		sHTML = sHTML+' vspace="'+sVSpace+'" hspace="'+sHSpace+'"><br/>';
		wbtbWin.WBTB_InsertHtml(sHTML);
	}

//	window.returnValue = null;
	window.close();
}

// 点确定时执行
function ok(){
	// 数字型输入的有效性
	oBorder.value = ToInt(oBorder.value);
	oWidth.value = ToInt(oWidth.value);
	oHeight.value = ToInt(oHeight.value);
	oVspace.value = ToInt(oVspace.value);
	oHspace.value = ToInt(oHspace.value);
	
	if (oCheckfromurl.checked){
		// 返回值
		ReturnValue();
	}else{
		// 无值返回
		if (oUpload.value=="") {window.close(); return;};
		// 上传文件判断
		if (!oFile.CheckUploadForm()) return false;
		// 使各输入框无效
		DisableItems();
		// 显示正在上传图片
		oProcessing.style.display="";
		if (!document.all){
			oProcessing.innerHTML="<center>上传中请等待...</center>";
			oProcessing.style.backgroundColor="infobackground";
		}
		// 上传表单提交
		oFile.oForm.submit();
	}
}

// 使所有输入框无效
function DisableItems(){
	oCheckfromfile.disabled=true;
	oCheckfromurl.disabled=true;
	oFromurl.disabled=true;
	oAlt.disabled=true;
	oBorder.disabled=true;
	oBordercolor.disabled=true;
	oFilter.disabled=true;
	oAlign.disabled=true;
	oWidth.disabled=true;
	oHeight.disabled=true;
	oVspace.disabled=true;
	oHspace.disabled=true;
	oOk.disabled=true;
}

// 使所有输入框有效
function AbleItems(){
	oCheckfromfile.disabled=false;
	oCheckfromurl.disabled=false;
	oFromurl.disabled=false;
	oAlt.disabled=false;
	oBorder.disabled=false;
	oBordercolor.disabled=false;
	oFilter.disabled=false;
	oAlign.disabled=false;
	oWidth.disabled=false;
	oHeight.disabled=false;
	oVspace.disabled=false;
	oHspace.disabled=false;
	oOk.disabled=false;
}

function colorchange(obj,oImg)
{
	if (obj.value != "")
	{
		try {
		oImg.style.backgroundColor = obj.value;
		}
		catch(e)
		{
			alert("你输入的不是颜色！");
			obj.value = "";
			oImg.style.backgroundColor = "";
		}
	}
}


-->
</script>

<BODY bgColor="menu" onload="InitDocument()"  topmargin="10" leftmargin="15">

<table border="0" cellpadding="0" cellspacing="0" align="left" >
<tr style="padding:2 0">
	<td>
	<fieldset>
	<legend>图片来源</legend>
	<table border="0" cellpadding="0" cellspacing="0">
		<tr><td colspan="9" height="5"></td></tr>
		<tr>
			<td width="7"></td>
			<td width="54" align="right" onclick="RadioClick('url')">
			<input type="radio" id="d_checkfromurl" class="cb" value="1" onclick="RadioClick('url')">网络:</td>
			<td width="5"></td>
			<td colspan="5">
			<input type="text" id="d_fromurl" style="width:243px" size=30 value="" onchange="return WindowResize(0,this)"></td>
			<td width="7"></td>
		</tr>
		<tr><td colspan="9" height="5"></td></tr>
		<tr style="display:none;">
			<td width="7"></td>
			<td width="54" align="right" onclick="RadioClick('file')">
			<input type="radio" id="d_checkfromfile" class="cb" value="1" onclick="RadioClick('file')">上传:</td>
			<td width="5"></td>
			<td colspan="5">
			<iframe id="d_file" frameborder="0" src="../WBTB_upload.aspx" width="100%" height="22" scrolling=no></iframe>
			</td>
			<td width="7"></td>
		</tr>
		<tr style="display:none;"><td colspan="9" height="5"></td></tr>
		</table>
	</fieldset>
	</td>
</tr>
<tr style="padding:2 0">
	<td>
	<fieldset>
	<legend>显示效果</legend>
	<table style="padding:0 2" border="0" cellpadding="0" cellspacing="0">
	<tr><td colspan="9" height="5"></td></tr>
	<tr>
		<td width="7"></td>
		<td>说明文字:</td>
		<td width="5"></td>
		<td colspan="5"><input type="text" id="d_alt" size="38" value="" style="width:243px"></td>
		<td width="7"></td>
	</tr>
	<tr><td colspan="9" height="5"></td></tr>
	<tr>
		<td width="7"></td>
		<td noWrap>边框粗细:</td>
		<td width="5"></td>
		<td><input type="text" id="d_border" size="10" value="" ONKEYPRESS="event.returnValue=IsDigit();"></td>
		<td width="40"></td>
		<td noWrap>边框颜色:</td>
		<td width="5"></td>
		<td><table border="0" cellpadding="0" cellspacing="0"><tr><td>
		<input type="text" id="d_bordercolor" size=7 value="" onchange="colorchange(this,s_bordercolor);">
		</td>
		<td><img border="0" src="images/pickcolor.gif" width="18" id="s_bordercolor" onclick="selColor(d_bordercolor,this)">
		</td></tr></table></td>
		<td width=7></td>
	</tr>
	<tr><td colspan="9" height="5"></td></tr>
	<tr>
		<td width="7"></td>
		<td>特殊效果:</td>
		<td width="5"></td>
		<td>
			<select id="d_filter" style="width:72px" size="1">
			<option value='' selected>无</option>
			<option value='Alpha(Opacity=50)'>半透明</option>
			<option value='Alpha(Opacity=0, FinishOpacity=100, Style=1, StartX=0, StartY=0, FinishX=100, FinishY=140)'>线型透明</option>
			<option value='Alpha(Opacity=10, FinishOpacity=100, Style=2, StartX=30, StartY=30, FinishX=200, FinishY=200)'>放射透明</option>
			<option value='blur(add=1,direction=14,strength=15)'>模糊效果</option><option value='blur(add=true,direction=45,strength=30)'>风动模糊</option>
			<option value='Wave(Add=0, Freq=60, LightStrength=1, Phase=0, Strength=3)'>正弦波纹</option>
			<option value='gray'>黑白照片</option><option value='Chroma(Color=#FFFFFF)'>白色透明</option>
			<option value='DropShadow(Color=#999999, OffX=7, OffY=4, Positive=1)'>投射阴影</option>
			<option value='Shadow(Color=#999999, Direction=45)'>阴影</option>
			<option value='Glow(Color=#ff9900, Strength=5)'>发光</option>
			<option value='flipv'>垂直翻转</option>
			<option value='fliph'>左右翻转</option>
			<option value='grays'>降低彩色</option>
			<option value='xray'>X光照片</option>
			<option value='invert'>底片</option>
            </select>		
		</td>
		<td width="40"></td>
		<td>对齐方式:</td>
		<td width="5"></td>
		<td>
			<select id="d_align" size="1" style="width:72px">
			<option value='' selected="selected">默认</option>
			<option value='left'>居左</option>
			<option value='right'>居右</option>
			<option value='top'>顶部</option>
			<option value='middle'>中部</option>
			<option value='bottom'>底部</option>
			<option value='absmiddle'>绝对居中</option>
			<option value='absbottom'>绝对底部</option>
			<option value='baseline'>基线</option>
			<option value='texttop'>文本顶部</option>
			</select>
		</td>
		<td width="7"></td>
	</tr>
	<tr><td colspan="9" height="5"></td></tr>
	<tr>
		<td width="7"></td>
		<td>图片宽度:</td>
		<td width="5"></td>
		<td><input type="text" id="d_width" size="10" value="" ONKEYPRESS="event.returnValue=IsDigit();" maxlength=4></td>
		<td width="40"></td>
		<td>图片高度:</td>
		<td width="5"></td>
		<td><input type="text" id="d_height" size="10" value="" ONKEYPRESS="event.returnValue=IsDigit();" maxlength=4></td>
		<td width="7"></td>
	</tr>
	<tr><td colspan="9" height="5"></td></tr>
	<tr>
		<td width="7"></td>
		<td>上下间距:</td>
		<td width="5"></td>
		<td><input type="text" id="d_vspace" size="10" value="" ONKEYPRESS="event.returnValue=IsDigit();" maxlength=2></td>
		<td width="40"></td>
		<td>左右间距:</td>
		<td width="5"></td>
		<td><input type="text" id="d_hspace" size="10" value="" ONKEYPRESS="event.returnValue=IsDigit();" maxlength=2></td>
		<td width="7"></td>
	</tr>
	<tr><td colspan="9" height="5"></td></tr>
	</table>
	</fieldset>
	</td>
</tr>
<tr style="padding:2 0"><td align="right">
	<button id="Ok" onclick="ok()">  确定  </button>&nbsp;&nbsp;
	<button onclick="window.close();">  取消  </button>
</td></tr>
</table>

<table width="230" height="100%" id="t_preview" style="display:none"><tr><td align="center">
<img id="i_preview" src="" width=1 onload="this.width=180">
</td></tr></table>

<div id="divProcessing" style="width:200px;height:30px;position:absolute;left:70px;top:100px;display:none">
<table border="0" cellpadding="0" cellspacing="1" bgcolor="#000000" width="100%" height="100%"><tr><td bgcolor="infobackground"><marquee align="middle" behavior="alternate" scrollamount="5"><font color=#000000>图片上传中...请等待!</font></marquee></td></tr></table>
</div>

</body>
</html>
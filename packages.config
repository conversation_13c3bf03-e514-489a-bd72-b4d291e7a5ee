﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="AlibabaCloud.EndpointUtil" version="0.1.1" targetFramework="net472" />
  <package id="AlibabaCloud.GatewaySpi" version="0.0.3" targetFramework="net472" />
  <package id="AlibabaCloud.OpenApiClient" version="0.1.12" targetFramework="net472" />
  <package id="AlibabaCloud.OpenApiUtil" version="1.1.1" targetFramework="net472" />
  <package id="AlibabaCloud.SDK.Dysmsapi20170525" version="3.1.0" targetFramework="net472" />
  <package id="AlibabaCloud.TeaUtil" version="0.1.19" targetFramework="net472" />
  <package id="AlibabaCloud.TeaXML" version="0.0.5" targetFramework="net472" />
  <package id="Aliyun.Credentials" version="1.4.2" targetFramework="net472" />
  <package id="Microsoft.Bcl" version="1.1.10" targetFramework="net472" />
  <package id="Microsoft.Bcl.Build" version="1.0.14" targetFramework="net472" />
  <package id="Microsoft.CodeDom.Providers.DotNetCompilerPlatform" version="2.0.0" targetFramework="net472" />
  <package id="Microsoft.Net.Http" version="2.2.29" targetFramework="net472" />
  <package id="Newtonsoft.Json" version="13.0.3" targetFramework="net472" />
  <package id="System.IO" version="4.3.0" targetFramework="net472" />
  <package id="System.Net.Http" version="4.3.4" targetFramework="net472" />
  <package id="System.Runtime" version="4.3.0" targetFramework="net472" />
  <package id="System.Security.Cryptography.Algorithms" version="4.3.0" targetFramework="net472" />
  <package id="System.Security.Cryptography.Encoding" version="4.3.0" targetFramework="net472" />
  <package id="System.Security.Cryptography.Primitives" version="4.3.0" targetFramework="net472" />
  <package id="System.Security.Cryptography.X509Certificates" version="4.3.0" targetFramework="net472" />
  <package id="Tea" version="1.1.3" targetFramework="net472" />
</packages>
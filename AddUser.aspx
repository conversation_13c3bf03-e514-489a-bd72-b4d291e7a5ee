﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="AddUser.aspx.cs" Inherits="AddUser" ValidateRequest="false" %>

<script type="text/javascript">
    function onclickbutton(id) {
        //     
        var target = document.getElementById(id);
        if (target.style.display == "none")
            target.style.display = "";
        else
            target.style.display = "none";
        //div111.visible=!div111.visible;

    }
</script>
<html xmlns="http://www.w3.org/1999/xhtml">
<head id="Head1" runat="server">
    <title>创建单个用户</title>
    <meta name="viewport" content="width=device-width,height=device-height,initial-scale=0.65">
    <style type="text/css">
        body, table, tr, td {
            font-size: 12px;
            font-family: 宋体;
        }
        #addUserTable{
            width:80%;
        }
        @media (max-width: 768px) {
        body, table, tr, td {
            font-size: 25px;
            font-family: 宋体;
            max-width: 100%;
            overflow-x: auto;
            margin-top:1%;
            /*            width:350px;
*/ 
            position: relative;
            margin-left:-5%;
        }
        #addUserTable{
            width:100%;
        }

        }
    </style>
</head>
<body>
    <form id="form1" method="post" runat="server">
        <table id="addUserTable" border="1" style="background: #e6e8fa; position: absolute; left: 10%; background-position: center; border-color: #666666">
            <tr align="center">
                <td align="center">
                    <asp:LinkButton runat="server" ID="hklBak1" ForeColor="blue" Text="返回首页" OnClick="btnReturn_Click" />
                    <br />
                    <br />
                    <div id="div1" runat="server">
                    </div>
                </td>
            </tr>
            <tr>
                <td align="center" style="border: 0px">
                    <br />
                    <asp:Button ID="btnAdd" Width="80px" runat="server" Text="添加" OnClick="btnAdd_Click" /><br />
                    <br />
                    <br />
                    <asp:LinkButton runat="server" ID="hklBak2" ForeColor="blue" Text="返回首页" OnClick="btnReturn_Click" />
                </td>
            </tr>
        </table>
    </form>
</body>
</html>

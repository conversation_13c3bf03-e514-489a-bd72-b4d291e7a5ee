﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="ChooseClient.aspx.cs" Inherits="ChooseClient" ValidateRequest="false" %>

<%@ Register Assembly="WebControlLibrary1" Namespace="WebControlLibrary1" TagPrefix="cc1" %>
<html xmlns="http://www.w3.org/1999/xhtml">
<%--"全选"复选框选择的处理函数--%>

<script language="javascript" type="text/javascript">
    function selectAll(obj) {
        var theTable = obj.parentElement.parentElement.parentElement;
        var i;
        var j = obj.parentElement.cellIndex;

        for (i = 0; i < theTable.rows.length; i++) {
            var objCheckBox = theTable.rows[i].cells[j].firstChild;
            if (objCheckBox.checked != null) objCheckBox.checked = obj.checked;
        }
    }
</script>

<head id="Head1" runat="server">
    <title>选择用户</title>
    <style type="text/css">
        body, table, tr, td {
            font-size: 12px;
            font-family: 宋体;
        }
    </style>
</head>
<body>
    <form id="form1" method="post" runat="server">
        <div id="div1" runat="server">
            <table style="width: 60%; border: 1; position: absolute; left: 20%">
                <tr>
                    <td>
                        <asp:PlaceHolder ID="placeHolder1" runat="server" />
                    </td>
                </tr>
                <tr>
                    <td>
                        <br />
                        <asp:Button ID="btnSearch" runat="server" Text="开始查找用户" OnClick="btnSearch_Click" />&nbsp;&nbsp;
                        &nbsp; &nbsp;&nbsp;<asp:HyperLink ID="hlkReturn" NavigateUrl="~/Default.aspx" Text="返回首页"
                            runat="server" />
                        <hr />
                    </td>
                </tr>
                <tr>
                    <td style="height: auto">
                        <br />
                        &nbsp;<asp:Button ID="btnChose" Width="150" runat="server" Text="批量选定并返回" OnClick="btnChoose_Click" />
                    </td>
                </tr>
                <tr>
                    <td>
                        <br />
                        <cc1:WebCustomControl1 ID="WebCustomControl1_1" OnChangePageClick="pager1_Click"
                            runat="server"></cc1:WebCustomControl1>
                    </td>
                </tr>
                <tr>
                    <td valign="top" style="height: auto">
                        <asp:PlaceHolder ID="placeHolder2" runat="server" />
                    </td>
                </tr>
                <tr>
                    <td>
                        <br />
                        <cc1:WebCustomControl1 ID="WebCustomControl1_2" OnChangePageClick="pager2_Click"
                            runat="server"></cc1:WebCustomControl1>
                    </td>
                </tr>
            </table>
        </div>
    </form>
</body>
</html>

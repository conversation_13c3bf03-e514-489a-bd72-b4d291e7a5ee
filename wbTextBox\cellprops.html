﻿<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>单元格属性</title>
<link rel="stylesheet" href="pop.css" type="text/css">
<script>
function IsDigit()
{
  return ((event.keyCode >= 48) && (event.keyCode <= 57));
}
function selColor(obj, oImg)
{
	var arr = showModalDialog("selcolor.html", "", "dialogWidth:18.5em; dialogHeight:15.5em; status:0; help:0");
	if (arr != null && arr != "")
	{
		obj.value=arr;
		oImg.style.backgroundColor=arr;
	}
}

function colorchange(obj,oSpan)
{
	if (obj.value != "")
	{
		try {
		oSpan.style.backgroundColor = obj.value;
		}
		catch(e)
		{
			alert("你输入的不是颜色！");
			obj.value = "";
			oSpan.style.backgroundColor = "";
		}
	}
}

function checkchange()
{
if  (document.all.widthspecified.checked==true){
    document.all.tablewidth.disabled=false;
	}
else{	
   document.all.tablewidth.disabled=true;
   }
}	 


</script>
<script language="jscript.encode">
function setattributes(){
	var x=window.dialogArguments;
	x.tablewidthspecified="yes";
	if(window.document.moondownerform.widthspecified.checked==false){
		x.tablewidthspecified="no";
	}
	x.tablewidth="";
	if(isNaN(parseInt(window.document.moondownerform.tablewidth.value))==false){x.tablewidth=parseInt(window.document.moondownerform.tablewidth.value);}
	var tempx="";
	if(window.document.moondownerform.widthtype[0].checked==true){tempx="pixels";}
	if(window.document.moondownerform.widthtype[1].checked==true){tempx="percentage";}
	x.tablewidthtype=tempx;
	var alignindex=window.document.moondownerform.horizontalalign.selectedIndex;
	x.tablealign=window.document.moondownerform.horizontalalign.options[alignindex].value;
	var valignindex=window.document.moondownerform.verticalalign.selectedIndex;
	x.tablevalign=window.document.moondownerform.verticalalign.options[valignindex].value;
	var tablestyleindex=window.document.moondownerform.cellstyle.selectedIndex;
	x.tablecellclass="";
	if(tablestyleindex!=0){x.tablecellclass=window.document.moondownerform.cellstyle.options[tablestyleindex].value;}
	x.tablebordercolor=document.all.bordercolor.value;
	x.tablebackgroundcolor=document.all.bgcolor.value;;
	x.tableiscancel="no";
	x.tableisinsert="yes";
	x.table_status=0;
	x.tablemoondowneropen=0;window.close();
}

function bye(){
	var x=window.dialogArguments;
	x.table_status=0;
	x.tableiscancel="yes";
	x.tableisinsert="no";
	window.close();
}

function clearme(){
	var x=window.dialogArguments;
	x.table_status=0;
}

function initmoondowner(){
	var x=window.dialogArguments;
	if(x.tablewidthspecified=="yes"){
		window.document.moondownerform.widthspecified.checked=true;
	}else{
		window.document.moondownerform.widthspecified.checked=false;
	}
	var s1=""+x.tablewidth;
	if(s1.indexOf("%")!=-1){
		window.document.moondownerform.widthtype[1].checked=true;
	}else{
		window.document.moondownerform.widthtype[0].checked=true;
	}
	if(x.tablewidth!=""&&isNaN(parseInt(x.tablewidth))!=true){
		window.document.moondownerform.tablewidth.value=parseInt(x.tablewidth);
	}else{
		window.document.moondownerform.tablewidth.value="";
	}
	var s2=""+x.tablealign;s2=s2.toUpperCase();
	if(s2=="LEFT"){
		window.document.moondownerform.horizontalalign.options[1].selected=true;
	}else if(s2=="CENTER"){
		window.document.moondownerform.horizontalalign.options[2].selected=true;
	}else if(s2=="RIGHT"){
		window.document.moondownerform.horizontalalign.options[3].selected=true;
	}else if(s2=="JUSTIFY"){
		window.document.moondownerform.horizontalalign.options[4].selected=true;
	}else{
		window.document.moondownerform.horizontalalign.options[0].selected=true;
	}
	var s3=""+x.tablevalign;s3=s3.toUpperCase();
	if(s3=="TOP"){
		window.document.moondownerform.verticalalign.options[1].selected=true;
	}else if(s3=="MIDDLE"){
		window.document.moondownerform.verticalalign.options[2].selected=true;
	}else if(s3=="BASELINE"){
		window.document.moondownerform.verticalalign.options[3].selected=true;
	}else if(s3=="BOTTOM"){
		window.document.moondownerform.verticalalign.options[4].selected=true;
	}else{
		window.document.moondownerform.verticalalign.options[0].selected=true;
	}
	if(x.tablebordercolor!=""&&x.tablebordercolor!=null){
		window.document.all.item("bordercolor").value=x.tablebordercolor;
		document.all.i_bordercolor.style.backgroundColor = x.tablebordercolor;
	}
	if(x.tablebackgroundcolor!=""&&x.tablebackgroundcolor!=null){
		window.document.all.item("bgcolor").value=x.tablebackgroundcolor;
		document.all.i_bgcolor.style.backgroundColor = x.tablebackgroundcolor;
	}
	
}

</script>
</head>

<body onLoad="initmoondowner();" onunload="clearme();" bgcolor=menu topmargin="5" leftmargin="5">
<form method="POST" name="moondownerform">
<table border="0" cellspacing="5" cellpadding="0" width="100%" align="center">
<tr>
 <td>
      <fieldset><legend>布局</legend>
      <table border="0" width="100%" cellspacing="0" cellpadding="3">
                <tr> 
                  <td>
                    水平对齐：
                    <select size="1" name="horizontalalign">
                      <option value="Default">默认&nbsp;</option>
                      <option value="Left">居左</option>
                      <option value="Center">居中</option>
                      <option value="Right">居右</option>
                    </select>
                  </td>
                  <td align=right> 
                    垂直对齐：
                    <select size="1" name="verticalalign">
                      <option value="Default">默认</option>
                      <option value="Top">顶端</option>
                      <option value="Middle">居中</option>
                      <option value="Baseline">基线</option>
                      <option value="Bottom">底部</option>
                    </select>
                  </td>
                </tr>
              </table></fieldset>
              
	</td></tr>
	<tr><td>
	
	<fieldset><legend>表格宽度</legend>
 	<table border=0 width="100%" cellpadding="3" cellspacing="0">
                 <tr><td>
                     <input type="checkbox" name="widthspecified" id="widthspecified" onClick="checkchange();" class="cb" value="ON" checked>
                     <label for="widthspecified">指定单元格宽度</label>
                 </td>
                  <td align="right"> 
                    <input type="text" name="tablewidth" size="4" value="100" ONKEYPRESS="event.returnValue=IsDigit();">
                    <input type="radio" value="pixels" class="cb" name="widthtype" id="widthtype1">
                    <label for="widthtype1">像素</label>
                    <input type="radio" value="percentage" class="cb" checked name="widthtype" id="widthtype2">
                    <label for="widthtype2">百分比</label>
                    </td>
                    </tr>
                  </table>
	</fieldset>
	
	<fieldset><legend>颜色</legend>
 
              
        <table border="0" width="100%">
          <tr> 
                  
            <td valign="middle"> 
              <table width="100%" border="0" cellspacing="0" cellpadding="0">
                <tr>
                  <td>边框颜色：<input name='bordercolor' type='text' size="7" maxlength="20"  onchange="colorchange(this,i_bordercolor);"><img id="i_bordercolor" src="images/pickcolor.gif" align=absmiddle  onclick="selColor(bordercolor,this);">
                  </td>
                  <td align=right>背景颜色：<input name='bgcolor' type='text' size="7" maxlength="20"  onchange="colorchange(this,i_bgcolor);"><img id="i_bgcolor" src="images/pickcolor.gif" align=absmiddle  onclick="selColor(bgcolor,this);">
                  </td>
                </tr>
              </table></td>
                </tr>
        </table>
	  </fieldset>
            </td>
          </tr>
          <tr style="display:none"> 
            <td><fieldset><legend>样式：</legend>单元格样式：
              <select size="1" name="cellstyle">
                <option value="Default">默认</option>
              </select>
			  </fieldset>
            </td>
</tr>
<tr>
<td align="right">
      <button name="btn_insert" id="btn_insert" onclick="setattributes();">  确定  </button>
       &nbsp;
       <button name="btn_cancel" onclick="bye();">  取消  </button>
    </td>
</tr>
</table>
</form>
</body>
</html>
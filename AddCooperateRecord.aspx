﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="AddCooperateRecord.aspx.cs" Inherits="AddRecord" %>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html xmlns="http://www.w3.org/1999/xhtml" >
<head runat="server">
    <title>添加跟踪记录</title>
</head>
<body>
    <form id="form1" runat="server">
    <div>
        <div style="width: 394px; height: 167px">
            <asp:Label ID="Label1" runat="server" Text="请输入跟踪的内容"></asp:Label><br />
            <asp:TextBox ID="TextBox1" runat="server" Height="90px" TextMode="MultiLine" Width="282px"></asp:TextBox><br />
            <asp:Button ID="Button1" runat="server" OnClick="Button1_Click" Text="添加" />
            &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
            &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
            &nbsp; &nbsp; &nbsp;<asp:LinkButton ID="LinkButton1" runat="server" PostBackUrl="~/Default.aspx">返回</asp:LinkButton></div>
    
    </div>
    </form>
</body>
</html>

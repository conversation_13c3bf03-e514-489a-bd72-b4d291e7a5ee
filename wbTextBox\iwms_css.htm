﻿<html>
<head>
<title>选择样式表</title>
<meta content="text/html; charset=utf-8" http-equiv="content-type">
<link rel="stylesheet" type="text/css" href="pop.css">

<script type="text/javascript">

var wbtbWin;
if (window.opener){
	wbtbWin=launchParameters["wbtb"];
}else{
	wbtbWin=dialogArguments;
}


window.dialogWidth = "250px";
window.dialogHeight = "120px";

// 本窗口返回值
function ReturnValue(){
/*	
	var tmp;
	var str = "<div class=function>&lt;%=GetLinks(";
	tmp = document.theForm.sort.value;
	if (tmp==""){
		str += "0,";
	}else{
		str += tmp +",";
	}
	tmp = document.theForm.cols.value;
	if (tmp==""){
		str += "1,";
	}else{
		str += tmp +",";
	}
	str += document.theForm.imgLink.checked;
	str += ")%&gt;</div>";
	
	wbtbWin.WBTB_InsertHtml(str);
*/
	wbtbWin.WBTB_iwmsCss(document.theForm.css.value);
//	window.returnValue = null;
	window.close();
}

</script>
</head>

<body>
<form name="theForm">
  <table border="0" cellpadding="3" align="center">
   <tr valign="baseline"> 
      <td align="right" nowrap>风格样式表
      	 <select name="css">
	</select>
     </td>
    </tr>
    <tr valign="baseline"> 
      <td align="center" nowrap>
 		<button id="Ok" onclick="ReturnValue();">  确定  </button>&nbsp;
		<button onclick="window.close();">  取消  </button>
      </td>
    </tr>
    <tr><td colspan="2" style="color:red">
    	注：这里修改的只是制作模板时的风格。
	</td></tr>
</table>
</form>

<script type="text/javascript">
function bindCss(arr){
	var obj = document.theForm.css;
	for(var i=0; i<arr.length; i++){
		var oOption = document.createElement("OPTION");
		obj.options.add(oOption);
		oOption.innerText = arr[i][1];
		oOption.value = arr[i][0]+"style.css";
	}
}
</script>
<script type="text/javascript" src="../wbtb_data.aspx?style=1"></script>

</body>
</html>

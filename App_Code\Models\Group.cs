﻿using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

/// <summary>
/// Group 的摘要说明
/// </summary>
public class Group
{
    public int groupId { get; set; }
    public string groupName { get; set; }
    public Group()
    {
        //
        // TODO: 在此处添加构造函数逻辑
        //
    }

    public override string ToString()
    {
        return groupName;
    }
}
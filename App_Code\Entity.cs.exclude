﻿using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

/// <summary>
/// 数据类型
/// </summary>
public enum EnumFieldType
{
    charType = 11,
    longcharType = 12,
    numberType = 13,
    dateType = 14,
    boolType = 15,
    picType = 16,
    enumType = 17
}

/// <summary>
/// 排序类型
/// </summary>
public enum EnumSortType
{
    NoSort = 0,
    ASC = 1,
    DESC = 2
}

/// <summary>
/// 
/// </summary>
public struct EnumField
{
    public int enumItem;
    public string itemDetail;

    public EnumField(int eItem, string iDetail)
    {
        enumItem = eItem;
        itemDetail = iDetail;
    }
}


/// <summary>
///字段
/// </summary>

public struct Field
{
    public int position;
    public string fieldName;
    public string fieldShowName;
    public int fieldTableShowLength;
    public EnumFieldType fieldType;
    public EnumSortType sortType;
    public int enumTag;
    public string linkPage;
    public int sortOrder;
    public string attentionMessage;

    public Field(int pos, string fldName, string fldShowName, int fldTableShowLength, EnumFieldType fldType, EnumSortType sType, int eTag, string lkPage, int sOrder, string attMessage)
    {
        position = pos;
        fieldName = fldName;
        fieldShowName = fldShowName;
        fieldTableShowLength = fldTableShowLength;
        fieldType = fldType;
        sortType = sType;
        enumTag = eTag;
        linkPage = lkPage;
        sortOrder = sOrder;
        attentionMessage = attMessage;
    }
}


/// <summary>
///表之间的连接
/// </summary>
public struct Join
{
    public string joinField;
    public string joinRTable;
    public string joinRField;

    public Join(string jonField, string jonRTable, string jonRField)
    {
        joinField = jonField;
        joinRTable = jonRTable;
        joinRField = jonRField;
    }
}


/// <summary>
/// 表
/// </summary>
public struct Table
{
    public string tableName;
    public Join join;
    public Field[] fields;

    public Table(string tblName, Join jon, Field[] flds)
    {
        tableName = tblName;
        join = jon;
        fields = flds;
    }
}



/// <summary>
/// 主要的输入参数
/// </summary>
public class InputParams
{

    #region  定义多表查找，批量删除操作的连接参数
    public const string DataBaseServer = "localhost";
    public const string userName = "limainfo";
    public const string password = "yimasoft";
    public const string dbName = "5city";

    //public const string DataBaseServer = "localhost";
    //public const string userName = "sa";
    //public const string password = "sa";
    //public const string dbName = "TMS";

    #endregion

    #region 显示样式
    public const int pageSize = 10;
    public const int itemSize = 10;
    public const string lblHeadColor = "#42426f";
    public const string lblFieldShowNameColor = "#871f78";
    public const string rbYNColor = "#97694f";

    public const string tbxOnmouseoverColor = " #EFFFFF";
    public const string tbxOnmouseoutColor = "#FFFFFF";
    public const string resultTableColor = "#adeaea";


    public const string rowColor1 = "#E0FFFF";
    public const string rowColor2 = "#E8EBFC";
    public const string rowColorLongChar = "#FFFFFF";


    public const int tbxCharLength = 250;
    public const int tbxLongCharLength = 500;
    public const int tbxPicLength = 160;
    public const int tbxNumberLength = 100;
    public const int tbxDateLength = 110;
    public const int ddlWidth = 140;
    public const int ddlHeight = 60;

    public const int ddlTimeWidth = 60;
    public const int ckbLength = 30;

    public const int tbxHeight = 22;
    public const int tbxLongCharHeight = 40;

    public const int btnLength = 100;
    public const int picTableHeight = 80;
    public const int longCharShowHeight = 20;

    public const int lkbSearch = 7;
    public const int lkbModify = 7;
    public const int lkbReport = 7;
    public const int lkbExamine = 7;
    public const int lkbDelete = 7;
    public const int lkbClose = 7;
    public const int lkbSelect = 100;

    #endregion
}

﻿using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Data.SqlClient;

public partial class Modify : System.Web.UI.Page
{
    string[] strUpdates;
    int modifyCount;
    int[] ckbsModify;
    int id;
	int tbxLongCharLength = 550;
    int tbxLongCharHeight = 200;
    string[] Name;   //用来存取修改用户的执行者进行批处理选择时的返回的SESSION数组的姓名
    string[] NameID;//用来存取修改用户的执行者进行批处理选择时的返回的SESSION数组的ID
    public const int AddCount = 1;
    string[] strInserts;
    public const string AETable = "FollowClient";
    readonly string ConnStr = InputParams.connectionStr;
    const string AEKey = "id";

    //iHM为0时，不生成时分控件，为1时，生成时分控件
    //int iHM = 0; 
    int iHM = 0;

    public EnumField[][] EnumField()//1.号码待确认。2.号码确认，无联系人。3.已有联系人。4.已发资料 
                                    //5.已拜访过。6.已发资料。7.已看过供应商。8.试吃过。9.正在试用中。
                                    //11.正式合作中。12.流失的老客户。0.非目标客户。
    {
        EnumField[] enum0 = { new EnumField(4, "待联系"), new EnumField(0, "跟进中"), new EnumField(1, "已确认"), new EnumField(2, "已合作"), new EnumField(3, "未合作") };
        EnumField[] enum1 = { new EnumField(1, "★"), new EnumField(2, "★★"), new EnumField(3, " ★★★"), new EnumField(4, "★★★★"), new EnumField(5, "★★★★★") };
        EnumField[] enum2 = { new EnumField(0, "盒饭"), new EnumField(1, "桶饭"), new EnumField(2, "食堂"), new EnumField(3, "个人解决") };


     
        EnumField[][] efTs ={ enum0,enum1,enum2};
        return efTs;
    }

    public Field[] InitFields()
    {
        Field fld0 = new Field(0, "firm_name", "公司名称", 0, EnumFieldType.charType, 0, 0, "", 0, "");
        Field fld2 = new Field(2, "telephone", "电话", 0, EnumFieldType.charType, 0, 0, "", 0, "");
        Field fld1 = new Field(1, "linkman", "联系人", 0, EnumFieldType.charType, 0, 0, "", 0, "");
        Field fld3 = new Field(3, "email", "Email", 0, EnumFieldType.charType, 0, 0, "", 0, "");
        //Field fld4 = new Field(4, "is_unify_reservation", "订餐类型", 0, EnumFieldType.enumType, 0, 2, "", 0, "");
        //Field fld5 = new Field(5, "is_telephone_client", "跟单客户", 0, EnumFieldType.boolType, 0, 0, "", 0, "");
        //Field fld6 = new Field(6, "is_azw_client", "爱知味客户", 0, EnumFieldType.boolType, 0, 0, "", 0, "");
        //Field fld7 = new Field(7, "firm_number", "公司人数", 0, EnumFieldType.numberType, 0, 0, "", 0, "");
        //Field fld8 = new Field(8, "dinner_level", "用餐标准", 0, EnumFieldType.doubleType, 0, 0, "", 0, "");
        //Field fld9 = new Field(9, "interviewee_sex", "受访者性别", 0, EnumFieldType.boolType, 0, 0, "", 0, "");
        Field fld4 = new Field(10, "area", "城市", 0, EnumFieldType.enumType, 0, 0, "", 0, "");
        Field fld5 = new Field(11, "client_state", "客户状态", 0, EnumFieldType.enumType, 0, 0, "", 0, "");
        //Field fld12 = new Field(12, "presented", "赠送的投资", 0, EnumFieldType.doubleType, 0, 0, "", 0, "");
        Field fld6 = new Field(13, "Type", "客户意向指数", 0, EnumFieldType.enumType, 0, 1, "", 0, "");
        Field fld7 = new Field(14, "next_follow_time", "预计下次回访时间", 0, EnumFieldType.dateType, 0, 0, "", 0, "");
        Field fld8 = new Field(15, "investigate", "备注", 0, EnumFieldType.longcharType, 0, 0, "", 0, "");
        Field fld9 = new Field(16, "executor_id", "执行者", 0, EnumFieldType.enumType, 0, 0, "", 0, "");
        Field fld10 = new Field(17, "userAddress", "地址", 0, EnumFieldType.charType, 0, 0, "", 0, "");
        //Field fld18 = new Field(18, "cooperation_name", "供应商", 0, EnumFieldType.charType, 0, 0, "Copy of SeeCooperate.aspx", 0, "");
        Field[] flds = { fld0, fld1, fld2, fld3, fld4, fld5, fld6, fld7, fld8, fld9, fld10 };
        return flds;
    }

    /// <summary>
    /// 页面加载
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void Page_Load(object sender, EventArgs e)
    {
		HttpCookie cookie = Request.Cookies["user"];
		if (cookie != null)//不用重新登录
			{
				Session["user_id"] = cookie.Values["user_id"].ToString();//用户的ID
				Session["isGroupManager"] = cookie.Values["isGroupManager"].ToString();
		}
		else
		{
			Response.Redirect("Login.aspx");
		}
		
        //Response.Write(Request.Params["CooperationName"]);
        Session["supPage"] = "ModifyTasks.aspx";
        ckbsModify = (int[])Session["ckbsModify"];
        modifyCount = ckbsModify?.Length ?? 0;
        //Response.Write(ckbsModify.Length);
      //  Label1.ForeColor = System.Drawing.ColorTranslator.FromHtml(InputParams.lblFieldShowNameColor);

        //Response.Write(Session["myId"].ToString());
        if (modifyCount > 0)
        {

            //Response.Write(Request.QueryString["id"].ToString());
                if (Request.QueryString["id"] != null)
                {
                   
                    try
                    {

                        id = int.Parse(Request.QueryString["id"]);
                        GeneModifyMould();
                       
                      
                          
                    }
                    catch
                    {
                    }
                }
                else
                {
                int result;
                bool b = int.TryParse(Request.QueryString["recordId"],out result);
                if (b) 
                    {
                    ckbsModify[0] = result;
                    }
                    GeneModifyMould();
                   // Response.Write(Request.QueryString["CooperationName"]);
                    //if (Request.QueryString["CooperationName"] == null)
                    //{
                    //       // Response.Write(Session["myId"]);
                    //        //SqlConnection MyConn = new SqlConnection(ConnStr);
                    //        //SqlDataReader dr = null;
                    //        //SqlCommand MyComm = null;

                    //        //MyConn.Open();
                    //        //String strSQL = "select cooperation_name from FollowClient where id=" + Session["myId"].ToString();
                    //        //MyComm = new SqlCommand(strSQL, MyConn);
                    //        //dr = MyComm.ExecuteReader();
                    //        //if (dr.Read())
                    //        //{

                    //        //   TextBox1.Text = dr[0].ToString();
                    //        //    //Response.Write(dr[0].ToString());
                    //        //   // Response.Write("aaaa");
                    //        //}
                    //    }
                    //    else
                    //    {
                    //        //Response.Write(Request.Params["CooperationName"]);
                    //        TextBox1.Text = Request.QueryString["CooperationName"].ToString();
                    //    }
                }
  
        }
        else
        {
            ClientScript.RegisterClientScriptBlock(this.GetType(), "info", "<script language=\"javascript\" type=\"text/javascript\">alert(\"请先选择要修改的任务！\")</script>");

        }
    }


    /// <summary>
    /// 修改事件处理
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void btnModify_Click(object sender, EventArgs e)
    {
        GetUpdateStr();
        string user_id = "";
        if (Request.Cookies["user"] != null)
        {
            HttpCookie cookie = Request.Cookies["user"];
            user_id = cookie.Values["user_id"].ToString();
        }
        try
        {
            Update();
            //Response.Redirect("Modify.aspx");

        }
        catch (SqlException ex)
        {
            Response.Write(ex.ToString());
        }
        Response.Redirect("Default.aspx");
    }

    /// <summary>
    /// 返回首页
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void hlkReturn_Click(object sender, EventArgs e)
    {
        Response.Redirect("Default.aspx?id=2");
    }


    protected void lbt_Command(object sender, CommandEventArgs e)
    {
       
        string linkPage = e.CommandArgument.ToString();
        string strDic = linkPage;
        Response.Redirect(strDic);
    }


    /// <summary>
    /// 生成修改模板
    /// </summary>
    /// <param name="placeHolder"></param>
    /// <param name="fields"></param>
    public void GeneModifyMould()
    {
        div1.Controls.Clear();
        Field[] fields = InitFields();
        EnumField[][] efTs = EnumField();
        int arrLen = ckbsModify.Length;
        strUpdates = new string[arrLen];
        int iGroupManager = int.Parse(Session["isGroupManager"].ToString());

        string strStart = "SELECT ";
        for (int fieldIndex = 0; fieldIndex < fields.Length; fieldIndex++)
        {
            strStart += fields[fieldIndex].fieldName;
            if (fieldIndex != fields.Length - 1)
            {
                strStart += ",";
            }
        }
        strStart += " FROM " + AETable + " WHERE " + AEKey + "=";
        SqlConnection conn = new SqlConnection(ConnStr);
        if (conn.State.ToString() == "Closed")
        {
            conn.Open();
        }


        for (int modifyIndex = 0; modifyIndex < ckbsModify.Length; modifyIndex++)
        {
            string str = strStart + ckbsModify[modifyIndex];
			//if (iGroupManager != 3)
			//{
			//		str += " and executor_id=" + Session["user_id"].ToString();
			//}  
            SqlDataAdapter da = new SqlDataAdapter(str, conn);
            DataSet ds = new DataSet();
            da.Fill(ds);
            DataTable dt = ds.Tables[0];
            DataRow dr = dt.Rows[0];

            PlaceHolder placeHolder = new PlaceHolder();
            placeHolder.ID = "placeHolder" + modifyIndex;
            div1.Controls.Add(placeHolder);
            PlaceHolder pHolder = (PlaceHolder)div1.FindControl("placeHolder" + modifyIndex);
            if (modifyIndex != 0)
            {
                Literal ltlHR = new Literal();
                ltlHR.Text = "<HR/>";
                placeHolder.Controls.Add(ltlHR);
            }

            if (!new Common().isMobile())
            {
                Literal ltlTag = new Literal();
                ltlTag.Text = AEKey + "：" + ckbsModify[modifyIndex];
            //Response.Write(ckbsModify[modifyIndex]);
           // Session["MyId"] = ckbsModify[modifyIndex].ToString();//当前客户ID【跟踪表】
            placeHolder.Controls.Add(ltlTag);
            }

            if (new Common().isMobile())
            {
                Literal literal0 = new Literal();
                literal0.Text = "<div class=\"navbar\">" +
                                    "客户系统 → <span> 客户信息编辑 </span>" +
                                    "</div >" +
                                "<div class=\"container\">" +
                                    "<div class=\"card\">";
                placeHolder.Controls.Add(literal0);
            }
            for (int fieldIndex = 0; fieldIndex < fields.Length; fieldIndex++)
            {
                Literal lbl1 = new Literal();
                lbl1.Text = "<br/>";
                pHolder.Controls.Add(lbl1);

                if (fields[fieldIndex].fieldName == "Description")
                {
                    Literal ltlD1 = new Literal();
                    ltlD1.Text = "<br/>";
                    placeHolder.Controls.Add(ltlD1);
                }

                
                if (new Common().isMobile())
                {
                    Literal lblTag = new Literal();
                    lblTag.Text = "<div class=\"form-group\"><label> " + fields[fieldIndex].fieldShowName + "：" + " </br></label><span>";//否则加";"
                    pHolder.Controls.Add(lblTag);
                }
                else {
                    Label label0 = new Label();
                    label0.Text = fields[fieldIndex].fieldShowName + "：" + " ";//否则加";"
                    pHolder.Controls.Add(label0);
                    label0.ForeColor = System.Drawing.ColorTranslator.FromHtml(InputParams.lblFieldShowNameColor);
				}

                if (fields[fieldIndex].fieldName == "Description")
                {
                    Literal ltlD2 = new Literal();
                    ltlD2.Text = "<br/>";
                    placeHolder.Controls.Add(ltlD2);
                }

                string strFldName = fields[fieldIndex].fieldName + modifyIndex;
                switch (fields[fieldIndex].fieldType)
                {
                    case EnumFieldType.charType:
                        #region charType
                        TextBox tbx1 = new TextBox();
                        tbx1.ID = "tbx" + strFldName;
                        tbx1.CssClass = "textbox1-style";
                        string str1 = "tbx" + strFldName;

                        if (fields[fieldIndex].fieldName != "cooperation_name")
                        {
                            if (id == 1)
                            {
                                tbx1.Text = Session[str1].ToString();
                            }
                            else {
                                tbx1.Text = dr[fieldIndex].ToString();
                            };
                            if (!new Common().isMobile())
                            {
                                tbx1.Width = 250;
                            }
                            pHolder.Controls.Add(tbx1);
                            if (new Common().isMobile())
                            {
                                Literal literal1 = new Literal();
                                literal1.Text = "</span></div>";
                                placeHolder.Controls.Add(literal1);
                            }
                        }
                        if (fields[fieldIndex].fieldName == "cooperation_name")
                        {
                            if (id == 1)
                            {
                                tbx1.Text = Session[str1].ToString();
                            }

                            else if ((fields[fieldIndex].fieldName == "cooperation_name") && (Request.QueryString["CooperationName"] != null))
                            {
                                tbx1.Text = Request.QueryString["CooperationName"].ToString();

                            }
                            else
                            {
                                tbx1.Text = dr[fieldIndex].ToString();
                            }
                            tbx1.Width = 250;

                            pHolder.Controls.Add(tbx1);

                            Literal ltlD2 = new Literal();
                            ltlD2.Text = "<br/>";
                            placeHolder.Controls.Add(ltlD2);

                            LinkButton lbt = new LinkButton();
                            lbt.Text = "  点此查找供应商";
                            lbt.ID = "lbt" + strFldName;
                            lbt.Command += new CommandEventHandler(lbt_Command);
                            lbt.Command += new CommandEventHandler(lbt_Command);
                            lbt.CommandName = "tbx" + fields[fieldIndex].fieldName + "&" + fieldIndex;
                            lbt.CommandArgument = fields[fieldIndex].linkPage.Trim().ToString();
                            pHolder.Controls.Add(lbt);
                        }
                        
                        continue;
                        #endregion charType

                    case EnumFieldType.longcharType:
                        #region longcharType
                        TextBox tbx2 = new TextBox();
                        tbx2.ID = "tbx" + strFldName;
                        string str2 = "tbx" + strFldName;
                        if (id == 1)
                        {
                            tbx2.Text = Session[str2].ToString().Replace("<br/>", "");
                        }
                        else
                        {
                            tbx2.Text = dr[fieldIndex].ToString().Replace("<br/>", "");
                        }
                        if (!new Common().isMobile()) {
                            tbx2.Width = tbxLongCharLength;
                        }
                        //tbx2.Width = tbxLongCharLength;
                        tbx2.Height = tbxLongCharHeight;
                        tbx2.TextMode = TextBoxMode.MultiLine;
                        tbx2.CssClass = "textbox2-style";
                            pHolder.Controls.Add(tbx2);
                        if (new Common().isMobile())
                        {
                            Literal literal2 = new Literal();
                            literal2.Text = "</span></div>";
                            placeHolder.Controls.Add(literal2);
                        }
                        ((TextBox)pHolder.FindControl("tbx" + strFldName)).Attributes.Add("onMouseOver", "this.style.backgroundColor='" + InputParams.tbxOnmouseoverColor + "';");
                        ((TextBox)pHolder.FindControl("tbx" + strFldName)).Attributes.Add("onMouseOut", "this.style.backgroundColor='" + InputParams.tbxOnmouseoutColor + "';");
                        continue;
                        #endregion longcharType

                    case EnumFieldType.nameType:
                        #region nameType
                        TextBox tbx7 = new TextBox();
                        LinkButton bt7 = new LinkButton();
                        tbx7.ID = "tbx" + strFldName;
                        string str7 = "tbx" + strFldName;
                        bt7.Text = "点击此查找";
                        tbx7.Width = 140;

                        try
                        {
                            #region 批处理选择多个用户
                            if (Request.Params["id"] != null)//表示是批处理选择多个用户而来的。
                            {
                                Name = (String[])Session["ModifyTasksChooseNames"];
                                NameID = (String[])Session["ModifyTasksIDs"];

                                for (int index = 0; index < Name.Length; index++)
                                {
                                    tbx7.Text += Name[index].ToString() + " ";
                                }
                            }
                            #endregion 批处理选择多个用户
                            else
                            {
                                if (Session["ModifyTastParame"] == null)
                                    tbx7.Text = " ";
                                else
                                    tbx7.Text = Session["ModifyTastParame"].ToString();
                            }
                        }
                        catch
                        {

                        }
                        finally
                        {
                            bt7.PostBackUrl = "~/ChooseUser.aspx?tbx=1";

                        }
                        pHolder.Controls.Add(tbx7);
                        pHolder.Controls.Add(bt7);
                        continue;
                        #endregion nameType

                    case EnumFieldType.numberType:
                        #region numberType
                        TextBox tbx3 = new TextBox();
                        tbx3.ID = "tbx" + strFldName;
                        string str3 = "tbx" + strFldName;
                        if (id == 1)
                        {
                            tbx3.Text = Session[str3].ToString();
                        }
                        else
                        {
                            tbx3.Text = dr[fieldIndex].ToString();
                        }
                        tbx3.Width = InputParams.tbxNumberLength;
                        tbx3.Height = InputParams.tbxHeight;
                        pHolder.Controls.Add(tbx3);

                        ((TextBox)pHolder.FindControl("tbx" + strFldName)).Attributes.Add("onMouseOver", "this.style.backgroundColor='" + InputParams.tbxOnmouseoverColor + "';");
                        ((TextBox)pHolder.FindControl("tbx" + strFldName)).Attributes.Add("onMouseOut", "this.style.backgroundColor='" + InputParams.tbxOnmouseoutColor + "';");
                        if (fields[fieldIndex].linkPage.ToString() != "")
                        {
                            LinkButton lbt = new LinkButton();
                            lbt.Text = "  点此查找";
                            lbt.ID = "lbt" + strFldName;
                            lbt.Command += new CommandEventHandler(lbt_Command);
                            lbt.CommandName = "tbx" + strFldName;
                            lbt.CommandArgument = fields[fieldIndex].linkPage.ToString();
                            pHolder.Controls.Add(lbt);
                        }
                        continue;
                        #endregion numberType

                    case EnumFieldType.doubleType:
                        #region doubleType
                        TextBox tbx13 = new TextBox();
                        tbx13.ID = "tbx" + strFldName;
                        string str13 = "tbx" + strFldName;
                        if (id == 1)
                        {
                            tbx13.Text = Session[str13].ToString();
                        }
                        else
                        {
                            tbx13.Text = dr[fieldIndex].ToString();
                        }
                        if (!new Common().isMobile())
                        {
                            tbx13.Width = InputParams.tbxNumberLength;
                        }
                        
                        tbx13.Height = InputParams.tbxHeight;
                        tbx13.CssClass = "textbox13-style";
                        pHolder.Controls.Add(tbx13);
                        if (new Common().isMobile())
                        {
                            Literal literal3 = new Literal();
                            literal3.Text = "</span></div>";
                            placeHolder.Controls.Add(literal3);
                        }
                        ((TextBox)pHolder.FindControl("tbx" + strFldName)).Attributes.Add("onMouseOver", "this.style.backgroundColor='" + InputParams.tbxOnmouseoverColor + "';");
                        ((TextBox)pHolder.FindControl("tbx" + strFldName)).Attributes.Add("onMouseOut", "this.style.backgroundColor='" + InputParams.tbxOnmouseoutColor + "';");
                        
                        continue;
                        #endregion doubleType

                    case EnumFieldType.dateType:
                        #region dateType
                        TextBox tbx4 = new TextBox();
                        tbx4.ID = "tbx" + strFldName;
                        string str4 = "tbx" + strFldName;
                        if (!new Common().isMobile())
                        {
                            tbx4.Width = InputParams.tbxDateLength;
                            //tbx4.Height = InputParams.tbxHeight;
                        }
                        
                        tbx4.CssClass = "textbox4-style";
						tbx4.TextMode = TextBoxMode.Date;
						if (id == 1)
                        {
                            tbx4.Text = Session[str4].ToString();

                        }
                        else
                        {
                            try
                            {
                                tbx4.Text = DateTime.Parse(dr[fieldIndex].ToString()).ToShortDateString();
                            }
                            catch
                            {
                                tbx4.Text = DateTime.Now.ToShortDateString();
                            }
                        }

                        pHolder.Controls.Add(tbx4);
 
                        ((TextBox)pHolder.FindControl("tbx" + strFldName)).Attributes.Add("onClick", "javascript:calendar()");
                        ((TextBox)pHolder.FindControl("tbx" + strFldName)).Attributes.Add("onClick", "javascript:calendar()");


                        string str4H = "ddl" + strFldName + "H";
                        string str4M = "ddl" + strFldName + "M";
                        if (iHM == 1)
                        {
                            Literal ltlSpace1 = new Literal();
                            ltlSpace1.Text = "&nbsp;";
                            placeHolder.Controls.Add(ltlSpace1);

                            DropDownList ddlH = new DropDownList();
                            ddlH.ID = "ddl" + strFldName + "H";
                            ddlH.Width = InputParams.ddlTimeWidth;
                            ddlH.CssClass = "time-dropdown";
                            for (int iH = 0; iH < 24; iH++)
                            {
                                ListItem li = new ListItem();
                                li.Text = iH.ToString();
                                li.Value = iH.ToString();
                                if ((id == 1) && (Session[str4H] != null) && (int.Parse(Session[str4H].ToString()) == iH))
                                {
                                    li.Selected = true;

                                }
                                else if ((dr[fieldIndex].ToString() != "") && (DateTime.Parse(dr[fieldIndex].ToString()).Hour.ToString() == iH.ToString()))
                                {
                                    li.Selected = true;
                                }
                                ddlH.Items.Add(li);
                            }

                            placeHolder.Controls.Add(ddlH);

                            Label lblH = new Label();
                            lblH.Text = "时";
                            //lblH.CssClass = "H-text";
                            placeHolder.Controls.Add(lblH);

                            DropDownList ddlM = new DropDownList();
                            ddlM.ID = "ddl" + strFldName + "M";
                            ddlM.Width = InputParams.ddlTimeWidth;
                            ddlM.CssClass = "time-dropdown2";
                            for (int iM = 0; iM < 60; iM++)
                            {
                                ListItem li = new ListItem();
                                li.Text = iM.ToString();
                                li.Value = iM.ToString();
                                if ((id == 1) && (Session[str4M] != null) && (int.Parse(Session[str4M].ToString()) == iM))
                                {
                                    li.Selected = true;
                                }
                                else if ((dr[fieldIndex].ToString() != "") && (DateTime.Parse(dr[fieldIndex].ToString()).Minute.ToString() == iM.ToString()))
                                {
                                    li.Selected = true;
                                }

                                ddlM.Items.Add(li);
                            }

                            placeHolder.Controls.Add(ddlM);

                            Label lblM = new Label();
                            lblM.Text = "分";
                            placeHolder.Controls.Add(lblM);
                        }
                        if (new Common().isMobile())
                        {
                            Literal literal4 = new Literal();
                            literal4.Text = "</span></div>";
                            placeHolder.Controls.Add(literal4);
                        }
                        continue;
                        #endregion dateType
                    case EnumFieldType.boolType:
                        #region boolType
                        RadioButton rbY = new RadioButton();
                        RadioButton rbN = new RadioButton();
                        if (dr[fieldIndex].ToString() == "1")
                            rbY.Checked = true;
                        else
                            rbN.Checked = true;

                        rbY.ID = "rb" + strFldName + "1";
                        string strY = "rb" + strFldName + "1";
                        rbY.GroupName = "gn" + strFldName;
                        if (fields[fieldIndex].fieldName == "interviewee_sex")
                            rbY.Text = "男";
                        else
                        rbY.Text = "是";
                        rbY.ForeColor = System.Drawing.ColorTranslator.FromHtml(InputParams.rbYNColor);
                        pHolder.Controls.Add(rbY);

                        rbN.ID = "rb" + strFldName + "2";
                        string strN = "rb" + strFldName + "2";
                        rbN.GroupName = "gn" + strFldName;
                        if (fields[fieldIndex].fieldName == "interviewee_sex")
                            rbN.Text = "女";
                        else
                        rbN.Text = "否";
                        rbN.ForeColor = System.Drawing.ColorTranslator.FromHtml(InputParams.rbYNColor);
                        pHolder.Controls.Add(rbN);

                        if (fields[fieldIndex].fieldName == "is_telephone_client")
                        {
                            if (iGroupManager != 1 && iGroupManager != 2)
                            {
                                rbY.Enabled = false;
                                rbN.Enabled = false;
                            }
                        }  

                        if (id == 1)
                        {
                            if (Session[strY].ToString() != null)
                            {
                                if (Session[strY].ToString() == "1")
                                {
                                    ((RadioButton)pHolder.FindControl(strY)).Checked = true;
                                }
                                else
                                {
                                    ((RadioButton)pHolder.FindControl(strN)).Checked = true;

                                }
                            }
                        }
                        continue;
                        #endregion boolType

                    case EnumFieldType.picType:
                        #region picType
                        TextBox tbx5 = new TextBox();
                        tbx5.ID = "tbx" + strFldName;
                        string str5 = "tbx" + strFldName;
                        if (id == 1)
                        {
                            tbx5.Text = Session[str5].ToString();
                        }
                        else
                        {
                            tbx5.Text = dr[fieldIndex].ToString();
                        }
                        tbx5.Width = InputParams.tbxCharLength;
                        tbx5.Height = InputParams.tbxHeight;
                        tbx5.TextMode = TextBoxMode.MultiLine;
                        tbx5.Wrap = true;
                        tbx5.Style.Add("overflow", "hidden");
                        pHolder.Controls.Add(tbx5);
                        ((TextBox)pHolder.FindControl("tbx" + strFldName)).Attributes.Add("onMouseOver", "this.style.backgroundColor='" + InputParams.tbxOnmouseoverColor + "';");
                        ((TextBox)pHolder.FindControl("tbx" + strFldName)).Attributes.Add("onMouseOut", "this.style.backgroundColor='" + InputParams.tbxOnmouseoutColor + "';");
                        continue;
                        #endregion picType
                    case EnumFieldType.enumType:
                        #region enumType
                        DropDownList ddl = new DropDownList();
                        ddl.ID = "ddl" + strFldName;
                        string str6 = "ddl" + strFldName;
                        int enumT = fields[fieldIndex].enumTag;
                        if (!new Common().isMobile())
                        {
                            ddl.Width = InputParams.ddlWidth;
                        }
                        ddl.AutoPostBack = false;
                        ddl.CssClass = "time-dropdown3";
                        if (fields[fieldIndex].fieldName == "area")
                        {
                            SqlConnection conn1 = new SqlConnection(ConnStr);

                            ddl.ClearSelection();
                            //ddl.ID = "ddl" + strFldName;
                            SqlDataAdapter dap = new SqlDataAdapter("SELECT DISTINCT town_id,name  FROM ArTown", conn1);
                            DataTable dt1 = new DataTable();
                            dap.Fill(dt1);
                            ddl.Items.Clear();

                            ddl.DataSource = dt1;
                            ddl.DataTextField = "name";
                            ddl.DataValueField = "town_id";
                            ddl.DataBind();
                            ddl.SelectedValue = (new Common()).GetAreaID(int.Parse(Request.Params["recordId"].ToString()));

                        }
                        else if (fields[fieldIndex].fieldName == "executor_id") 
                        {
                            SqlConnection conn1 = new SqlConnection(ConnStr);

                            ddl.ClearSelection();
                            //ddl.ID = "ddl" + strFldName;
                            SqlDataAdapter dap = new SqlDataAdapter("SELECT DISTINCT nickname,user_id  FROM Users where group_id=1", conn1);
                            DataTable dt2 = new DataTable();
                            dap.Fill(dt2);
                            ddl.Items.Clear();

                            ddl.DataSource = dt2;
                            ddl.DataTextField = "nickname";
                            ddl.DataValueField = "user_id";
                            
                            ddl.DataBind();
                            ddl.SelectedValue = (new Common()).GetExecutorID(int.Parse(Request.Params["recordId"].ToString()));


                            //ListItem li2 = new ListItem();
                            //li2.Text = "请选择";
                            //li2.Value = "请选择";
                            //li2.Selected = true;
                            //ddl.Items.Add(li2);
                        }
                        else
                        {


                            for (int enumLen = 0; enumLen < efTs[enumT].Length; enumLen++)
                            {
                                ListItem li = new ListItem();
                                li.Value = efTs[enumT][enumLen].enumItem.ToString();
                                li.Text = efTs[enumT][enumLen].itemDetail;
                                if (id == 1)
                                {
                                    if ((Session[str6] != null) && (efTs[enumT][enumLen].enumItem.ToString() == Session[str6].ToString()))
                                    {
                                        li.Selected = true;
                                    }
                                }
                                else
                                {
                                    if (efTs[enumT][enumLen].enumItem.ToString() == dr[fieldIndex].ToString())
                                    {
                                        li.Selected = true;
                                    }
                                }

                                ddl.Items.Add(li);
                            }
                        }


                              pHolder.Controls.Add(ddl);
                        if (new Common().isMobile())
                        {
                            Literal literal5 = new Literal();
                            literal5.Text = "</span></div>";
                            placeHolder.Controls.Add(literal5);
                        }
                        //((DropDownList)pHolder.FindControl("ddl" + strFldName)).Attributes.Add("onMouseOver", "this.style.backgroundColor='" + InputParams.tbxOnmouseoverColor + "';");
                        //((DropDownList)pHolder.FindControl("ddl" + strFldName)).Attributes.Add("onMouseOut", "this.style.backgroundColor='" + InputParams.tbxOnmouseoutColor + "';");
                        continue;
                        #endregion enumType
                }
            }

            if (new Common().isMobile())
            {
                Literal literal5 = new Literal();
                literal5.Text = "</div></div>";
                placeHolder.Controls.Add(literal5);
            }
        }
        
        if (conn.State.ToString() == "Open")
        {
            conn.Close();
        }


    

    }


    /// <summary>
    /// 获得更新记录的SQL Update字符串
    /// </summary>
    /// <param name="placeHolder"></param>
    /// <param name="fields"></param>
    /// <returns></returns>
    public void GetUpdateStr()
    {
        #region 单个选择用户
        Field[] fields = InitFields();
        EnumField[][] efTs = EnumField();

        for (int modifyIndex = 0; modifyIndex < modifyCount; modifyIndex++)
        {
            string strUpdate = "UPDATE " + AETable + " SET ";
            PlaceHolder pHolder = (PlaceHolder)div1.FindControl("placeHolder" + modifyIndex);

            for (int fieldIndex = 0; fieldIndex < fields.Length; fieldIndex++)
            {              
                string strFldName = fields[fieldIndex].fieldName + modifyIndex;
                if (fields[fieldIndex].fieldName == "area" || fields[fieldIndex].fieldName == "executor_id")
                {
                    if (((DropDownList)pHolder.FindControl("ddl" + fields[fieldIndex].fieldName + modifyIndex)).Text != "请选择")
                    {
                        if (fieldIndex != 0)
                        {
                            strUpdate += ",";
                        }

                        strUpdate += fields[fieldIndex].fieldName + "=";
                    }
                    else
                        continue;
                }
                else
                {
                    if (fieldIndex != 0)
                    {
                        strUpdate += ",";
                    }


                    strUpdate += fields[fieldIndex].fieldName + "=";
                }
                    switch (fields[fieldIndex].fieldType)
                    {

                        case EnumFieldType.numberType:
                            string tbxName2 = "tbx" + strFldName + "2";
                            if (((TextBox)pHolder.FindControl("tbx" + strFldName)).Text != "")
                            {
                                if (Session[tbxName2] != null)
                                {
                                    strUpdate += int.Parse(Session[tbxName2].ToString());
                                }
                                else
                                {
                                    strUpdate += ((TextBox)pHolder.FindControl("tbx" + strFldName)).Text.Trim();
                                }
                            }
                            else
                            {
                                strUpdate += "' '";
                            }
                            continue;

                        case EnumFieldType.doubleType:
                            string tbxName3 = "tbx" + strFldName + "2";
                            if (((TextBox)pHolder.FindControl("tbx" + strFldName)).Text != "")
                            {
                                if (Session[tbxName3] != null)
                                {
                                    strUpdate += int.Parse(Session[tbxName3].ToString());
                                }
                                else
                                {
                                    strUpdate += ((TextBox)pHolder.FindControl("tbx" + strFldName)).Text.Trim();
                                }
                            }
                            else
                            {
                                strUpdate += "' '";
                            }
                            continue;

                    //case EnumFieldType.dateType:
                    //if (((TextBox)pHolder.FindControl("tbx" + strFldName)).Text != "" && ((TextBox)pHolder.FindControl("tbx" + strFldName)).Text != null )
                    //{
                    //    string strDataTime = ((TextBox)pHolder.FindControl("tbx" + strFldName)).Text.Trim().ToString();
                    //    if (iHM == 1)
                    //    {
                    //        DropDownList ddlH = (DropDownList)(pHolder.FindControl("ddl" + strFldName + "H"));
                    //        DropDownList ddlM = (DropDownList)(pHolder.FindControl("ddl" + strFldName + "M"));
                    //        string strH = "00";
                    //        if (ddlH.Text != "")
                    //        {
                    //            strH = ddlH.Text.Trim().ToString();
                    //        }
                    //        string strM = "00";
                    //        if (ddlM.Text != "")
                    //        {
                    //            strM = ddlM.Text.Trim().ToString();
                    //        }
                    //        strDataTime += " " + strH + ":" + strM + ":" + "00";
                    //    }
                    //        DateTime dt = DateTime.Parse(strDataTime);
                    //        strUpdate += "'" + dt.ToString() + "'";
                    //}
                    case EnumFieldType.dateType:
                        TextBox dateTextBox = (TextBox)pHolder.FindControl("tbx" + strFldName);
                        string dateText = dateTextBox?.Text?.Trim();  // 安全获取文本框值

                        if (!string.IsNullOrEmpty(dateText))
                        {
                            string strDataTime = dateText;

                            // 处理时分选择
                            if (iHM == 1)
                            {
                                string strH = "00";
                                string strM = "00";

                                // 安全获取小时控件
                                if (pHolder.FindControl("ddl" + strFldName + "H") is DropDownList ddlH && !string.IsNullOrEmpty(ddlH.Text))
                                {
                                    strH = ddlH.Text.Trim();
                                }

                                // 安全获取分钟控件
                                if (pHolder.FindControl("ddl" + strFldName + "M") is DropDownList ddlM && !string.IsNullOrEmpty(ddlM.Text))
                                {
                                    strM = ddlM.Text.Trim();
                                }

                                strDataTime += " " + strH + ":" + strM + ":00";
                            }

                            // 安全转换日期
                            if (DateTime.TryParse(strDataTime, out DateTime dt))
                            {
                                strUpdate += "'" + dt.ToString("yyyy-MM-dd HH:mm:ss") + "'";  // 统一格式
                            }
                            else
                            {
                                // 日期格式错误处理：使用默认值
                                strUpdate += "'" + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") + "'";
                            }
                        }
						else
						{
							// 空值处理：使用默认当前时间
							string defaultTime = iHM == 1
								? DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
								: DateTime.Now.Date.ToString("yyyy-MM-dd") + " 00:00:01";
							strUpdate += "'" + defaultTime + "'";
						}

						continue;


                        case EnumFieldType.nameType:
                            int iGroupManager = int.Parse(Session["isGroupManager"].ToString());
                            if (iGroupManager == 1 || iGroupManager == 2)//该用户不为管理员，不执行添加  
                            {
                                if (Request.Params["id"] != null)//若是用户名是选择多用户名而来的；则先修改一个。再添加后面的
                                {
                                    strUpdate += "'" + NameID[0].ToString() + "'";
                                }
                                else                              //为单个选定并返回
                                {
                                    if (Session["ModifyTastParame"].ToString() == "")
                                        strUpdate += "' '";
                                    else
                                        strUpdate += "'" + Session["ModifyTastID"].ToString() + "'";
                                }
                            }
                            continue;

                        case EnumFieldType.enumType:

                            string strDdl = ((DropDownList)pHolder.FindControl("ddl" + strFldName)).Text.Trim();
                            int iValue = 0;
                            string strText;

                            int enumT = fields[fieldIndex].enumTag;
                            for (int enumLen = 0; enumLen < efTs[enumT].Length; enumLen++)
                            {
                                iValue = efTs[enumT][enumLen].enumItem;
                                strText = efTs[enumT][enumLen].itemDetail;
                                if (strDdl == iValue.ToString())
                                {
                                    break;
                                }
                            }
                            if (strFldName.Contains("area") || strFldName.Contains("executor_id"))
                            {
                                strUpdate += ((DropDownList)pHolder.FindControl("ddl" + strFldName)).SelectedValue.ToString().Trim();
                            }
                            else
                            {

                                strUpdate += iValue.ToString();
                            }
                            continue;

                        case EnumFieldType.boolType:

                            if (((RadioButton)pHolder.FindControl("rb" + strFldName + "1")).Checked)
                            {
                                strUpdate += "1";
                            }
                            else
                            {
                                strUpdate += "0";
                            }
                            continue;

                        default:
                            string str = ((TextBox)pHolder.FindControl("tbx" + strFldName)).Text;
                            if (str != "")
                            {
                                str = str.Replace("\n", "<br/>");
                                strUpdate += "'" + str.Trim() + "'";
                            }
                            else
                            {
                                strUpdate += "' '";
                            }
                            continue;
                    }
                
            }
            strUpdate += " WHERE " + AEKey + "=" + ckbsModify[modifyIndex];
            strUpdates[modifyIndex] = strUpdate;
        }
        //string constr = "update FollowClient set cooperation_name='" + TextBox1.Text.ToString() + "'  where id=" + Session["myId"].ToString();
        //SqlConnection MyConn = new SqlConnection(ConnStr);
        //SqlCommand mysql = new SqlCommand(constr, MyConn);
      

        //    MyConn.Open();
        //    mysql.ExecuteNonQuery();

        //        // 关闭数据库连接
        //   MyConn.Close();
    

        #endregion 单个选择用户
    }

    /// <summary>
    /// 修改操作
    /// </summary>
    /// <param name="strInsert"></param>
    public void Update()
    {
        SqlConnection conn = new SqlConnection(ConnStr);

        if (conn.State.ToString() == "Closed")
        {
            conn.Open();
        }
        for (int modifyIndex = 0; modifyIndex < ckbsModify.Length; modifyIndex++)
        {
            SqlCommand comm = new SqlCommand(strUpdates[modifyIndex], conn);
            comm.ExecuteNonQuery();
        }

        string strSql = "";
        strSql = "insert into FollowRecord(follow_client_id,description,follow_time,follow_executor) values(" + Request.Params["recordId"] + ",'" + (new Common()).GetNickname(int.Parse(Session["user_id"].ToString())) + "修改了客户记录．" + "','" + DateTime.Now.ToString() + "'," + Session["user_id"].ToString() + ")";
        SqlCommand comm1 = new SqlCommand(strSql, conn);
        comm1.ExecuteNonQuery();

        //更新的跟踪记录的次数
         (new Common()).UpdateFollowNumber(int.Parse(Request.Params["recordId"].ToString()));
        
        if (conn.State.ToString() == "Open")
        {
            conn.Close();
        }
    }

   
    protected void LinkButton2_Click(object sender, EventArgs e)
    {
        Response.Redirect("Copy of SeeCooperate.aspx");
    }
}

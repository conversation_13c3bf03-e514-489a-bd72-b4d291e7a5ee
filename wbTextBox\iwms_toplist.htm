﻿<html>
<head>
<title>列表新闻函数</title>
<meta content="text/html; charset=utf-8" http-equiv="content-type">
<link rel="stylesheet" type="text/css" href="pop.css">

<script type="text/javascript">

var wbtbWin;
if (window.opener){
	wbtbWin=launchParameters["wbtb"];
}else{
	wbtbWin=dialogArguments;
}


window.dialogWidth = "300px";
window.dialogHeight = "330px";

// 本窗口返回值
function ReturnValue(){
	
	var tmp;
	var str = "<div class=function>&lt;%=TopList(";

	tmp="";
	var o = document.theForm.sort;
	for(var i=0; i<o.options.length; i++){
		if (o.options[i].selected){
			if (tmp.length>0){
				tmp+=","+ o.options[i].value;
			}else{
				tmp = o.options[i].value;
			}
		}
	}
	if (tmp.indexOf(",")>0){
		// 多分类没有type和toptype
		str += "\""+ tmp +"\",";
	}else if (tmp==""){
		str += "0,\"sort\",\"" + document.theForm.toptype.options[document.theForm.toptype.selectedIndex].value +"\",";
	}else{
		str += tmp +",\"sort\",\""+ document.theForm.toptype.options[document.theForm.toptype.selectedIndex].value +"\",";
	}
	tmp = document.theForm.num.value;
	if (tmp==""){
		str += "8,";
	}else{
		str += tmp +",";
	}
	tmp = document.theForm.maxLength.value;
	if (tmp==""){
		str += "20,";
	}else{
		str += tmp +",";
	}
	str += document.theForm.showClass.checked +",";
	str += document.theForm.showHits.checked +",";
	str += document.theForm.showDate.checked +",";
	str += document.theForm.showNew.checked +",";
	str += document.theForm.showAuthor.checked +",";
	str += document.theForm.remarkLink.checked +",";
	str += document.theForm.alternat.checked+",";
	str += document.theForm.cols.value;
	str += ")%&gt;</div>";
	
	wbtbWin.WBTB_InsertHtml(str);

//	window.returnValue = null;
	window.close();
}

function sortChange()
{
	var obj = document.theForm.sort;
	if (obj.value=="0" || obj.value=="sortID"){
		obj.multiple = false;
	}else{
		obj.multiple = true;
	}
}
</script>


</head>

<body>
<form name="theForm">
  <table border="0" cellpadding="3" align="center">
    <tr> 
      <td colspan="2">
      		<table width="100%">
      			<tr><td>新闻分类</td>
      				<td>
			     	 <select multiple="false" name="sort" size="4" onchange="sortChange()">
			     	 	<option value="0">所有分类</option>
			     	 	<option value="sortID" selected="selected">页面所在分类</option>
			   	 </select>
	   		</td></tr>
	   	</table>
      	</td>
   </tr>
   <tr valign="baseline"> 
      <td>新闻数量<input name="num" type="text" size="3" value="8"></td>
      <td>标题长度<input name="maxLength" type="text" size="3" value="20"></td>
    </tr>
    <tr valign="baseline"> 
       <td>新闻类型<select name="toptype"><option value="new" selected="selected">最新</option><option value="hot">热门</otion><option value="weekhot">周热门</option><option value="dayhot">日热门</option></select></td>
      <td>标题分列<input name="cols" type="text" size="2" value="1"></td>
    </tr>
    <tr valign="baseline"> 
      <td><input name="showClass" type="checkbox">显示分类</td>
     <td><input name="showHits" type="checkbox">显示点击数</td>
    </tr>
    <tr valign="baseline"> 
       <td><input name="showDate" type="checkbox">显示日期</td>
     <td><input name="showNew" type="checkbox">显示New图标</td>
   </tr>
   <tr>
       <td><input name="showAuthor" type="checkbox">显示作者></td>
      <td><input name="remarkLink" type="checkbox">评论链接</td>
   </tr>
   <tr valign="baseline"> 
       <td><input name="alternat" type="checkbox">标题交替背景</td>
     <td></td>
    </tr>
   <tr>
   	<td align="center" colspan="2">
 		<button id="Ok" onclick="ReturnValue();">  确定  </button>&nbsp;
		<button onclick="window.close();">  取消  </button>
   	</td>
   </tr>
   <tr>
   	<td colspan="2" style="color:red">
   	注：可按住ctrl点选多个分类
   	</td>
   </tr>
  </table>
</form>

<script type="text/javascript">
function bindSort(arr){
	var obj = document.theForm.sort;
	for(var i=0; i<arr.length; i++){
		var oOption = document.createElement("OPTION");
		obj.options.add(oOption);
		oOption.innerText = arr[i][1];
		oOption.value = arr[i][0];
	}
}
</script>
<script type="text/javascript" charset="utf-8" src="../wbtb_data.aspx?sort=1"></script>
</body>
</html>

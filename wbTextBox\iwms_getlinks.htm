﻿<html>
<head>
<title>友情链接函数</title>
<meta content="text/html; charset=utf-8" http-equiv="content-type">
<link rel="stylesheet" type="text/css" href="pop.css">

<script type="text/javascript">

var wbtbWin;
if (window.opener){
	wbtbWin=launchParameters["wbtb"];
}else{
	wbtbWin=dialogArguments;
}


window.dialogWidth = "250px";
window.dialogHeight = "120px";

// 本窗口返回值
function ReturnValue(){
	
	var tmp;
	var str = "<div class=function>&lt;%=GetLinks(";
	tmp = document.theForm.sort.value;
	if (tmp==""){
		str += "0,";
	}else{
		str += tmp +",";
	}
	tmp = document.theForm.cols.value;
	if (tmp==""){
		str += "1,";
	}else{
		str += tmp +",";
	}
	str += document.theForm.imgLink.checked;
	str += ")%&gt;</div>";
	
	wbtbWin.WBTB_InsertHtml(str);

//	window.returnValue = null;
	window.close();
}

</script>
</head>

<body>
<form name="theForm">
  <table border="0" align="center" width="100%">
   <tr valign="baseline"> 
      <td align="right" nowrap>链接分类:</td>
      <td>
      	 <select name="sort">
		<option value="0" selected="selected">所有分类</option>
	</select>
	</td>
      <td rowspan="3" align="center">
 		<button id="Ok" onclick="ReturnValue();">  确定  </button><br/><br/>
		<button onclick="window.close();">  取消  </button>
     </td>
    </tr>
    <tr valign="baseline"> 
      <td align="right" nowrap>分列显示:</td>
      <td  align="left"> <input name="cols" type="text" size="10" value="1"></td>
    </tr>
    <tr valign="baseline"> 
      <td align="right" nowrap>图片链接:</td>
      <td  align="left"> <input name="imgLink" type="checkbox"></td>
    </tr>
</table>
</form>

<script type="text/javascript">
function bindLinkSort(arr){
	var obj = document.theForm.sort;
	for(var i=0; i<arr.length; i++){
		var oOption = document.createElement("OPTION");
		obj.options.add(oOption);
		oOption.innerText = arr[i][1];
		oOption.value = arr[i][0];
	}
}
</script>
<script type="text/javascript" src="../wbtb_data.aspx?linksort=1"></script>

</body>
</html>

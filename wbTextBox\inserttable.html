﻿<html>
<head>
<title>插入表格</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="pop.css">
<script type="text/javascript" language="javascript" src="img.js"></script>
<script language="JavaScript">
var oWidth, oWidthspecified, oAlign, oBorder, oBordercolor, oCellpad, oCellspace, oBgcolor, oWidthUnit, oRows, oColumns;

// 初始值
function InitDocument(){
	oWidth=document.getElementById("width");
	oWidthspecified=document.getElementById("widthspecified");
	oAlign=document.getElementById("align");
	oBorder=document.getElementById("border");
	oBordercolor=document.getElementById("bordercolor");
	oCellpad=document.getElementById("cellpad");
	oCellspace=document.getElementById("cellspace");
	oBgcolor=document.getElementById("bgcolor");
	oWidthUnit=document.getElementById("WidthUnit");
	oRows=document.getElementById("rows");
	oColumns=document.getElementById("columns");
}


function checkchange()
{
	if(oWidthspecified.checked==true){
    oWidth.disabled=false;
	}else{	
   oWidth.disabled=true;
   }
}	 

function colorchange(obj,oImg)
{
	if (obj.value != "")
	{
		try {
		oImg.style.backgroundColor = obj.value;
		}
		catch(e)
		{
			alert("你输入的不是颜色！");
			obj.value = "";
			oImg.style.backgroundColor = "";
		}
	}
}

function table(){
	var tablehtml="";
	tablehtml=tablehtml+"<table style='table-layout:fixed;border-collapse:collapse' ";
	if (oAlign.value=="left"||oAlign.value=="center"||oAlign.value=="right")
	 tablehtml=tablehtml+"align="+oAlign.value+" ";
	if (oBorder!="")
	 tablehtml=tablehtml+"border='"+oBorder.value+"' "
	 else
	 tablehtml=tablehtml+"border='1' "
	if(oCellpad.value!="")
	  tablehtml=tablehtml+"cellpadding='"+oCellpad.value+"' " 
	 if(oCellspace.value!="")
	  tablehtml=tablehtml+"cellspacing='"+oCellspace.value+"' "
	  if(oBordercolor.value!="")
	  tablehtml=tablehtml+"bordercolor='"+oBordercolor.value+"' " 
	  if(oBgcolor.value!="")
	  tablehtml=tablehtml+"bgcolor='"+oBgcolor.value+"' "
	  if  (oWidthspecified.checked==true)
    {
	if(oWidth.value!="")
    { tablehtml=tablehtml+"width='"+oWidth.value+"'"
	     tablehtml=tablehtml+oWidthUnit.value+""
	}	 
	} 	
	tablehtml=tablehtml+">"  
	for (i=1;i<=oRows.value;i++)
	{
	tablehtml=tablehtml+"<tr>";
	for (j=1;j<=oColumns.value;j++)
		tablehtml=tablehtml+"<td>&nbsp;</td>\r\n";
	tablehtml=tablehtml+"</tr>\r\n";
}  
	tablehtml= tablehtml+"</table>";
	if (window.opener){
		opener.WBTB_insertTable(tablehtml);
	}else{
		window.returnValue = tablehtml;
	}
	window.close();
}
</script>
</head>
<body bgcolor="menu" onload="InitDocument()" topmargin="5" leftmargin="5">
<table width="100%" border="0" cellspacing="0" cellpadding="3">
  <tr>
    <td>
    <fieldset><legend>表格大小</legend>
      <table border="0" cellspacing="0" cellpadding="3" width="100%">
        <tr>
          <td>表格行数：
            <input type="text" id="rows" size="8" maxlength="2" value="1" onkeypress="event.returnValue=IsDigit();">
          </td>
          <td align=right>表格列数：
            <input type="text" id="columns" size="8" maxlength="2" value="1" onkeypress="event.returnValue=IsDigit();">
          </td>
        </tr>
        </table>
      </fieldset>
      
      </td></tr>
      <tr><td>
      
      <fieldset><legend>表格布局</legend>
       <table border=0 cellspacing=0 cellpadding=3 width="100%">
        <tr>
          <td>边框粗细：
          <input id="border" type="text" value="1" size="8" maxlength="2" onkeypress="event.returnValue=IsDigit();"></td>
          <td align=right>对齐方式：
            <select id="align">
              <option selected>默认&nbsp;&nbsp;&nbsp;</option>
              <option value="left">居左</option>
              <option value="center">居中</option>
              <option value="right">居右</option>
            </select></td>
        </tr>
        <tr>
          <td>单元边距：
          <input type="text" id="cellpad" size="8" maxlength="2" onkeypress="event.returnValue=IsDigit();"></td>
          <td align=right>单元间距：
          <input type="text" id="cellspace" size="8" maxlength="2" onkeypress="event.returnValue=IsDigit();" value="0"></td>
        </tr>
        </table>
       </fieldset>
       
       </td></tr>
       <tr><td>
       
       <fieldset><legend>表格宽度</legend>
        <table border=0 cellspacing=0 cellpadding=3 width="100%">
        <tr>
          <td><input id="widthspecified" id="widthspecified" type="checkbox" class="cb" onClick="checkchange();" value="1" checked><label for="widthspecified">指定表格的宽度</label></td>
          <td align="right">
          <input id="width" type="text" value="500" size="5" maxlength="3" onkeypress="event.returnValue=IsDigit();" >
            <select id="WidthUnit">
              <option selected>像素</option>
              <option value="%">百分比</option>
            </select></td>
        </tr>
        </table>
       </fieldset>
       
       </td></tr>
       <tr><td>
       
       <fieldset><legend>表格颜色</legend>
        <table border=0 cellspacing=0 cellpadding=3 width="100%">
        <tr>
          <td>边框颜色：<input id='bordercolor' type='text' size="7" maxlength="20" value="#CCCCCC" onchange="colorchange(this,document.all.spanBdColor);"><img src="images/pickcolor.gif" style="background:#cccccc" align=absmiddle  onclick="selColor(bordercolor,this);">
          </td>
          <td align=right>背景颜色：<input id='bgcolor' type='text' size="7" maxlength="20"  onchange="colorchange(this,document.all.spanBgColor);"><span id="spanBgColor"><img src="images/pickcolor.gif" align=absmiddle  onclick="selColor(bgcolor,document.all.spanBgColor);"></span>
          </td>
        </tr>
    </table>
   </fieldset>
    </td>
  </tr>
</table>
<table width="100%">
<tr>
    <td align="right">
    <button type="submit" onclick='table();'  name="submit"> 确定 </button>
	&nbsp; &nbsp; <button onClick='window.close();'  name="button"> 取消 </button>
</td></tr>
</table>
</body>
</html>
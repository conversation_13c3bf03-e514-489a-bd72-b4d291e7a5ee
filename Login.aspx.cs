﻿using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Data.SqlClient;


public partial class Login : System.Web.UI.Page
{
    static readonly string ConnStr = InputParams.connectionStr;
    /// <summary>
    /// 页面加载
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void Page_Load(object sender, EventArgs e)
    {
        HttpCookie cookie = Request.Cookies["user"];
        if (Request.Params["id"] != null)
        {
            cookie = null;
        }
        else if (cookie != null && cookie.Values["nickname"] != null)//不用重新登录
        {
            Response.Redirect("Default.aspx");
        }

    }

    /// <summary>
    /// 登录用户按键
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void btnLogin_Click(object sender, EventArgs e)
    {
        string mobile = tbxUserName.Text.Trim();
        string pwd = tbxPassword.Text.Trim();
        string strSql = $"SELECT user_id,nickname,group_id,isGroupManager,state FROM Users WHERE ( email='{mobile}' OR mobile='{mobile}')  AND password='{pwd}'";
        SqlConnection conn = new SqlConnection(ConnStr);
        conn.Open();
        SqlCommand comm = new SqlCommand(strSql, conn);
        SqlDataReader dr = comm.ExecuteReader();

        if (dr.Read())
        {
            if (dr[4].ToString().Equals("0"))
            {
                Response.Write(Common.alertMsg("你的账号已被禁用,请联系企业/团队管理员了解详情"));
            }
            else
            {
                HttpCookie cookie = new HttpCookie("user");
                cookie.Values.Add("user_id", dr[0].ToString());
                cookie.Values.Add("nickname", dr[1].ToString());
                cookie.Values.Add("group_id", dr[2].ToString());
                cookie.Values.Add("isGroupManager", dr[3].ToString());

                if (ckbAutoLogin.Checked == true)
                {
                    cookie.Expires = DateTime.Now.AddYears(10);
                }
                Response.AppendCookie(cookie);
                Response.Redirect("Default.aspx");
            }
            dr.Close();

        }

        else
        {
            ClientScript.RegisterClientScriptBlock(this.GetType(), "info", "<script language=\"javascript\" type=\"text/javascript\">alert(\"用户名或密码错误,请重试！\");window.location.href='Login.aspx';</script>");
        }
        conn.Close();

    }


}

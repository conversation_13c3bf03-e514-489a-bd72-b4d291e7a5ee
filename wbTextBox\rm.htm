﻿<html><head>
<title>插入real媒体</title>
<meta content="text/html; charset=utf-8" http-equiv=Content-Type>
<link rel="stylesheet" type="text/css" href="pop.css">
<script type="text/javascript" language="javascript" src="img.js"></script>


<script type="text/javascript">
function ok()
{
	var s=document.getElementById("path").value;
	if (s.length<5)
	{
		alert("请填写文件地址");
	}else{
		s = s +"*"+ document.getElementById("selrow").value+"*"+ document.getElementById("selcol").value;
		if (opener){
			opener.WBTB_forrm(s);
		}else{
			window.returnValue = s;
		}
		window.close();
	}
}
</script>
<body bgcolor="menu" topmargin="5" leftmargin="5">

<table border=0 width="100%" cellPadding="3" cellSpacing="0" align="center">
  <tr>
    <td>
    <fieldset>
    	<legend>real媒体</legend>
	<table>
	<tr><td colspan=2>
	文件类型：rm, ra, ram, rmvb
	</td>
	</tr>
	<tr>
	<td  colspan="2">
	地址: <input id="path" size="28" value="">
	</td></tr>
	<tr>
	<td>宽度: <input id="selrow" size="7" value="480" onkeypress="event.returnValue=IsDigit();"></td>
	<td align="right">
	高度: <input id="selcol" size="7" value="360" onkeypress="event.returnValue=IsDigit();"></td>
	</tr>
	</table>
    </fieldset>
  </td>
  </tr>
  <tr>
    <td align="right"><button id="Ok" onclick="ok()" type=submit>确认</button>&nbsp; &nbsp;<button  onclick="window.close();">取消</button>
      </td></tr></table>
      
</body></html><script>

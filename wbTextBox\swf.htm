﻿<html><head><title>插入flash</title>
<meta content="text/html; charset=utf-8" http-equiv="content-type">
<link rel="stylesheet" type="text/css" href="pop.css">
<script type="text/javascript" language="javascript" src="img.js"></script>

<script type="text/javascript">
function ok()
{
	var s=document.getElementById("path").value;
	var rows=document.getElementById("selrow").value;
	var cols=document.getElementById("selcol").value;
	var fs = document.getElementById("disFs").checked;
	var fst = document.getElementById("selfst").value;
	if (s.length<5)
	{
		alert('请填写文件地址');
	}else{
		s = s+"*"+ rows +"*"+ cols +"*"+ (fs?"1":"0") +"*"+ fst;
		if (opener){
			opener.WBTB_forswf(s);
		}else{
			window.returnValue = s;
		}
		window.close();
	}
}
</script>
</head>
<body bgcolor="menu" topmargin=5 leftmargin=5>


<table border=0 width="100%" cellpadding=3 cellspacing=0 align="center">
<tr>
<td>
<fieldset>
<legend>flash媒体</legend>
	<table>
	<tr><td colspan=2>
	地址: <INPUT id="path" size=25 value="">
	</td></tr>
	<tr>
	<td>宽度: <input id="selrow" size=7 value=480 onkeypress="event.returnValue=IsDigit();"></td>
	<td align="right">
	高度: <input id="selcol" size=7 value=360 onkeypress="event.returnValue=IsDigit();"></td>
	</tr>
	<tr><td colspan="2">
		<input type="checkbox" id="disFs" name="disFs"><label for="disFs">显示全屏链接</label>
		<input id="selfst" size="14" value="『全屏查看』">
	</td></tr>
	</table>
</fieldset>
</td></tr>
<tr><td align=right>
<button id="Ok" onclick="ok()" type="submit">确定</button>&nbsp; &nbsp;
<button onclick="window.close();">取消</button>
</TD></TR>
</TABLE>

</BODY></HTML>

﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="ApplyManage.aspx.cs" Inherits="admin_ApplyManage" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>审批管理</title>
    <meta name="viewport" content="width=device-width,height=device-height,initial-scale=0.65">
    <style>
        .gridView {
            margin: 20px auto;
            width: 80%
        }
        @media (max-width: 768px) {
        .gridView {
                    margin: 20px auto;
                    width: 100%;
                }
        }

    </style>
</head>
<body>
    <form id="form1" runat="server" method="post">
        <div class="applyMng" style="margin-top: 50px;">
            <div style="text-align: center">
                <asp:HyperLink ID="HyperLink1" runat="server" NavigateUrl="~/Default.aspx" ForeColor="#3366FF">←返回首页</asp:HyperLink>
            </div>
            <asp:GridView ID="GridView1" runat="server" OnSelectedIndexChanged="GridView1_SelectedIndexChanged" OnRowCommand="GridView1_RowCommand" CssClass="gridView" AllowPaging="True" BorderStyle="None"  CellPadding="5" AutoGenerateColumns="False" OnRowDataBound="GridView1_RowDataBound" OnPageIndexChanging="GridView1_PageIndexChanging" PageSize="10" EmptyDataText="暂时没有申请记录!">
                <Columns>
                    <asp:BoundField DataField="id" HeaderText="id" Visible="false" />
                    <asp:BoundField DataField="user_id" HeaderText="用户Id" />
                    <asp:BoundField DataField="nickname" HeaderText="用户昵称" />
                    <asp:BoundField DataField="truename" HeaderText="真名" />
                    <asp:BoundField DataField="apply_time" HeaderText="申请日期" />
                    <asp:BoundField DataField="is_deal" HeaderText="处理状态" />
                    <asp:BoundField DataField="deal_result" HeaderText="处理结果" />
                    <asp:BoundField DataField="deal_time" HeaderText="处理日期" />
                    <asp:TemplateField HeaderText="操作">
                        <ItemTemplate>
                            <asp:Button ID="btnAgree" runat="server" Text="同意" CommandName="Agree" CommandArgument='<%# Eval("id") + "," + Eval("user_id") %>'
                                OnClientClick="return confirm('是否同意该用户成为管理员?');" />
                            &nbsp;</br>
                             <asp:Button ID="btnRefuse" runat="server" Text="拒绝" CommandName="Refuse" CommandArgument='<%# Eval("id") + "," + Eval("user_id") %>'
                                 OnClientClick="return confirm('是否拒绝该申请?');" />
                        </ItemTemplate>
                    </asp:TemplateField>
                </Columns>
                <RowStyle HorizontalAlign="Center" />
            </asp:GridView>
        </div>
    </form>
</body>
</html>

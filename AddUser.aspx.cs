﻿
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Data.SqlClient;

public partial class AddUser : System.Web.UI.Page
{
    int id;
    public const int AddCount = 1;
    string[] strInserts;
    int actualAddCount=0;
    public const string AETable = "Users";
    readonly string ConnStr = InputParams.connectionStr;


    int tbxCharLength = 250;
    public EnumField[][] EnumField()
    {
        EnumField[] enum0 ={ new EnumField(0, "一般用户"), new EnumField(1, "管理员") };
        EnumField[][] efTs ={ enum0 };
        return efTs;
    }

    public Field[] InitFields()
    {
        Field fld0 = new Field(0, "email", "电子邮箱", 80, EnumFieldType.charType, 0, 0, "", 0, "");
        Field fld1 = new Field(0, "mobile", "手机号码", 80, EnumFieldType.charType, 0, 0, "", 0, "“手机号码”必须填写");
        Field fld2 = new Field(0, "password", "密码", 80, EnumFieldType.charType, 0, 0, "", 0, "“密码”必须填写");
        Field fld3 = new Field(0, "passwordagain", "确认密码", 80, EnumFieldType.charType, 0, 0, "", 0, "“密码”必须一致");
        Field fld4 = new Field(0, "nickname", "用户名", 80, EnumFieldType.charType, 0, 0, "", 0, "");
        Field fld5 = new Field(0, "truename", "真实姓名", 80, EnumFieldType.charType, 0, 0, "", 0, "");
        //Field fld6 = new Field(0, "isGroupManager", "用户类型", 50, EnumFieldType.enumType, 0, 0, "", 0, "");
       // Field fld7 = new Field(0, "isNewYufutong", "预付通用户", 60, EnumFieldType.boolType, 0, 0, "", 0, "");

        Field[] flds ={ fld0, fld1, fld2, fld3, fld4, fld5};
        return flds;
    }

    protected void Page_Load(object sender, EventArgs e)
    {
        if (Request.QueryString["id"] != null)
        {
            try
            {
                id = int.Parse(Request.QueryString["id"]);
                GeneAddMould();
            }
            catch
            {
            }
        }
        else
        {
            GeneAddMould();
        }
    }
 
    /// <summary>
    /// 添加按钮的触发事件
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    
    
    protected void btnAdd_Click(object sender, EventArgs e)
    {
        GetInsertStr();
    
        try
        {
            Field[] fields = InitFields();
            string strAttentionMessage1 = GetAttentionMessage(fields);
            string strAttentionMessage = "<p style ='color:Red '>上次共创建了" +"1"+ "个用户<br/>" + strAttentionMessage1.ToString() + "</p>";
            Add();

            if (strAttentionMessage1 != "")
            {
                Response.Write("<script language=javascript>alert(\'" + strAttentionMessage1 + "\');</script>"); //提示报错信息
                //Response.Redirect("AddUser.aspx?info=" + strAttentionMessage);
            }
            else
            {
                Response.Write("<script language=javascript>alert(\'添加用户成功！\');</script>"); //提示用户添加成功信息
                
               
                //Response.Redirect("AddUsers.aspx");
            }
            
        
        }
        catch (SqlException ex)
        {
            Response.Write(ex.ToString());
        }
    
    
    }

    protected void lbt_Command(object sender, CommandEventArgs e)
    {
        Field[] fields = InitFields();
        EnumField[][] efTs = EnumField();

        for (int addIndex = 0; addIndex < AddCount; addIndex++)
        {
            PlaceHolder pHolder = (PlaceHolder)div1.FindControl("placeHolder" + addIndex);
            for (int fieldIndex = 0; fieldIndex < fields.Length; fieldIndex++)
            {
                string strFldName = fields[fieldIndex].fieldName + addIndex;
                switch (fields[fieldIndex].fieldType)
                {
                    case EnumFieldType.enumType:
                        string str1 = "ddl" + strFldName;
                        DropDownList ddl = (DropDownList)pHolder.FindControl(str1);
                        Session[str1] = ddl.Text;
                        continue;

                    case EnumFieldType.boolType:
                        string str2 = "rb" + strFldName + "1";
                        if (((RadioButton)pHolder.FindControl(str2)).Checked == true)
                        {
                            Session[str2] = 1;
                        }
                        else
                        {
                            Session[str2] = 0;
                        }
                        continue;

                    default:
                        string str3 = "tbx" + strFldName;
                        Session[str3] = ((TextBox)(pHolder.FindControl(str3))).Text.Trim().ToString();
                        continue;
                }
            }
        }
        Session["supPage"] = "AddUser.aspx";
        string strDire = e.CommandArgument.ToString() + "?tbxName=" + e.CommandName;
        Response.Redirect(strDire);
    }

    /// <summary>
    /// 生成信息输入模板
    /// </summary>
    /// <param name="placeHolder"></param>
    /// <param name="fields"></param>
    public void GeneAddMould()
    {
        div1.Controls.Clear();
        Field[] fields = InitFields();
        EnumField[][] efTs = EnumField();
        strInserts = new string[5];
        for (int addIndex = 0; addIndex < AddCount; addIndex++)
        {
            PlaceHolder placeHolder = new PlaceHolder();
            placeHolder.ID = "placeHolder" + addIndex;
            div1.Controls.Add(placeHolder);
            PlaceHolder pHolder = (PlaceHolder)div1.FindControl("placeHolder" + addIndex);
            if (addIndex != 0)
            {
                Literal ltlHR = new Literal();
                ltlHR.Text = "<HR/>";
                placeHolder.Controls.Add(ltlHR);
            }

            Literal ltlTag = new Literal();
            ltlTag.Text = "第" + (addIndex + 1) + "条";
            placeHolder.Controls.Add(ltlTag);

            for (int fieldIndex = 0; fieldIndex < fields.Length; fieldIndex++)
            {
                Label lbl1 = new Label();
                lbl1.Text = "<br/>";
               

                Label lblTag = new Label();
                lblTag.Text = fields[fieldIndex].fieldShowName + "：" + " ";
                lblTag.ForeColor = System.Drawing.ColorTranslator.FromHtml(InputParams.lblFieldShowNameColor);
                
                //添加表头

                if (fields[fieldIndex].fieldShowName == "用户类型")
                {
                    if (Request.Params["register"] == null)
                    {
                        int iGroupManager = int.Parse(Session["isGroupManager"].ToString());
                        if (iGroupManager == 1 || iGroupManager == 2)//该用户为管理员，表头添加删除标志
                        {
                            pHolder.Controls.Add(lbl1);
                            pHolder.Controls.Add(lblTag);
                        }
                    }
                }
                else
                {
                    pHolder.Controls.Add(lbl1);
                    pHolder.Controls.Add(lblTag);
                }
                
                string strFldName = fields[fieldIndex].fieldName + addIndex;
                switch (fields[fieldIndex].fieldType)
                {
                    case EnumFieldType.charType:
                        TextBox tbx1 = new TextBox();
                        string str1 = "tbx" + strFldName;
                        tbx1.ID = "tbx" + strFldName;
                        tbx1.Width = tbxCharLength;
                        tbx1.Height = InputParams.tbxHeight;
                        tbx1.TextMode = TextBoxMode.SingleLine;

                        //密码要用密码的输入形式
                        if (fields[fieldIndex].fieldShowName == "密码" || fields[fieldIndex].fieldShowName == "确认密码")
                            tbx1.TextMode = TextBoxMode.Password;


                        if (id == 1)
                        {
                            tbx1.Text = Session[str1].ToString();
                        }
                        pHolder.Controls.Add(tbx1);

                       if (fields[fieldIndex].attentionMessage != "")
                        {

                            Literal ltlX = new Literal();
                            ltlX.Text = "&nbsp;&nbsp;<b style='color :red; font-size :7px'> ★</b>";
                            pHolder.Controls.Add(ltlX);
                            for (int i = 1; i < fields[fieldIndex].fieldShowName.Length; i++)
                            {
                                Literal ltlspace1 = new Literal();
                                ltlspace1.Text = "&nbsp;&nbsp;";
                                pHolder.Controls.Add(ltlspace1);

                            } 
                       
                       }
                       else  //为了方便每行对齐，没有为★的则加四个空格
                        {
                            Literal ltlspace = new Literal();
                            ltlspace.Text = "&nbsp;&nbsp;";
                            pHolder.Controls.Add(ltlspace);

                            //if (fields[fieldIndex].fieldShowName.Length <4)//若小于
                            //{
                            for (int i = 0; i < fields[fieldIndex].fieldShowName.Length; i++)
                            {
                                Literal ltlspace1 = new Literal();
                                ltlspace1.Text = "&nbsp;&nbsp;";
                                pHolder.Controls.Add(ltlspace1);

                            }
                               
                        }
                        //if (fields[fieldIndex].attentionMessage != "")
                        //{
                        //    RequiredFieldValidator rfv = new RequiredFieldValidator();
                        //    rfv.ID = "rfv" + strFldName;
                        //    rfv.ErrorMessage = fields[fieldIndex].attentionMessage.ToString();
                        //    rfv.ControlToValidate = "tbx" + strFldName;
                        //    pHolder.Controls.Add(rfv);

                        //}

                        //((TextBox)pHolder.FindControl("tbx" + strFldName)).Attributes.Add("onMouseOver", "this.style.backgroundColor='" + InputParams.tbxOnmouseoverColor + "';");
                        //((TextBox)pHolder.FindControl("tbx" + strFldName)).Attributes.Add("onMouseOut", "this.style.backgroundColor='" + InputParams.tbxOnmouseoutColor + "';");
                        continue;

                    case EnumFieldType.longcharType:
                        TextBox tbx2 = new TextBox();
                        string str2 = "tbx" + strFldName;
                        tbx2.ID = "tbx" + strFldName;
                        tbx2.Width = InputParams.tbxLongCharLength;
                        tbx2.Height = InputParams.tbxLongCharHeight;
                        tbx2.TextMode = TextBoxMode.MultiLine;
                        if (id == 1)
                        {
                            tbx2.Text = Session[str2].ToString();
                        }
                        pHolder.Controls.Add(tbx2);
                        //((TextBox)pHolder.FindControl("tbx" + strFldName)).Attributes.Add("onMouseOver", "this.style.backgroundColor='" + InputParams.tbxOnmouseoverColor + "';");
                        //((TextBox)pHolder.FindControl("tbx" + strFldName)).Attributes.Add("onMouseOut", "this.style.backgroundColor='" + InputParams.tbxOnmouseoutColor + "';");
                        continue;

                    case EnumFieldType.numberType:
                        TextBox tbx3 = new TextBox();
                        string str3 = "tbx" + strFldName;
                        tbx3.ID = "tbx" + strFldName;
                        tbx3.Width = InputParams.tbxNumberLength;
                        tbx3.Height = InputParams.tbxHeight;
                        if (id == 1)
                        {
                            tbx3.Text = Session[str3].ToString();
                        }
                        pHolder.Controls.Add(tbx3);
                        //((TextBox)pHolder.FindControl("tbx" + strFldName)).Attributes.Add("onMouseOver", "this.style.backgroundColor='" + InputParams.tbxOnmouseoverColor + "';");
                        //((TextBox)pHolder.FindControl("tbx" + strFldName)).Attributes.Add("onMouseOut", "this.style.backgroundColor='" + InputParams.tbxOnmouseoutColor + "';");
                        if (fields[fieldIndex].linkPage.ToString() != "")
                        {
                            LinkButton lbt = new LinkButton();
                            lbt.Text = "  点此查找";
                            lbt.ID = "lbt" + strFldName;
                            lbt.Command += new CommandEventHandler(lbt_Command);
                            lbt.CommandName = "tbx" + strFldName;
                            lbt.CommandArgument = fields[fieldIndex].linkPage.ToString();
                            pHolder.Controls.Add(lbt);
                        }
                        continue;

                    case EnumFieldType.dateType:
                        TextBox tbx4 = new TextBox();
                        string str4 = "tbx" + strFldName;
                        tbx4.Text = DateTime.Now.ToShortDateString().ToString();
                        tbx4.ID = "tbx" + strFldName;
                        tbx4.Width = InputParams.tbxDateLength;
                        tbx4.Height = InputParams.tbxHeight;
                        if (id == 1)
                        {
                            tbx4.Text = Session[str4].ToString();
                        }
                        pHolder.Controls.Add(tbx4);
                        ((TextBox)pHolder.FindControl("tbx" + strFldName)).Attributes.Add("onClick", "javascript:calendar()");
                        ((TextBox)pHolder.FindControl("tbx" + strFldName)).Attributes.Add("onClick", "javascript:calendar()");
                        continue;

                    case EnumFieldType.boolType:
                        RadioButton rbY = new RadioButton();
                        RadioButton rbN = new RadioButton();
                        string strY = "rb" + strFldName + "1";
                        string strN = "rb" + strFldName + "2";
                        rbY.ID = "rb" + strFldName + "1";
                        rbY.GroupName = "gn" + strFldName;
                        rbY.Text = "是";
                        rbY.ForeColor = System.Drawing.ColorTranslator.FromHtml(InputParams.rbYNColor);
                        rbN.ID = "rb" + strFldName + "2";
                        rbN.GroupName = "gn" + strFldName;
                        rbN.Text = "否";
                        rbN.ForeColor = System.Drawing.ColorTranslator.FromHtml(InputParams.rbYNColor);
                        pHolder.Controls.Add(rbY);
                        pHolder.Controls.Add(rbN);

                        if (id == 1)
                        {
                            if (Session[strY].ToString() != null)
                            {
                                if (Session[strY].ToString() == "1")
                                {
                                    ((RadioButton)pHolder.FindControl(strY)).Checked = true;
                                }
                                else
                                {
                                    ((RadioButton)pHolder.FindControl(strN)).Checked = true;

                                }
                            }
                        }
                        continue;

                    case EnumFieldType.picType:
                        TextBox tbx5 = new TextBox();
                        string str5 = "tbx" + strFldName;
                        tbx5.ID = "tbx" + strFldName;
                        tbx5.Width = InputParams.tbxCharLength;
                        tbx5.Height = InputParams.tbxHeight;
                        tbx5.TextMode = TextBoxMode.MultiLine;
                        tbx5.Wrap = true;
                        tbx5.Style.Add("overflow", "hidden");

                        if (id == 1)
                        {
                            tbx5.Text = Session[str5].ToString();
                        }
                        pHolder.Controls.Add(tbx5);
                        //((TextBox)pHolder.FindControl("tbx" + strFldName)).Attributes.Add("onMouseOver", "this.style.backgroundColor='" + InputParams.tbxOnmouseoverColor + "';");
                        //((TextBox)pHolder.FindControl("tbx" + strFldName)).Attributes.Add("onMouseOut", "this.style.backgroundColor='" + InputParams.tbxOnmouseoutColor + "';");
                        continue;

                    case EnumFieldType.enumType:
                        DropDownList ddl = new DropDownList();
                        string str6 = "ddl" + strFldName;
                        ddl.ID = "ddl" + strFldName;
                        int enumT = fields[fieldIndex].enumTag;
                        ddl.Width = InputParams.ddlWidth;
                        ddl.AutoPostBack = false;

                        for (int enumLen = 0; enumLen < efTs[enumT].Length; enumLen++)
                        {
                            ListItem li = new ListItem();
                            li.Value = efTs[enumT][enumLen].enumItem.ToString();
                            li.Text = efTs[enumT][enumLen].itemDetail;
                            if (id == 1)
                            {
                                if ((Session[str6] != null) && (efTs[enumT][enumLen].enumItem.ToString() == Session[str6].ToString()))
                                {
                                    li.Selected = true;
                                }
                            }
                            ddl.Items.Add(li);
                        }
                        pHolder.Controls.Add(ddl);


                        //若是管理员才有权限选择些功能
                        int iGroupManager = int.Parse(Session["isGroupManager"].ToString());
                        if (Request.Params["register"] != null || (iGroupManager != 1 && iGroupManager != 2))
                            ddl.Visible = false;//该用户为管理员，表头添加删除标志
                              
                        //((DropDownList)pHolder.FindControl("ddl" + strFldName)).Attributes.Add("onMouseOver", "this.style.backgroundColor='" + InputParams.tbxOnmouseoverColor + "';");
                        //((DropDownList)pHolder.FindControl("ddl" + strFldName)).Attributes.Add("onMouseOut", "this.style.backgroundColor='" + InputParams.tbxOnmouseoutColor + "';");
                        continue;
                }
            }
        }
    }


    /// <summary>
    /// 获得添加记录的SQL Insert字符串
    /// </summary>
    /// <param name="placeHolder"></param>
    /// <param name="fields"></param>
    /// <returns></returns>
    public void GetInsertStr()
    {
        Field[] fields = InitFields();
        EnumField[][] efTs = EnumField();

        for (int addIndex = 0; addIndex < AddCount; addIndex++)
        {
            PlaceHolder pHolder = (PlaceHolder)div1.FindControl("placeHolder" + addIndex);
            string strIns = "INSERT INTO " + AETable + "(group_id,";
            string strVal = " VALUES ( 1,";

            for (int fieldIndex = 0; fieldIndex < fields.Length; fieldIndex++)
            {
                if (fieldIndex != 0)
                {
                    if (fields[fieldIndex].fieldShowName =="确认密码")
                    {
                    }
                    else
                    {
                        strIns += "," + fields[fieldIndex].fieldName;
                        if (fieldIndex != fields.Length)
                        {
                            strVal += ",";
                        }
                    }
                }
                else
                {
                    if (fields[fieldIndex].fieldShowName == "确认密码")
                    { 
                    }
                    else
                    strIns += fields[fieldIndex].fieldName;
                }
                string strFldName = fields[fieldIndex].fieldName + addIndex;

                switch (fields[fieldIndex].fieldType)
                {

                    case EnumFieldType.numberType:
                        string tbxName2 = "tbx" + strFldName + "2";
                        if (((TextBox)pHolder.FindControl("tbx" + strFldName)).Text != "")
                        {
                            if (Session[tbxName2] != null)
                            {
                                strVal += int.Parse(Session[tbxName2].ToString());
                            }
                            else
                            {
                                strVal += ((TextBox)pHolder.FindControl("tbx" + strFldName)).Text.Trim();
                            }
                        }
                        else
                        {
                            strVal += "' '";
                        }
                        continue;


                    case EnumFieldType.dateType:
                        if (((TextBox)pHolder.FindControl("tbx" + strFldName)).Text != "")
                        {
                            DateTime dt = DateTime.Parse(((TextBox)pHolder.FindControl("tbx" + strFldName)).Text.Trim());
                            strVal += "'" + dt.ToString() + "'";
                        }
                        continue;

                    case EnumFieldType.enumType:
                        string strDdl = ((DropDownList)pHolder.FindControl("ddl" + strFldName)).Text.Trim();
                        int iValue = 0;
                        string strText;
                        int enumT = fields[fieldIndex].enumTag;

                        for (int enumLen = 0; enumLen < efTs[enumT].Length; enumLen++)
                        {
                            iValue = efTs[enumT][enumLen].enumItem;
                            strText = efTs[enumT][enumLen].itemDetail;
                            if (strDdl == strText)
                            {
                                break;
                            }
                        }
                        strVal += iValue.ToString();
                        continue;

                    case EnumFieldType.boolType:

                        if (((RadioButton)pHolder.FindControl("rb" + strFldName + "1")).Checked)
                        {
                            strVal += "1";
                        }
                        else
                        {
                            strVal += "0";
                        }
                        continue;

                    default:

                        if (fields[fieldIndex].fieldShowName == "确认密码")
                            break;
                        if (((TextBox)pHolder.FindControl("tbx" + strFldName)).Text != "")
                        {
                            strVal += "'" + ((TextBox)pHolder.FindControl("tbx" + strFldName)).Text.Trim() + "'";
                        }
                        else
                        {
                            strVal += "' '";
                        }
                        continue;
                }
            }
            strIns += ")";
            strVal += ")";
            strInserts[addIndex] = strIns + strVal;
        }
    }

    /// <summary>
    /// 向数据库添加记录
    /// </summary>
    /// <param name="strInsert"></param>
    public void Add()
    {
        SqlConnection conn = new SqlConnection(ConnStr);

        if (conn.State.ToString() == "Closed")
        {
            conn.Open();
        }
        for (int addIndex = 0; addIndex < AddCount; addIndex++)
        {
            SqlCommand comm = new SqlCommand(strInserts[addIndex], conn);
            comm.ExecuteNonQuery();
        }
        if (conn.State.ToString() == "Open")
        {
            conn.Close();
        }
    }
    /// <summary>
    /// 返回按键事件
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void btnReturn_Click(object sender, EventArgs e)
    {
        if (Request.Params["register"] == null)
            Response.Redirect("Default.aspx");
        else
            Response.Redirect("Login.aspx");
    }

    /// <summary>
    /// 获得必要项的提示信息
    /// </summary>
    /// <param name="tables"></param>
    /// <returns></returns>
    protected string GetAttentionMessage(Field[] fields)
    {
        string strAttentionMessage = "";
        actualAddCount = 0;
        string passwordtest = "";     //用来每次记录密码，方便同确认密码相对

        for (int addIndex = 0; addIndex < AddCount; addIndex++)
        {
            int iTag = 1;

            for (int fieldIndex = 0; fieldIndex < fields.Length; fieldIndex++)
            {
                string strFldName = fields[fieldIndex].fieldName + addIndex;
                string attentionMessage = fields[fieldIndex].attentionMessage.ToString();


                if (attentionMessage != "")
                {
                    TextBox tbx = (TextBox)div1.FindControl("tbx" + strFldName);


                    if (fields[fieldIndex].fieldShowName == "密码")
                        passwordtest = tbx.Text;//记录密码

                    if (fields[fieldIndex].fieldShowName == "确认密码")
                    {
                        if (tbx.Text != passwordtest)
                        {
                            iTag = 0;
                            strAttentionMessage += "第" + (addIndex + 1) + "个用户的" + attentionMessage ;

                        }

                    }

                    else //如果不是确认密码
                    {
                        if (tbx.Text == "")
                        {
                            iTag = 0;
                            strAttentionMessage += "第" + (addIndex + 1) + "个用户的" + attentionMessage;

                        }
                    }
                }
            }
            if (iTag == 1)
            {
                actualAddCount++;
            }
        }

        return strAttentionMessage;
    }

}


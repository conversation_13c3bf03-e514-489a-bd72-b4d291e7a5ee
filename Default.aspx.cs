﻿using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Data.SqlClient;


public partial class See : System.Web.UI.Page
{ /// <summary>
  /// 声明全局变量
  /// </summary>
    int recordCount;
    int pageCount;
    int fieldsCount;
    int longNum;
    int pagerIndex;
    int rowSpan;
    int colSpan;
    int[] picLongchar;
    int[] fieldTableShowLength;
    string subSearchStr;
    int lineLength = 2;

    //iHM为0时，不生成时分控件，为1时，生成时分控件
    //int iHM = 0; 
    int iHM = 1;

    #region 查找参数初始化
    //利用此模板设计查找页面时，只需要修改这一部分
    readonly string ConnStr = InputParams.connectionStr;
    //private static string ConnStr = WebConfigurationManager.ConnectionStrings["5cityConnectionString"].ConnectionString;
    const string mainTable = "FollowClient";
    const string pKey = "id";
    string[] singleSearch = { "FollowClient", "id", "1" };
    SqlConnection sqlCon;
    
    public EnumField[][] EnumField()
    {
        EnumField[] enum0 = { new EnumField(4, "待联系"), new EnumField(0, "跟进中"), new EnumField(1, "已确认"), new EnumField(2, "已合作"), new EnumField(3, "未合作") };
        EnumField[] enum1 = { new EnumField(1, "★"), new EnumField(2, "★★"), new EnumField(3, " ★★★"), new EnumField(4, "★★★★"), new EnumField(5, "★★★★★") };
        EnumField[] enum2 = { new EnumField(0, "盒饭"), new EnumField(1, "桶饭"), new EnumField(2, "食堂"), new EnumField(3, "个人解决") };

        EnumField[][] efTs = { enum0, enum1, enum2 };
        return efTs;
    }
   
    public bool isMobile() {
        var context = HttpContext.Current;
        if (context == null)
            return false;

        var userAgent = context.Request.UserAgent?.ToLower() ?? string.Empty;
        string[] mobileDevices = { "iphone", "android", "windows phone", "mobile" };
        for (int i = 0; i < mobileDevices.Length; i++)
        {
            if (userAgent.Contains(mobileDevices[i]))
                return true;
        }

        return false;
    }


    //计算时差
    public static string FormatDateTime(DateTime dt2)
    {
        DateTime now = DateTime.Now;

        // 比较日期部分是否相同（忽略时间）
        if (dt2.Date == now.Date)
        {
            // 今天：显示 "今天 17:17"
            return $"今天 {dt2:HH:mm}";
        }
        else
        {
            // 计算日期差（结果可能为负数）
            TimeSpan diff = dt2.Date - now.Date;
            int daysDiff = diff.Days;

            if (daysDiff > 0)
            {
                // 未来日期：显示 "3天后"
                return $"{daysDiff}天后";
            }
            else
            {
                // 过去日期：显示 "5天前"（根据需求可选）
                return $"{Math.Abs(daysDiff)}天前";
            }
        }
    }
    private Field[] tableFields;
    
    public Table[] Tables()
    {
        string tblName1 = "FollowClient";

        Field fld0 = new Field(0, "firm_name", "公司名称", 10, EnumFieldType.charType, 0, 0, "", 0, "");
        Field fld1 = new Field(1, "area", "区域", 4, EnumFieldType.enumType, 0, 0, "", 0, "");
        Field fld2 = new Field(2, "linkman", "联系人", 5, EnumFieldType.charType, 0, 0, "", 0, "");
        Field fld3 = new Field(3, "telephone", "电话", 8, EnumFieldType.charType, 0, 0, "", 0, "");
        Field fld10 = new Field(4, "email", "邮箱", 8, EnumFieldType.charType, 0, 0, "", 0, "");
        //Field fld4 = new Field(4, "is_unify_reservation", "订餐类型", 0, EnumFieldType.enumType, 0, 2, "", 0, "");
        //Field fld5 = new Field(5, "is_telephone_client", "跟单客户", 0, EnumFieldType.boolType, 0, 0, "", 0, "");
        //Field fld6 = new Field(6, "is_azw_client", "爱知味客户", 0, EnumFieldType.boolType, 0, 0, "", 0, "");
        //Field fld7 = new Field(7, "firm_number", "公司人数", 0, EnumFieldType.numberType, 0, 0, "", 0, "");
        //Field fld8 = new Field(8, "dinner_level", "用餐标准", 0, EnumFieldType.doubleType, 0, 0, "", 0, "");
        //Field fld9 = new Field(9, "interviewee_sex", "联系人性别", 0, EnumFieldType.boolType, 0, 0, "", 0, "");
        Field fld4 = new Field(5, "last_follow_time", "最后跟踪时间", 5, EnumFieldType.dateType, EnumSortType.DESC, 0, "", 0, "");
        Field fld5 = new Field(6, "client_state", "客户状态", 0, EnumFieldType.enumType, 0, 0, "", 0, "");
        //Field fld13 = new Field(13, "cooperation_name", "供应商", 0, EnumFieldType.charType, 0, 0, "", 0, "");
        //Field fld14 = new Field(14, "presented", "已投资金额", 0, EnumFieldType.doubleType, 0, 0, "", 0, "");
        Field fld6 = new Field(7, "Type", "客户意向指数", 8, EnumFieldType.enumType, 0, 1, "", 0, "");
        Field fld7 = new Field(8, "creat_time", "创建时间", 0, EnumFieldType.dateType, EnumSortType.DESC, 0, "", 0, "");
        Field fld8 = new Field(9, "userAddress", "地址", 5, EnumFieldType.charType, 0, 0, "", 0, "");
        Field fld9 = new Field(10, "investigate", "咨询内容", 18, EnumFieldType.charType, 0, 0, "", 0, "");
        //Field fld11 = new Field(11, "investigate", "咨询内容", 18, EnumFieldType.charType, 0, 0, "", 0, "");
        Field fld19 = new Field(11, "follow_number", "追加次数", 4, EnumFieldType.numberType, 0, 0, "", 0, "");
        Field fld20 = new Field(12, "next_follow_time", "预计下次回访时间", 0, EnumFieldType.dateType, EnumSortType.DESC, 0, "", 0, "");
		//fld20.show = isMobile();
		fld1.show = false;//区域
		fld2.show = false;//联系人
		fld3.show = false;//电话
		fld10.show = false;//邮箱
		fld7.show = false;//创建时间
		fld8.show = false;//地址
        fld5.show = false;//客户状态
        if (isMobile()) {
            fld6.position = 1;//客户意向指数
            fld7.position = 2;//创建时间
            fld19.position = 3;//追加次数
            fld4.position = 4;//最后跟踪时间
            fld20.position = 5;//预计下次回访时间
            fld9.position = 6;//咨询内容
            fld5.position = 7;//客户状态
            fld1.position = 8;//区域
            fld2.position = 9;//联系人
            fld3.position = 10;//电话
            fld10.position = 11;//邮箱
            fld8.position = 12;//创建时间

        }
        /* fld19.show = !isMobile();*///追加次数

        //ViewState["sortExpression"] = "最后跟踪时间";
        if (ViewState["sortExpression"] == null)
        {
            ViewState["sortExpression"] = "最后跟踪时间";
        }

        if (ViewState["sortDirection"] == null)
        {
            ViewState["sortDirection"] = "DESC";
        }
        Field[] flds1 = { };
        if (isMobile())
        {
            flds1 = new Field[] { fld0, fld6,fld7, fld19, fld4, fld20, fld9 ,fld5, fld1, fld2, fld3, fld10, fld8 };
        }
        else {
            flds1 = new Field[] { fld0, fld1, fld2, fld3, fld10, fld4, fld5, fld6, fld7, fld8, fld9, fld19, fld20 };

        }
		

		tableFields = flds1;

        Join join = new Join();
        Table table1 = new Table(tblName1, join, flds1);

        string tableName2 = "FollowRecord";
        Field t2Field0 = new Field(0, "group_id", "企业id", 10, EnumFieldType.numberType, 0, 0, "", 0, "");
        Field[] fields2 = { t2Field0 };
        Table table2 = new Table(tableName2, join, fields2);

        Table[] tables = { table1 };
        return tables;
    }
    #endregion

    /// <summary>
    /// 页面加载
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void Page_Load(object sender, EventArgs e)
    {
        if (sqlCon == null)
        {
            sqlCon = new SqlConnection(ConnStr);
        }
        WebCustomControl1_1.PageSize = InputParams.pageSize;
        WebCustomControl1_1.ItemSize = InputParams.itemSize;
        WebCustomControl1_2.PageSize = InputParams.pageSize;
        WebCustomControl1_2.ItemSize = InputParams.itemSize;
		WebCustomControl1_3.PageSize = InputParams.pageSize;
		WebCustomControl1_3.ItemSize = InputParams.itemSize;

		GenerateSearchMould();
        btnDelete.Attributes.Add("onclick", "return confirm('确认删除?')");
        string searchStr = "";
        string sortExpression = "";
        string sortDirection = "";
        pagerIndex = 1;
        //ViewState["UserID"] = 1;
        HttpCookie cookie = Request.Cookies["user"];
        Session["supPage"] = "Default.aspx";

        //bool iFirmName = false; //标识当前是否为公司名称的字段,用于后面显示时用.

        if (cookie != null)//不用重新登录
        {
            lblWel.Text = cookie.Values["nickname"].ToString();
            Session["nickname"] = cookie.Values["nickname"].ToString();
            Session["user_id"] = cookie.Values["user_id"].ToString();//用户的ID
            Session["isGroupManager"] = cookie.Values["isGroupManager"].ToString();
            Session["group_id"] = cookie.Values["group_id"].ToString();


            GetUserApplyStatus();
            lblWel.Text = (new Common()).GetNickname(int.Parse(Session["user_id"].ToString()));

            #region if条件

            if (Request.QueryString["userID"] != null)
            {
                singleSearch[2] = Request.QueryString["userID"].ToString().Trim();
                ViewState["UserID"] = Request.QueryString["userID"].ToString().Trim();
                TextBox1.Text = (new Common()).UserID_to_Nickname(int.Parse(Request.QueryString["userID"].ToString().Trim()));

            }
            else
            {
                // singleSearch[2] = ViewState["UserID"].ToString();
            }
            if (Request.QueryString["message"] != null)
            {
                ClientScript.RegisterClientScriptBlock(this.GetType(), "info", "<script language=\"javascript\" type=\"text/javascript\">alert(\"添加成功！\");window.location.href='Default.aspx';</script>");

            }

            int iGroupManager = int.Parse(Session["isGroupManager"].ToString());
            if ((iGroupManager == 1 || iGroupManager == 2) && !isMobile())
            {

                lkbBrowseUser.Visible = true;
                hlk4.Visible = true;
                hlk5.Visible = true;
                lkbApplyMange.Visible = true;
                div_executor.Visible = true;
                pnlAdminTd.Visible = true;
                hideLinks.Visible = true;
                phAdminLinks.Visible = false;
                changePageButtons.Visible = false;

            }
            else if ((iGroupManager == 1 || iGroupManager == 2) && isMobile())
            {
                phAdminLinks.Visible = true;
                pnlAdminTd.Visible = false;
                hideLinks.Visible = true;
                changePageButtons.Visible = true;
            }
            else if(isMobile())
            {
                div_executor.Visible = false;
                phAdminLinks.Visible = false;
                pnlAdminTd.Visible = false;
                hideLinks.Visible = false;
                changePageButtons.Visible = true;
            }
            else
            {
                lkbBrowseUser.Visible = false;
                hlk4.Visible = false;
                hlk5.Visible = false;
                lkbApplyMange.Visible = false;
                div_executor.Visible = false;
                phAdminLinks.Visible = false;
                pnlAdminTd.Visible = false;
                hideLinks.Visible = false;
                changePageButtons.Visible = false;

            }
            //手机端适配显示管理员相关按钮
            //if ((iGroupManager == 1 || iGroupManager == 2) && isMobile())
            //{
            //    phAdminLinks.Visible = true;
            //    phAdminLinksDesk.Visible = false;
            //    hideLinks.Visible = true;
            //}
            //else {
            //    hideLinks.Visible = false;
            //    phAdminLinks.Visible = false;

            //}


                #endregion if条件

                if (Request.QueryString["id"] != null)
                if (Request.QueryString["id"].ToString() == "1")//管理员从查找组员的页面返回
                {

                    Session["sortExpression"] = "";
                    Session["sortDirection"] = "";

                    // id = 1;
                    GenerateSearchMould();

                    //查找返回的结果是两个数组，一个是用户ID,另一个是用户的昵称，用session传递
                    string[] tbxMemberNames = (string[])(Session["tbxMemberNames"]);

                    TextBox1.Text = "";
                    for (int iMemberIndex = 0; iMemberIndex < tbxMemberNames.Length; iMemberIndex++)
                    {
                        TextBox1.Text += tbxMemberNames[iMemberIndex].ToString() + " ";
                    }

                }


            if (!Page.IsPostBack)
            {
                searchStr = GetSearchStr();
                ViewState["searchStr"] = searchStr;
                ViewState["subSearchStr"] = subSearchStr;
                
            }

            if (ViewState["sortExpression"] != null)
            {
                sortExpression = ViewState["sortExpression"].ToString();
                sortDirection = ViewState["sortDirection"].ToString();
            }

            if (ViewState["searchStr"] != null)
            {
                searchStr = ViewState["searchStr"].ToString();
            }

            try
            {
                DataTable dt = GetSearchResultTable(searchStr, sortExpression, sortDirection);
                if ((ViewState["pagerIndex"] != null) && (pageCount >= int.Parse(ViewState["pagerIndex"].ToString())))
                {
                    pagerIndex = int.Parse(ViewState["pagerIndex"].ToString());
                }
                else
                {
                    pagerIndex = 1;

                }
                ViewState["pagerIndex"] = pagerIndex;
                ShowResult(dt, sortExpression, sortDirection);
            }
            catch (SqlException ex)
            {
                Response.Write(ex.ToString());
            }
        }
        else
        {
            Response.Redirect("Login.aspx");
        }


    }

    /// <summary>
    /// 分页事件处理
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void pager_Click(object sender, EventArgs e)
    {
        pagerIndex = WebCustomControl1_1.CurrentPageIndex;
        WebCustomControl1_2.CurrentPageIndex = pagerIndex;

        ViewState["pagerIndex"] = pagerIndex;

        string sortExpression = "";
        string sortDirection = "";
        string searchStr = ViewState["searchStr"].ToString();

        if (ViewState["sortExpression"] != null)
        {
            sortExpression = ViewState["sortExpression"].ToString();
            sortDirection = ViewState["sortDirection"].ToString();
        }
        GenerateSearchMould();
        btnDelete.Attributes.Add("onclick", "return confirm('确认删除?')");
        DataTable dt = GetSearchResultTable(searchStr, sortExpression, sortDirection);
        ShowResult(dt, sortExpression, sortDirection);
    }


    protected void pager_Click2(object sender, EventArgs e)
    {
        pagerIndex = WebCustomControl1_2.CurrentPageIndex;
        WebCustomControl1_1.CurrentPageIndex = pagerIndex;

        ViewState["pagerIndex"] = pagerIndex;

        string sortExpression = "";
        string sortDirection = "";
        string searchStr = ViewState["searchStr"].ToString();

        if (ViewState["sortExpression"] != null)
        {
            sortExpression = ViewState["sortExpression"].ToString();
            sortDirection = ViewState["sortDirection"].ToString();
        }
        GenerateSearchMould();
        btnDelete.Attributes.Add("onclick", "return confirm('确认删除?')");
        DataTable dt = GetSearchResultTable(searchStr, sortExpression, sortDirection);
        ShowResult(dt, sortExpression, sortDirection);
    }
    //首页
    protected void pager_Click3(object sender, EventArgs e)
    {
        pagerIndex = 1;
		WebCustomControl1_3.CurrentPageIndex = pagerIndex;
		//Console.WriteLine( (double)WebCustomControl1_2.TotalRecord / WebCustomControl1_2.PageSize);
		//WebCustomControl1_3.CurrentPageIndex = (int)Math.Ceiling((double)WebCustomControl1_2.TotalRecord / WebCustomControl1_2.PageSize);
  //      pagerIndex = WebCustomControl1_3.CurrentPageIndex;
        ViewState["pagerIndex"] = pagerIndex;

        string sortExpression = "";
        string sortDirection = "";
        string searchStr = ViewState["searchStr"].ToString();

        if (ViewState["sortExpression"] != null)
        {
            sortExpression = ViewState["sortExpression"].ToString();
            sortDirection = ViewState["sortDirection"].ToString();
        }
        GenerateSearchMould();
        btnDelete.Attributes.Add("onclick", "return confirm('确认删除?')");
        DataTable dt = GetSearchResultTable(searchStr, sortExpression, sortDirection);
        ShowResult(dt, sortExpression, sortDirection);
    }
    //尾页
    protected void pager_Click4(object sender, EventArgs e)
    {
        //Console.WriteLine( (double)WebCustomControl1_2.TotalRecord / WebCustomControl1_2.PageSize);
        WebCustomControl1_3.CurrentPageIndex = (int)Math.Ceiling((double)WebCustomControl1_2.TotalRecord / WebCustomControl1_2.PageSize);

        pagerIndex = WebCustomControl1_3.CurrentPageIndex;
        WebCustomControl1_1.CurrentPageIndex = pagerIndex;
        WebCustomControl1_2.CurrentPageIndex = pagerIndex;
        ViewState["pagerIndex"] = pagerIndex;

        string sortExpression = "";
        string sortDirection = "";
        string searchStr = ViewState["searchStr"].ToString();

        if (ViewState["sortExpression"] != null)
        {
            sortExpression = ViewState["sortExpression"].ToString();
            sortDirection = ViewState["sortDirection"].ToString();
        }
        GenerateSearchMould();
        btnDelete.Attributes.Add("onclick", "return confirm('确认删除?')");
        DataTable dt = GetSearchResultTable(searchStr, sortExpression, sortDirection);
        ShowResult(dt, sortExpression, sortDirection);
    }
    //上一页
    protected void pager_Click5(object sender, EventArgs e)
    {

        pagerIndex = ViewState["pagerIndex"] != null
            ? (int)ViewState["pagerIndex"]
            : 1;

        // 页码递减并确保不小于1
        pagerIndex = Math.Max(pagerIndex - 1, 1);
        WebCustomControl1_1.CurrentPageIndex = pagerIndex;
        WebCustomControl1_2.CurrentPageIndex = pagerIndex;
        WebCustomControl1_3.CurrentPageIndex = pagerIndex;
        ViewState["pagerIndex"] = pagerIndex;

        string sortExpression = "";
        string sortDirection = "";
        string searchStr = ViewState["searchStr"].ToString();

        if (ViewState["sortExpression"] != null)
        {
            sortExpression = ViewState["sortExpression"].ToString();
            sortDirection = ViewState["sortDirection"].ToString();
        }
        GenerateSearchMould();
        btnDelete.Attributes.Add("onclick", "return confirm('确认删除?')");
        DataTable dt = GetSearchResultTable(searchStr, sortExpression, sortDirection);
        ShowResult(dt, sortExpression, sortDirection);
    }
    //下一页
    protected void pager_Click6(object sender, EventArgs e)
    {

        // 计算总页数（与尾页逻辑一致）
        int totalPages = (int)Math.Ceiling(
            (double)WebCustomControl1_2.TotalRecord / WebCustomControl1_2.PageSize
        );

        // 从ViewState获取当前页码
        pagerIndex = ViewState["pagerIndex"] != null
            ? (int)ViewState["pagerIndex"]
            : 1;

        // 页码递增并确保不超过总页数
        pagerIndex = Math.Min(pagerIndex + 1, totalPages);
        WebCustomControl1_1.CurrentPageIndex = pagerIndex;
        WebCustomControl1_2.CurrentPageIndex = pagerIndex;
        WebCustomControl1_3.CurrentPageIndex = pagerIndex;
        ViewState["pagerIndex"] = pagerIndex;

        string sortExpression = "";
        string sortDirection = "";
        string searchStr = ViewState["searchStr"].ToString();

        if (ViewState["sortExpression"] != null)
        {
            sortExpression = ViewState["sortExpression"].ToString();
            sortDirection = ViewState["sortDirection"].ToString();
        }
        GenerateSearchMould();
        btnDelete.Attributes.Add("onclick", "return confirm('确认删除?')");
        DataTable dt = GetSearchResultTable(searchStr, sortExpression, sortDirection);
        ShowResult(dt, sortExpression, sortDirection);
    }


    /// <summary>
    /// 排序事件处理
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void lkb_Command(object sender, CommandEventArgs e)
    {
        string searchStr = ViewState["subSearchStr"].ToString();
        string sortExpression = e.CommandName;
        string sortDirection;

        if ((ViewState["sortExpression"] != null) && (ViewState["sortExpression"].ToString() == e.CommandName))
        {
            if ((ViewState["sortDirection"] != null) && (ViewState["sortDirection"].ToString() != "ASC"))
            {
                sortDirection = "ASC";
            }
            else
            {
                sortDirection = "DESC";
            }
        }
        else
        {
            sortDirection = "DESC";
            ViewState["sortExpression"] = e.CommandName;
        }

        ViewState["sortDirection"] = sortDirection;
        GenerateSearchMould();
        btnDelete.Attributes.Add("onclick", "return confirm('确认删除?')");
        DataTable dt = GetSearchResultTable(searchStr, sortExpression, sortDirection);
        ShowResult(dt, sortExpression, sortDirection);
    }

    /// <summary>
    /// 查找事件处理
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void btnSearch_Click(object sender, EventArgs e)
    {
        string searchStr = GetSearchStr();
        ViewState["searchStr"] = searchStr;
        ViewState["subSearchStr"] = subSearchStr;
        //pagerIndex = 1;

        string sortExpression = "";
        string sortDirection = "";

        GenerateSearchMould();
        btnDelete.Attributes.Add("onclick", "return confirm('确认删除?')");

        DataTable dt = GetSearchResultTable(searchStr, sortExpression, sortDirection);
        ShowResult(dt, sortExpression, sortDirection);
    }


    /// <summary>
    /// 删除事件处理
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void btnDelete_Click(object sender, EventArgs e)
    {
        int iGroupManager = int.Parse(Session["isGroupManager"].ToString());
        if (iGroupManager != 3)
        {
            Response.Write("<script>alert('没有删除权限');</script>");
            return;
        }
        try
        {
            SqlConnection conn = new SqlConnection(ConnStr);
            //和数据库建立连接
            if (conn.State.ToString() == "Closed")
            {
                conn.Open();
            }
            for (int i = 0; i < placeHolder2.Controls.Count; i++)
            {
                string str = this.placeHolder2.Controls[i].GetType().ToString();
                if (str == "System.Web.UI.WebControls.CheckBox")
                {
                    string strCkb = placeHolder2.Controls[i].ID.ToString();
                    if (((CheckBox)placeHolder2.FindControl(strCkb)).Checked)
                    {
                        string[] arrCkb = strCkb.Split(' ');
                        int RecordId = int.Parse(arrCkb[0]);
                        //string strDelete = " DELETE FROM  " + mainTable + "  WHERE " + pKey + "=" + RecordId;
                        //update by tcf 2010-3-1
                        string strDelete = " Update " + mainTable + " Set isDel = 1 Where " + pKey + "=" + RecordId;
                        //Response.Write(strDelete);
                        SqlCommand comm = new SqlCommand(strDelete, conn);
                        comm.ExecuteNonQuery();
                    }
                }
            }

            //释放数据库连接
            if (conn.State.ToString() == "Open")
            {
                conn.Close();
            }

            string sortExpression = "";
            string sortDirection = "";
            string searchStr = ViewState["searchStr"].ToString();

            if (ViewState["sortExpression"] != null)
            {
                sortExpression = ViewState["sortExpression"].ToString();
                sortDirection = ViewState["sortDirection"].ToString();
            }
            GenerateSearchMould();
            btnDelete.Attributes.Add("onclick", "return confirm('确认删除?')");
            DataTable dt = GetSearchResultTable(searchStr, sortExpression, sortDirection);

            if (pageCount > int.Parse(ViewState["pagerIndex"].ToString()) || pageCount == int.Parse(ViewState["pagerIndex"].ToString()))
            {
                pagerIndex = int.Parse(ViewState["pagerIndex"].ToString());

            }
            else
            {
                pagerIndex = int.Parse(ViewState["pagerIndex"].ToString()) - 1;
            }
            ViewState["pagerIndex"] = pagerIndex;
            WebCustomControl1_1.CurrentPageIndex = pagerIndex;
            WebCustomControl1_2.CurrentPageIndex = pagerIndex;
            ShowResult(dt, sortExpression, sortDirection);
            ClientScript.RegisterClientScriptBlock(this.GetType(), "info", "<script language=\"javascript\" type=\"text/javascript\">alert(\"删除成功！\");</script>");

        }
        catch (SqlException ex)
        {
            Response.Write("删除失败！");
            Response.Write(ex.ToString());
        }
    }


    /// <summary>
    /// 修改事件处理
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void btnModify_Click(object sender, EventArgs e)
    {
        int modifyCount = 0;
        for (int i = 0; i < placeHolder2.Controls.Count; i++)
        {
            string str = this.placeHolder2.Controls[i].GetType().ToString();
            if (str == "System.Web.UI.WebControls.CheckBox")
            {
                string strCkb = placeHolder2.Controls[i].ID.ToString();
                if (((CheckBox)placeHolder2.FindControl(strCkb)).Checked)
                {
                    modifyCount++;
                }
            }
        }

        int[] ckbsModify = new int[modifyCount];

        int modifyIndex = 0;
        for (int i = 0; i < placeHolder2.Controls.Count; i++)
        {
            string str = this.placeHolder2.Controls[i].GetType().ToString();
            if (str == "System.Web.UI.WebControls.CheckBox")
            {
                string strCkb = placeHolder2.Controls[i].ID.ToString();
                if (((CheckBox)placeHolder2.FindControl(strCkb)).Checked)
                {
                    string[] arrCkb = strCkb.Split(' ');
                    int RecordId = int.Parse(arrCkb[0]);
                    ckbsModify[modifyIndex] = RecordId;
                    modifyIndex++;
                }
            }
        }
        Session["ckbsModify"] = ckbsModify;
        //Response.Redirect("ModifyClients.aspx?");
        Response.Redirect("ModifyCIients.aspx?recordId=" + ckbsModify);
    }


    /// <summary>
    /// 创建任务
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void btnAdd_Click(object sender, EventArgs e)
    {
        Response.Redirect("ADD.aspx");
    }

    /// <summary>
    /// 汇报任务
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void btnReport_Click(object sender, EventArgs e)
    {
        int taskID = 0;
        try
        {
            for (int i = 0; i < placeHolder2.Controls.Count; i++)
            {
                string strType = this.placeHolder2.Controls[i].GetType().ToString();
                if (strType == "System.Web.UI.WebControls.CheckBox")
                {
                    string strCkb = placeHolder2.Controls[i].ID.ToString();
                    if (((CheckBox)placeHolder2.FindControl(strCkb)).Checked)
                    {
                        string[] arrCkb = strCkb.Split(' ');
                        taskID = int.Parse(arrCkb[0]);
                        break;
                    }
                }
            }
            string strSql = "SELECT TaskName FROM  " + mainTable + " WHERE isDel = 0 and " + pKey + "='" + taskID + "'";
            SqlConnection conn = new SqlConnection(ConnStr);
            conn.Open();
            SqlCommand comm = new SqlCommand(strSql, conn);
            SqlDataReader dr = comm.ExecuteReader();
            string strTaskName;

            if (dr.Read())
            {
                strTaskName = dr[0].ToString();
                dr.Close();
                conn.Close();

                string strDire = "Report.aspx?taskID=" + taskID + "&taskName=" + strTaskName;

                Response.Redirect(strDire);
            }
        }

        catch (SqlException ex)
        {
            Response.Write(ex.ToString());
            //ClientScript.RegisterClientScriptBlock(this.GetType(), "info", "<script language=\"javascript\" type=\"text/javascript\">alert(\"请选择要汇报的任务！\");</script>");

        }
    }


    /// <summary>
    /// 延迟结束，继续执行
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void btnDelay_Click(object sender, EventArgs e)
    {

        int delayCount = 0;
        for (int i = 0; i < placeHolder2.Controls.Count; i++)
        {
            string str = this.placeHolder2.Controls[i].GetType().ToString();
            if (str == "System.Web.UI.WebControls.CheckBox")
            {
                string strCkb = placeHolder2.Controls[i].ID.ToString();
                if (((CheckBox)placeHolder2.FindControl(strCkb)).Checked)
                {
                    delayCount++;
                }
            }
        }

        int[] ckbsDelay = new int[delayCount];

        int delayIndex = 0;
        for (int i = 0; i < placeHolder2.Controls.Count; i++)
        {
            string str = this.placeHolder2.Controls[i].GetType().ToString();
            if (str == "System.Web.UI.WebControls.CheckBox")
            {
                string strCkb = placeHolder2.Controls[i].ID.ToString();
                if (((CheckBox)placeHolder2.FindControl(strCkb)).Checked)
                {
                    string[] arrCkb = strCkb.Split(' ');
                    int RecordId = int.Parse(arrCkb[0]);
                    ckbsDelay[delayIndex] = RecordId;
                    delayIndex++;
                }
            }
        }
        Session["ckbsDelay"] = ckbsDelay;
        Response.Redirect("Delay.aspx");

    }


    /// <summary>
    /// 申请任务
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void btnApply_Click(object sender, EventArgs e)
    {

    }

    /// <summary>
    ///表格导出到Excel&Word
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    ///   
    public override void VerifyRenderingInServerForm(Control control)
    {

    }
    protected void btnWord_Click(object sender, EventArgs e)
    {
        Export("application/ms-word", "SearchResult.doc");

    }
    protected void btnExcel_Click(object sender, EventArgs e)
    {
        Export("application/ms-excel", "SearchResult.xls");

    }



    private void Export(string FileType, string FileName)
    {

    }


    /// <summary>
    /// 生成查找模板
    /// </summary>
    protected void GenerateSearchMould()
    {
        placeHolder1.Controls.Clear();
        EnumField[][] efTs = EnumField();
        Table[] tables = Tables();
        Label lblHr1 = new Label();
        lblHr1.Text = "<hr/>";
        placeHolder1.Controls.Add(lblHr1);

        //Label lblBr1 = new Label();
        //lblBr1.Text = "<br/>";
        //placeHolder1.Controls.Add(lblBr1);

        //计算查找字段总数
        fieldsCount = 0;
        btnDelete.Attributes.Add("onclick", "return confirm('确认删除?')");
        for (int tableIndex = 0; tableIndex < tables.Length; tableIndex++)
        {
            for (int fieldIndex = 0; fieldIndex < tables[tableIndex].fields.Length; fieldIndex++)
            {
                fieldsCount++;
            }
        }

        picLongchar = new int[fieldsCount];
        fieldTableShowLength = new int[fieldsCount];
        //Label lblHead = new Label();
        //lblHead.Text = " 设置查找条件：<br/> <br/>";
        //lblHead.ForeColor = System.Drawing.ColorTranslator.FromHtml(InputParams.lblHeadColor);
        //placeHolder1.Controls.Add(lblHead);

        int iLine = 0;
        int iPosition;

        #region forTable
        for (int tableIndex = 0; tableIndex < tables.Length; tableIndex++)
        {
            #region forField
            for (int fieldIndex = 0; fieldIndex < tables[tableIndex].fields.Length; fieldIndex++)
            {
                iPosition = tables[tableIndex].fields[fieldIndex].position;
                bool isCompanyName = (tables[tableIndex].fields[fieldIndex].fieldShowName == "公司名称");//用于隐藏不是公司名称的所有字段
                
                //生成换行符
                //if ((iLine != 0) && (iLine % lineLength) == 0)
                //{
                //    Label lblBrs = new Label();
                //    lblBrs.Text = "<br/>";
                //    placeHolder1.Controls.Add(lblBrs);
                //}
                if (isMobile())
                {
                    // 手机端逻辑：每行都换行
                    if (iLine != 0 /*&& (iLine % lineLength) == 0*/)
                    {
                        Label lblBrs = new Label();
                        lblBrs.Text = "<br/><br/>";
                        lblBrs.CssClass = isCompanyName ? "" : "additional-condition"; // 添加标识类
                        lblBrs.Style.Add("display", isCompanyName ? "inline" : "none"); // 默认隐藏
                        placeHolder1.Controls.Add(lblBrs);
                    }
                }
                else
                {
                    // 电脑端逻辑：每 lineLength 行换行
                    if ((iLine != 0) && (iLine % lineLength) == 0 && !isMobile())
                    {
                        Label lblBrs2 = new Label();
                        lblBrs2.Text = "<br/>";
                        lblBrs2.CssClass = isCompanyName ? "" : "additional-condition"; // 添加标识类
                        lblBrs2.Style.Add("display", isCompanyName ? "inline" : "none"); // 默认隐藏
                        placeHolder1.Controls.Add(lblBrs2);
                    }
                }
                iLine++;
                //生成空格
                if (iLine % lineLength == 0 && !isMobile())
                {
                    Label ltlSpaces = new Label();
                    ltlSpaces.Text = "&nbsp; &nbsp; &nbsp; ";
                    ltlSpaces.CssClass = isCompanyName ? "" : "additional-condition"; // 添加标识类
                    ltlSpaces.Style.Add("display", isCompanyName ? "inline" : "none"); // 默认隐藏
                    placeHolder1.Controls.Add(ltlSpaces);
                }

                //生成字段显示名称
                Label lbl = new Label();
                lbl.Text = tables[tableIndex].fields[fieldIndex].fieldShowName + "：";
                lbl.CssClass = isCompanyName ? "" : "additional-condition"; // 添加标识类
                lbl.Style.Add("display", isCompanyName ? "inline" : "none"); // 默认隐藏

                lbl.ForeColor = System.Drawing.ColorTranslator.FromHtml(InputParams.lblFieldShowNameColor);
                placeHolder1.Controls.Add(lbl);

                fieldTableShowLength[iLine - 1] = int.Parse(tables[tableIndex].fields[fieldIndex].fieldTableShowLength.ToString());
                //生成填写选择条件的文本框
                #region switch

                string strFldName = tables[tableIndex].fields[fieldIndex].fieldName;

                switch (tables[tableIndex].fields[fieldIndex].fieldType)
                {
                    case EnumFieldType.charType:
                        picLongchar[iPosition] = 11;
                        TextBox tbx1 = new TextBox();
                        tbx1.ID = "tbx" + iLine.ToString() + strFldName;
                        tbx1.ToolTip = "不同的关键字之间请以空格隔开！";
                        if (isCompanyName)
                        {
                            tbx1.Width = InputParams.tbxCompanyNameLength;
                        }
                        else
                        {
                            tbx1.Width = InputParams.tbxCharLength;
                        }
                        tbx1.Height = InputParams.tbxHeight;
                        tbx1.TextMode = TextBoxMode.MultiLine;
                        tbx1.Wrap = true;
                        tbx1.Style.Add("overflow", "hidden");
                        tbx1.CssClass = isCompanyName ? "" : "additional-condition"; // 添加标识类
                        tbx1.Style.Add("display", isCompanyName ? "inline" : "none"); // 默认隐藏

                        placeHolder1.Controls.Add(tbx1);
                        ((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString() + strFldName)).Attributes.Add("onMouseOver", "this.style.backgroundColor='"

+ InputParams.tbxOnmouseoverColor + "';");
                        ((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString() + strFldName)).Attributes.Add("onMouseOut", "this.style.backgroundColor='"

+ InputParams.tbxOnmouseoutColor + "';");
                        if (isCompanyName && isMobile())
                        {
                            //Button btnMore = new Button();
                            //btnMore.Text = "更多筛选条件";
                            LinkButton btnMore = new LinkButton();
                            btnMore.Text = "<span class='text'>更多筛选条件</span>";
                            //btnMore.Style.Add("overflow", "hidden");
                            btnMore.Attributes.Add("onclick", "toggleConditions(); return false;"); // 阻止回发
							btnMore.CssClass = "btn-more";
							placeHolder1.Controls.Add(btnMore);
						}
                        continue;

                    case EnumFieldType.longcharType:
                        picLongchar[iPosition] = 12;
                        TextBox tbx2 = new TextBox();
                        tbx2.ID = "tbx" + iLine.ToString() + strFldName;
                        tbx2.ToolTip = "请输入字段包含的文字，不同的关键字之间请以空格隔开！";
                        tbx2.Width = InputParams.tbxLongCharLength;
                        tbx2.Height = InputParams.tbxHeight;
                        tbx2.TextMode = TextBoxMode.MultiLine;
                        //tbx2.Wrap = true;
                        //tbx2.Style.Add("overflow", "hidden");
                        tbx2.CssClass = isCompanyName ? "" : "additional-condition"; // 添加标识类
                        tbx2.Style.Add("display", isCompanyName ? "inline" : "none"); // 默认隐藏
                        placeHolder1.Controls.Add(tbx2);

                        ((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString() + strFldName)).Attributes.Add("onMouseOver", "this.style.backgroundColor='"
                        
+ InputParams.tbxOnmouseoverColor + "';");
                        ((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString() + strFldName)).Attributes.Add("onMouseOut", "this.style.backgroundColor='"
                        
+ InputParams.tbxOnmouseoutColor + "';");

                        continue;

                    case EnumFieldType.numberType:
                        picLongchar[iPosition] = 13;
                        TextBox tbx3 = new TextBox();
                        tbx3.ID = "tbx" + iLine.ToString() + strFldName + "1";
                        tbx3.Width = InputParams.tbxNumberLength;
                        tbx3.Height = InputParams.tbxHeight;
                        tbx3.CssClass = isCompanyName ? "" : "additional-condition"; // 添加标识类
                        tbx3.Style.Add("display", isCompanyName ? "inline" : "none"); // 默认隐藏
                        Label lblDao1 = new Label();
                        lblDao1.Text = "到";
                        lblDao1.ForeColor = System.Drawing.ColorTranslator.FromHtml(InputParams.lblFieldShowNameColor);
                        lblDao1.CssClass = isCompanyName ? "" : "additional-condition"; // 添加标识类
                        lblDao1.Style.Add("display", isCompanyName ? "inline" : "none"); // 默认隐藏
                        TextBox tbx4 = new TextBox();
                        tbx4.ID = "tbx" + iLine.ToString() + strFldName + "2";
                        tbx4.Width = InputParams.tbxNumberLength;
                        tbx4.Height = InputParams.tbxHeight;
                        tbx4.CssClass = isCompanyName ? "" : "additional-condition"; // 添加标识类
                        tbx4.Style.Add("display", isCompanyName ? "inline" : "none"); // 默认隐藏
                        placeHolder1.Controls.Add(tbx3);
                        placeHolder1.Controls.Add(lblDao1);
                        placeHolder1.Controls.Add(tbx4);

                        ((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString() + strFldName + "1")).Attributes.Add("onMouseOver",

"this.style.backgroundColor='" + InputParams.tbxOnmouseoverColor + "';");
                        ((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString() + strFldName + "1")).Attributes.Add("onMouseOut",

"this.style.backgroundColor='" + InputParams.tbxOnmouseoutColor + "';");
                        ((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString() + strFldName + "2")).Attributes.Add("onMouseOver",

"this.style.backgroundColor='" + InputParams.tbxOnmouseoverColor + "';");
                        ((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString() + strFldName + "2")).Attributes.Add("onMouseOut",

"this.style.backgroundColor='" + InputParams.tbxOnmouseoutColor + "';");
                        continue;
                    case EnumFieldType.doubleType:
                        picLongchar[iPosition] = 19;
                        TextBox tbx13 = new TextBox();
                        tbx13.ID = "tbx" + iLine.ToString() + strFldName + "1";
                        tbx13.Width = InputParams.tbxNumberLength;
                        tbx13.Height = InputParams.tbxHeight;

                        Label lblDao11 = new Label();
                        lblDao11.Text = "到";
                        lblDao11.ForeColor = System.Drawing.ColorTranslator.FromHtml(InputParams.lblFieldShowNameColor);
                        lblDao11.CssClass = isCompanyName ? "" : "additional-condition"; // 添加标识类
                        lblDao11.Style.Add("display", isCompanyName ? "inline" : "none"); // 默认隐藏
                        TextBox tbx14 = new TextBox();
                        tbx14.ID = "tbx" + iLine.ToString() + strFldName + "2";
                        tbx14.Width = InputParams.tbxNumberLength;
                        tbx14.Height = InputParams.tbxHeight;
                        tbx14.CssClass = isCompanyName ? "" : "additional-condition"; // 添加标识类
                        tbx14.Style.Add("display", isCompanyName ? "inline" : "none"); // 默认隐藏
                        placeHolder1.Controls.Add(tbx13);
                        placeHolder1.Controls.Add(lblDao11);
                        placeHolder1.Controls.Add(tbx14);

                        ((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString() + strFldName + "1")).Attributes.Add("onMouseOver",

"this.style.backgroundColor='" + InputParams.tbxOnmouseoverColor + "';");
                        ((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString() + strFldName + "1")).Attributes.Add("onMouseOut",

"this.style.backgroundColor='" + InputParams.tbxOnmouseoutColor + "';");
                        ((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString() + strFldName + "2")).Attributes.Add("onMouseOver",

"this.style.backgroundColor='" + InputParams.tbxOnmouseoverColor + "';");
                        ((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString() + strFldName + "2")).Attributes.Add("onMouseOut",

"this.style.backgroundColor='" + InputParams.tbxOnmouseoutColor + "';");
                        continue;

                    case EnumFieldType.dateType:
                        picLongchar[iPosition] = 14;

                        //DropDownList ddlY1 = new DropDownList();
                        //ddlY1.ID = "ddl" + iLine.ToString() + strFldName + "Y1";
                        //ddlY1.Width = InputParams.ddlTimeWidth;
                        //ddlY1.CssClass = isCompanyName ? "" : "additional-condition"; // 添加标识类
                        //ddlY1.Style.Add("display", isCompanyName ? "inline" : "none"); // 默认隐藏
                        //for (int iM = 2000; iM <= 2099; iM++)
                        //{
                        //    ListItem li = new ListItem();
                        //    if (iM == 2000)
                        //    {
                        //        li.Text = "";
                        //    }
                        //    else
                        //    {
                        //        li.Text = iM.ToString();
                        //        li.Value = iM.ToString();
                        //    }
                        //    ddlY1.Items.Add(li);
                        //}
                        //placeHolder1.Controls.Add(ddlY1);

                        //日历选择框
						TextBox tbx5 = new TextBox();
						tbx5.ID = "tbx" + iLine.ToString() + strFldName + "1";
						tbx5.TextMode = TextBoxMode.Date;
						//tbx5.Text = new DateTime(1900, 1, 1).ToString("yyyy-MM-dd");
						tbx5.Width = InputParams.tbxDateLength;
						tbx5.Height = InputParams.tbxHeight;
						tbx5.CssClass = isCompanyName ? "" : "additional-condition"; // 添加标识类
						tbx5.Style.Add("display", isCompanyName ? "inline" : "none"); // 默认隐藏
						placeHolder1.Controls.Add(tbx5);

						if (iHM == 1)
                        {
                            //Literal ltlSpace1 = new Literal();
                            //ltlSpace1.Text = "&nbsp;";
                            //placeHolder1.Controls.Add(ltlSpace1);


                            Label lblSpace1 = new Label();
                            lblSpace1.Text = "&nbsp;";
                            lblSpace1.CssClass = isCompanyName ? "" : "additional-condition";
                            lblSpace1.Style.Add("display", isCompanyName ? "inline" : "none");
                            placeHolder1.Controls.Add(lblSpace1);

                            DropDownList ddlH1 = new DropDownList();
                            ddlH1.ID = "ddl" + iLine.ToString() + strFldName + "H1";
                            ddlH1.Width = InputParams.ddlTimeWidth;

                            ddlH1.CssClass = isCompanyName ? "" : "additional-condition"; // 添加标识类
                            ddlH1.Style.Add("display", isCompanyName ? "inline" : "none"); // 默认隐藏
                            for (int iH = 0; iH <= 24; iH++)
                            {
                                ListItem li = new ListItem();
                                if (iH == 0)
                                {
                                    li.Text = "";
                                }
                                else
                                {
                                    li.Text = iH.ToString();
                                    li.Value = iH.ToString();
                                }
                                ddlH1.Items.Add(li);
                            }
                            placeHolder1.Controls.Add(ddlH1);

                            Label lblH1 = new Label();
                            lblH1.Text = "时";
                            lblH1.CssClass = isCompanyName ? "" : "additional-condition"; // 添加标识类
                            lblH1.Style.Add("display", isCompanyName ? "inline" : "none"); // 默认隐藏
                            placeHolder1.Controls.Add(lblH1);

                            DropDownList ddlM1 = new DropDownList();
                            ddlM1.ID = "ddl" + iLine.ToString() + strFldName + "M1";
                            ddlM1.Width = InputParams.ddlTimeWidth;
                            ddlM1.CssClass = isCompanyName ? "" : "additional-condition"; // 添加标识类
                            ddlM1.Style.Add("display", isCompanyName ? "inline" : "none"); // 默认隐藏
                            for (int iM = 0; iM <= 60; iM++)
                            {
                                ListItem li = new ListItem();
                                if (iM == 0)
                                {
                                    li.Text = "";
                                }
                                else
                                {
                                    li.Text = iM.ToString();
                                    li.Value = iM.ToString();
                                }
                                ddlM1.Items.Add(li);
                            }
                            placeHolder1.Controls.Add(ddlM1);

                            Label lblM1 = new Label();
                            lblM1.Text = "分";
                            lblM1.CssClass = isCompanyName ? "" : "additional-condition"; // 添加标识类
                            lblM1.Style.Add("display", isCompanyName ? "inline" : "none"); // 默认隐藏
                            placeHolder1.Controls.Add(lblM1);

                            //Literal ltlSpace2 = new Literal();

                            //ltlSpace2.Text = "&nbsp;";


                            //placeHolder1.Controls.Add(ltlSpace2);
                            Label lblSpace2 = new Label();
                            lblSpace2.Text = "&nbsp;";
                            lblSpace2.CssClass = isCompanyName ? "" : "additional-condition";
                            lblSpace2.Style.Add("display", isCompanyName ? "inline" : "none");
                            placeHolder1.Controls.Add(lblSpace2);

                            Label lblDao2 = new Label();
                            lblDao2.Text = "到";
                            lblDao2.ForeColor = System.Drawing.ColorTranslator.FromHtml(InputParams.lblFieldShowNameColor);
                            lblDao2.CssClass = isCompanyName ? "" : "additional-condition"; // 添加标识类
                            lblDao2.Style.Add("display", isCompanyName ? "inline" : "none"); // 默认隐藏
							if (isMobile())
							{
                                Label liSpaces4 = new Label();
								liSpaces4.Text = "<br/><br/>";
                                liSpaces4.CssClass = isCompanyName ? "" : "additional-condition"; // 添加标识类
                                liSpaces4.Style.Add("display", isCompanyName ? "inline" : "none"); // 默认隐藏
                                placeHolder1.Controls.Add(liSpaces4);
							}
							placeHolder1.Controls.Add(lblDao2);
                            //Literal ltlSpace3 = new Literal();
                            //ltlSpace3.Text = "&nbsp;";
                            //placeHolder1.Controls.Add(ltlSpace3);
                            Label lblSpace3 = new Label();
                            lblSpace3.Text = "&nbsp;";
                            lblSpace3.CssClass = isCompanyName ? "" : "additional-condition";
                            lblSpace3.Style.Add("display", isCompanyName ? "inline" : "none");
                            placeHolder1.Controls.Add(lblSpace3);

                            TextBox tbx6 = new TextBox();
                            tbx6.ID = "tbx" + iLine.ToString() + strFldName + "2";
							tbx6.TextMode = TextBoxMode.Date;
							//tbx6.Text = DateTime.Now.ToString("yyyy-MM-dd");
							tbx6.Width = InputParams.tbxDateLength;
                            tbx6.Height = InputParams.tbxHeight;
                            //tbx6.Text = DateTime.Now.ToShortDateString();
                            tbx6.CssClass = isCompanyName ? "" : "additional-condition"; // 添加标识类
                            tbx6.Style.Add("display", isCompanyName ? "inline" : "none"); // 默认隐藏
                            placeHolder1.Controls.Add(tbx6);

                            //Literal ltSpace4 = new Literal();
                            //ltSpace4.Text = "&nbsp;";

                            //placeHolder1.Controls.Add(ltSpace4);
                            Label lblSpace4 = new Label();
                            lblSpace4.Text = "&nbsp;";
                            lblSpace4.CssClass = isCompanyName ? "" : "additional-condition";
                            lblSpace4.Style.Add("display", isCompanyName ? "inline" : "none");
                            placeHolder1.Controls.Add(lblSpace4);

                            DropDownList ddlH2 = new DropDownList();
                            ddlH2.ID = "ddl" + iLine.ToString() + strFldName + "H2";
                            ddlH2.Width = InputParams.ddlTimeWidth;

                            ddlH2.CssClass = isCompanyName ? "" : "additional-condition"; // 添加标识类
                            ddlH2.Style.Add("display", isCompanyName ? "inline" : "none"); // 默认隐藏
                            for (int iH = 0; iH <= 24; iH++)
                            {
                                ListItem li = new ListItem();
                                if (iH == 0)
                                {
                                    li.Text = "";
                                }
                                else
                                {
                                    li.Text = iH.ToString();
                                    li.Value = iH.ToString();
                                }
                                ddlH2.Items.Add(li);
                            }
                            placeHolder1.Controls.Add(ddlH2);

                            Label lblH2 = new Label();
                            lblH2.Text = "时";
                            lblH2.CssClass = isCompanyName ? "" : "additional-condition"; // 添加标识类
                            lblH2.Style.Add("display", isCompanyName ? "inline" : "none"); // 默认隐藏
                            placeHolder1.Controls.Add(lblH2);

                            DropDownList ddlM2 = new DropDownList();
                            ddlM2.ID = "ddl" + iLine.ToString() + strFldName + "M2";
                            ddlM2.Width = InputParams.ddlTimeWidth;

                            ddlM2.CssClass = isCompanyName ? "" : "additional-condition"; // 添加标识类
                            ddlM2.Style.Add("display", isCompanyName ? "inline" : "none"); // 默认隐藏
                            for (int iM = 0; iM <= 60; iM++)
                            {
                                ListItem li = new ListItem();
                                if (iM == 0)
                                {
                                    li.Text = "";
                                }
                                else
                                {
                                    li.Text = iM.ToString();
                                    li.Value = iM.ToString();
                                }
                                ddlM2.Items.Add(li);
                            }
                            placeHolder1.Controls.Add(ddlM2);

                            Label lblM2 = new Label();
                            lblM2.Text = "分";
                            lblM2.CssClass = isCompanyName ? "" : "additional-condition"; // 添加标识类
                            lblM2.Style.Add("display", isCompanyName ? "inline" : "none"); // 默认隐藏
                            placeHolder1.Controls.Add(lblM2);

                            //Literal ltlSpace5 = new Literal();
                            //ltlSpace5.Text = "&nbsp;";

                            //placeHolder1.Controls.Add(ltlSpace5);
                            Label lblSpace5 = new Label();
                            lblSpace5.Text = "&nbsp;";
                            lblSpace5.CssClass = isCompanyName ? "" : "additional-condition";
                            lblSpace5.Style.Add("display", isCompanyName ? "inline" : "none");
                            placeHolder1.Controls.Add(lblSpace5);
                        }
                        else
                        {
                            Label lblDao2 = new Label();
                            lblDao2.Text = "到";
                            lblDao2.ForeColor = System.Drawing.ColorTranslator.FromHtml(InputParams.lblFieldShowNameColor);
                            lblDao2.CssClass = isCompanyName ? "" : "additional-condition"; // 添加标识类
                            lblDao2.Style.Add("display", isCompanyName ? "inline" : "none"); // 默认隐藏
                            if (isMobile())
                            {
                                Literal lSpaces4 = new Literal();
                                lSpaces4.Text = "<br/><br/>";
                                placeHolder1.Controls.Add(lSpaces4);  
                            }
                            placeHolder1.Controls.Add(lblDao2);

                            TextBox tbx6 = new TextBox();
                            tbx6.ID = "tbx" + iLine.ToString() + strFldName + "2";
							tbx6.TextMode = TextBoxMode.Date;
							tbx6.Width = InputParams.tbxDateLength;
                            tbx6.Height = InputParams.tbxHeight;
                            
                            tbx6.CssClass = isCompanyName ? "" : "additional-condition"; // 添加标识类
                            tbx6.Style.Add("display", isCompanyName ? "inline" : "none"); // 默认隐藏
                            placeHolder1.Controls.Add(tbx6);


                        }

						((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString() + strFldName + "1")).Attributes.Add("onClick", "javascript:calendar()");
						((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString() + strFldName + "2")).Attributes.Add("onClick", "javascript:calendar()");
                        continue;



                    case EnumFieldType.boolType:
                        picLongchar[iPosition] = 15;
                        RadioButton rbY = new RadioButton();
                        RadioButton rbN = new RadioButton();

                        rbY.ID = "rb" + iLine.ToString() + strFldName + "1";
                        rbY.GroupName = "gn" + strFldName;
                        rbY.Text = "是";
                        if (tables[tableIndex].fields[fieldIndex].fieldName == "interviewee_sex")
                        {
                            rbY.Text = "男";
                        }
                        rbY.ForeColor = System.Drawing.ColorTranslator.FromHtml(InputParams.rbYNColor);
                        rbY.CssClass = isCompanyName ? "" : "additional-condition"; // 添加标识类
                        rbY.Style.Add("display", isCompanyName ? "inline" : "none"); // 默认隐藏
                        placeHolder1.Controls.Add(rbY);


                        rbN.ID = "rb" + iLine.ToString() + strFldName + "2";
                        rbN.GroupName = "gn" + strFldName;
                        rbN.Text = "否";
                        if (tables[tableIndex].fields[fieldIndex].fieldName == "interviewee_sex")
                        {
                            rbN.Text = "女";
                        }
                        rbN.ForeColor = System.Drawing.ColorTranslator.FromHtml(InputParams.rbYNColor);
                        rbN.CssClass = isCompanyName ? "" : "additional-condition"; // 添加标识类
                        rbN.Style.Add("display", isCompanyName ? "inline" : "none"); // 默认隐藏
                        placeHolder1.Controls.Add(rbN);

                        continue;

                    case EnumFieldType.picType:
                        picLongchar[iPosition] = 16;
                        TextBox tbx7 = new TextBox();
                        tbx7.ID = "tbx" + iLine.ToString() + strFldName;
                        tbx7.ToolTip = "请输入图片路径！";
                        tbx7.Width = InputParams.tbxCharLength;
                        tbx7.Height = InputParams.tbxHeight;
                        tbx7.TextMode = TextBoxMode.MultiLine;
                        tbx7.Wrap = true;
                        tbx7.Style.Add("overflow", "hidden");
                        tbx7.CssClass = isCompanyName ? "" : "additional-condition"; // 添加标识类
                        tbx7.Style.Add("display", isCompanyName ? "inline" : "none"); // 默认隐藏
                        placeHolder1.Controls.Add(tbx7);
                        ((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString() + strFldName)).Attributes.Add("onMouseOver", "this.style.backgroundColor='"

+ InputParams.tbxOnmouseoverColor + "';");
                        ((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString() + strFldName)).Attributes.Add("onMouseOut", "this.style.backgroundColor='"

+ InputParams.tbxOnmouseoutColor + "';");
                        continue;

                    case EnumFieldType.enumType:
                        picLongchar[iPosition] = tables[tableIndex].fields[fieldIndex].enumTag;
                        DropDownList ddl = new DropDownList();
                        ddl.ID = "ddl" + iLine.ToString() + strFldName;
                        int enumT = tables[tableIndex].fields[fieldIndex].enumTag;
                        ddl.Width = InputParams.ddlWidth;
                        ddl.AutoPostBack = false;
                        ddl.CssClass = isCompanyName ? "" : "additional-condition"; // 添加标识类
                        ddl.Style.Add("display", isCompanyName ? "inline" : "none"); // 默认隐藏
                        ListItem liTop = new ListItem();
                        liTop.Text = " 请选择 ";
                        ddl.Items.Add(liTop);

                        if (tables[tableIndex].fields[fieldIndex].fieldName == "area")
                        {
                            // SqlConnection conn1 = new SqlConnection(ConnStr);
                            if (sqlCon.State == ConnectionState.Closed)
                            {
                                sqlCon.Open();
                            }

                            SqlDataAdapter dap = new SqlDataAdapter("SELECT DISTINCT town_id,name  FROM ArTown", sqlCon);
                            DataTable dt = new DataTable();
                            dap.Fill(dt);
                            ddl.DataSource = dt;
                            ddl.DataTextField = "name";
                            ddl.DataValueField = "town_id";
                            ddl.DataBind();
                            ListItem li = new ListItem();
                            li.Text = "请选择";
                            li.Value = "请选择";
                            li.Selected = true;
                            ddl.Items.Add(li);

                        }
                        else
                        {
                            for (int enumLen = 0; enumLen < efTs[enumT].Length; enumLen++)
                            {
                                ListItem li = new ListItem();
                                li.Value = efTs[enumT][enumLen].enumItem.ToString();
                                li.Text = efTs[enumT][enumLen].itemDetail;
                                ddl.Items.Add(li);
                            }
                            enumT++;
                        }
                        placeHolder1.Controls.Add(ddl);
                        ((DropDownList)placeHolder1.FindControl("ddl" + iLine.ToString() + strFldName)).Attributes.Add("onMouseOver",

"this.style.backgroundColor='" + InputParams.tbxOnmouseoverColor + "';");
                        ((DropDownList)placeHolder1.FindControl("ddl" + iLine.ToString() + strFldName)).Attributes.Add("onMouseOut",

"this.style.backgroundColor='" + InputParams.tbxOnmouseoutColor + "';");
                        continue;
                }
                #endregion switch  
            }
            #endregion forField
        }
        #endregion  forTable
        longNum = 0;
        for (int i = 0; i < picLongchar.Length; i++)
        {
            if (picLongchar[i] == 12)
            {
                longNum++;
            }
        }
        rowSpan = longNum + 1;
        colSpan = fieldsCount - 1;
        // 生成“更多筛选条件”按钮
        if (!isMobile()) {
			placeHolder1.Controls.Add(new Label { Text = "<br/> <br/>" });

			Button btnMore = new Button();
			btnMore.Text = "更多筛选条件";
			btnMore.Attributes.Add("onclick", "toggleConditions(); return false;"); // 阻止回发
			btnMore.CssClass = "btn-more";
			placeHolder1.Controls.Add(btnMore);

			placeHolder1.Controls.Add(new Label { Text = "<br/> " });
		}


		// 添加JavaScript代码
		//LiteralControl script = new LiteralControl(@"
		//<script>
		//    function toggleConditions() {
		//        var elements = document.getElementsByClassName('additional-condition');
		//        for (var i = 0; i < elements.length; i++) {
		//            elements[i].style.display = (elements[i].style.display === 'none') ? 'inline' : 'none';
		//        }
		//        document.querySelector('.btn-more').textContent = 
		//            (elements[0].style.display === 'none') ? '更多筛选条件' : '收起筛选条件';
		//    }
		//</script>
		//");
		//placeHolder1.Controls.Add(script);
	}


    /// <summary>
    /// 获得查找字符串
    /// </summary>
    /// <returns></returns>
    protected string GetSearchStr()
    {
        EnumField[][] efTs = EnumField();
        Table[] tables = Tables();

        string strSearch;
        string strSelect = "SELECT DISTINCT ";
        string strFrom = " FROM ";
        string strWhere = " WHERE  1=1 and isDel = 0 ";
        string strOrderBy = "";
        int tag = 0;
        int iposition;

        int iGroupManager = int.Parse(Session["isGroupManager"].ToString());
        string groupId = Common.ToString(Session["group_id"]);
        string UserId = Request.Params["UserId"];
        if (string.IsNullOrEmpty(UserId))
        {
            UserId = (string)Session["user_id"];
        }

        if ((iGroupManager == 1 || iGroupManager == 2) && !string.IsNullOrEmpty(groupId)) // 企业管理员
        {
            //strWhere += " and ( group_id = " + groupId;
            strWhere += string.Format(" and (group_id = {0} or executor_id = {1})", groupId, UserId);
        }
        else if (!string.IsNullOrEmpty(UserId))
        {
            strWhere += "and executor_id=" + UserId.ToString();
        }

        //strWhere += "and executor_id=" + Session["user_id"].ToString();

        string id = Request.Params["id"];

        if (id != null)
        {
            if (int.Parse(id.ToString()) == 1)
            {
                string[] tbxMemberIDs = (string[])(Session["tbxMemberIDs"]);

                for (int i = 0; i < tbxMemberIDs.Length; i++)
                {
                    if (i == 0)
                        strWhere += " and (executor_id=" + tbxMemberIDs[i];
                    else
                        strWhere += " or executor_id=" + tbxMemberIDs[i];
                    if (i == tbxMemberIDs.Length - 1)
                        strWhere += ")";
                }

            }
        }

        string[] arrSearchStr = new string[fieldsCount];

        #region forTable
        int iLine = 0;
        for (int tableIndex = 0; tableIndex < tables.Length; tableIndex++)
        {
            string strTblName = tables[tableIndex].tableName;

            if (tableIndex == 0)
            {
                strFrom += strTblName;
            }
            else
            {
                strFrom += "," + strTblName;
            }
            if (tables[tableIndex].join.joinField != null)
            {
                strWhere += " AND " + strTblName + "." + tables[tableIndex].join.joinField + "=" + tables[tableIndex].join.joinRTable + "." + tables

[tableIndex].join.joinRField;
            }

            #region forField
            for (int fieldIndex = 0; fieldIndex < tables[tableIndex].fields.Length; fieldIndex++)
            {
                iLine++;
                iposition = tables[tableIndex].fields[fieldIndex].position;
                string strFldName = tables[tableIndex].fields[fieldIndex].fieldName;
                if (tables[tableIndex].fields[fieldIndex].sortType != EnumSortType.NoSort)
                {
                    if (tag == 0)
                    {
                        strOrderBy += " ORDER BY " + strTblName + "." + strFldName + " " + tables[tableIndex].fields[fieldIndex].sortType;
                        tag = 1;
                    }
                    else
                        if (tag == 1)
                    {
                        strOrderBy += " ," + strTblName + "." + strFldName + " " + tables[tableIndex].fields[fieldIndex].sortType;
                    }
                }

                if (iposition == 0)
                {
                    arrSearchStr[iposition] = " " + strTblName + "." + strFldName + " AS " + "'" + tables[tableIndex].fields[fieldIndex].fieldShowName + "'";
                }
                else
                {
                    arrSearchStr[iposition] = ",";
                    arrSearchStr[iposition] += " " + strTblName + "." + strFldName + " AS " + "'" + tables[tableIndex].fields[fieldIndex].fieldShowName + "'";
                }
                switch (tables[tableIndex].fields[fieldIndex].fieldType)
                {

                    case EnumFieldType.numberType:
                        if ((((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString() + strFldName + "1")).Text != "") || (((TextBox)

placeHolder1.FindControl("tbx" + iLine.ToString() + strFldName + "2")).Text != ""))
                        {
                            if (((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString() + strFldName + "1")).Text == "")
                            {

                                strWhere += " AND ";
                                strWhere += strTblName + "." + strFldName + " <= " + float.Parse(((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString() +

strFldName + "2")).Text);
                            }
                            else
                            {

                                if (((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString() + strFldName + "2")).Text == "")
                                {

                                    strWhere += " AND ";
                                    strWhere += strTblName + "." + strFldName + " >= " + float.Parse(((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString

() + strFldName + "1")).Text);
                                }
                                else
                                {
                                    strWhere += " AND ";
                                    strWhere += strTblName + "." + strFldName + " >= " + float.Parse(((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString

() + strFldName + "1")).Text);
                                    strWhere += " AND ";
                                    strWhere += strTblName + "." + strFldName + " <= " + float.Parse(((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString

() + strFldName + "2")).Text);
                                }
                            }
                        }
                        continue;
                    case EnumFieldType.doubleType:
                        if ((((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString() + strFldName + "1")).Text != "") || (((TextBox)

placeHolder1.FindControl("tbx" + iLine.ToString() + strFldName + "2")).Text != ""))
                        {
                            if (((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString() + strFldName + "1")).Text == "")
                            {

                                strWhere += " AND ";
                                strWhere += strTblName + "." + strFldName + " <= " + float.Parse(((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString() +

strFldName + "2")).Text);
                            }
                            else
                            {

                                if (((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString() + strFldName + "2")).Text == "")
                                {

                                    strWhere += " AND ";
                                    strWhere += strTblName + "." + strFldName + " >= " + float.Parse(((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString

() + strFldName + "1")).Text);
                                }
                                else
                                {
                                    strWhere += " AND ";
                                    strWhere += strTblName + "." + strFldName + " >= " + float.Parse(((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString

() + strFldName + "1")).Text);
                                    strWhere += " AND ";
                                    strWhere += strTblName + "." + strFldName + " <= " + float.Parse(((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString

() + strFldName + "2")).Text);
                                }
                            }
                        }
                        continue;

                    case EnumFieldType.dateType:
                        if ((((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString() + strFldName + "1")).Text != "") || (((TextBox)

placeHolder1.FindControl("tbx" + iLine.ToString() + strFldName + "2")).Text != ""))
                        {
                            DropDownList ddlH1 = (DropDownList)placeHolder1.FindControl("ddl" + iLine.ToString() + strFldName + "H1");
                            DropDownList ddlH2 = (DropDownList)placeHolder1.FindControl("ddl" + iLine.ToString() + strFldName + "H2");
                            DropDownList ddlM1 = (DropDownList)placeHolder1.FindControl("ddl" + iLine.ToString() + strFldName + "M1");
                            DropDownList ddlM2 = (DropDownList)placeHolder1.FindControl("ddl" + iLine.ToString() + strFldName + "M2");


                            int iH1 = 0;
                            int iH2 = 0;
                            int iM1 = 0;
                            int iM2 = 0;

                            if (iHM == 1)
                            {
                                if (ddlH1.Text != "")
                                {
                                    iH1 = int.Parse(ddlH1.Text.ToString());
                                }
                                if (ddlH2.Text != "")
                                {
                                    iH2 = int.Parse(ddlH2.Text.ToString());
                                }
                                if (ddlM1.Text != "")
                                {
                                    iM1 = int.Parse(ddlM1.Text.ToString());
                                }
                                if (ddlM2.Text != "")
                                {
                                    iM2 = int.Parse(ddlM2.Text.ToString());
                                }
                            }

                            string strDataTime1 = "";
                            string strDataTime2 = "";
                            if (((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString() + strFldName + "2")).Text == "")
                            {
                                strDataTime1 = ((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString() + strFldName + "1")).Text.ToString() + " " + iH1 +

":" + iM1 + ":" + "00";
                                strWhere += " AND ";
                                strWhere += strTblName + "." + strFldName + " >= " + "'" + DateTime.Parse(strDataTime1) + "'";
                            }
                            else
                                if (((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString() + strFldName + "1")).Text == "")
                            {
                                strDataTime2 = ((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString() + strFldName + "2")).Text.ToString() + " " + iH2

+ ":" + iM2 + ":" + "00";
                                strWhere += " AND ";
                                strWhere += strTblName + "." + strFldName + "<= " + "'" + DateTime.Parse(strDataTime2) + "'";
                            }
                            else
                            {
                                strDataTime1 = ((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString() + strFldName + "1")).Text.ToString() + " " + iH1

+ ":" + iM1 + ":" + "00";
                                strDataTime2 = ((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString() + strFldName + "2")).Text.ToString() + " " + iH2

+ ":" + iM2 + ":" + "00"; strWhere += " AND ";
                                strWhere += strTblName + "." + strFldName + " >= " + "'" + DateTime.Parse(strDataTime1) + "'";
                                strWhere += " AND ";
                                strWhere += strTblName + "." + strFldName + "<= " + "'" + DateTime.Parse(strDataTime2) + "'";
                            }


                        }
                        continue;

                    case EnumFieldType.enumType:
                        string strCho = "请选择";
                        string strDdl = ((DropDownList)placeHolder1.FindControl("ddl" + iLine.ToString() + strFldName)).Text.Trim();
                        if (strDdl != strCho.Trim())
                        {
                            int enumItem = int.Parse(strDdl);
                            strWhere += " AND ";
                            strWhere += strTblName + "." + strFldName + " =" + enumItem;
                        }
                        continue;

                    case EnumFieldType.boolType:

                        if (((RadioButton)placeHolder1.FindControl("rb" + iLine.ToString() + strFldName + "1")).Checked)
                        {
                            strWhere += " AND ";
                            strWhere += strTblName + "." + strFldName + " =1";
                        }
                        else
                            if (((RadioButton)placeHolder1.FindControl("rb" + iLine.ToString() + strFldName + "2")).Checked)
                        {
                            strWhere += " AND ";
                            strWhere += strTblName + "." + strFldName + " =0";
                        }
                        continue;

                    default:
                        if (((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString() + strFldName)).Text != "")
                        {
                            strWhere += " AND " + strTblName + "." + strFldName + " like" + "'%";
                            string str = ((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString() + strFldName)).Text;
                            string[] strs = str.Split(' ');
                            for (int i = 0; i < strs.Length; i++)
                            {
                                if (i != strs.Length - 1)
                                {
                                    strWhere += strs[i].ToString();
                                    strWhere += "%";
                                }
                                else
                                {
                                    strWhere += strs[i].ToString();
                                    strWhere += "%'";
                                }
                            }
                        }
                        continue;

                }
            }
            #endregion forField
        }
        #endregion forTable
        int arrSearchStrIndex;
        for (arrSearchStrIndex = 0; arrSearchStrIndex < arrSearchStr.Length; arrSearchStrIndex++)
        {
            strSelect += arrSearchStr[arrSearchStrIndex];
        }


        //strWhere += " AND " + singleSearch[0].Trim().ToString() + "." + singleSearch[1].Trim().ToString() + "=" + int.Parse(singleSearch[2].Trim().ToString());
        strSearch = strSelect + " ," + mainTable + "." + pKey + " " + strFrom + strWhere + strOrderBy;
        subSearchStr = strSelect + " ," + mainTable + "." + pKey + " " + strFrom + strWhere;
        //Response.Write(strSearch);
        return strSearch;
    }



    /// <summary>
    /// 生成查找结果表
    /// </summary>
    /// <param name="searchStr"></param>
    /// <param name="sortExpression"></param>
    /// <param name="sortDirection"></param>
    /// <returns></returns>
    protected DataTable GetSearchResultTable(string searchStr, string sortExpression, string sortDirection)
    {
        string connStr = ConnStr;
        SqlConnection conn = new SqlConnection(connStr);
        SqlDataAdapter da;
        if (conn.State.ToString() == "Closed")
        {
            conn.Open();
        }
        if (sortExpression != "")
        {
            searchStr = ViewState["subSearchStr"].ToString() + " ORDER BY " + sortExpression + " " + sortDirection;
        }
        //Response.Write(searchStr);
        da = new SqlDataAdapter(searchStr, connStr);

        DataTable dt = new DataTable();
        da.Fill(dt);

        recordCount = dt.Rows.Count;
        if (recordCount % InputParams.pageSize == 0)
        {
            pageCount = recordCount / InputParams.pageSize;
        }
        else
        {
            pageCount = (recordCount / InputParams.pageSize) + 1;
        }

        if (conn.State.ToString() == "Open")
        {
            conn.Close();
        }
        return dt;

	}


 //    <summary>
 //   将查找结果表输出
 //   </summary>
 //   <param name = "dt" ></ param >
//    < param name="sortExpression"></param>
 //   <param name = "sortDirection" ></ param >

	protected void ShowResult(DataTable dt, string sortExpression, string sortDirection)
	{
		placeHolder2.Controls.Clear();
		placeHolder2.EnableViewState = true;
		EnumField[][] efTs = EnumField();
		string strShowSpace = "&nbsp;";

        //生成表头strTop
        //string str1 = "<table id='tableBody'   border='1' style='background-color:" + InputParams.resultTableColor + "'>" + "<tr style='color:Blue'>" + "<td style='color:#871f78; width:" + InputParams.ckbLength + "'>";
        string str1= "";
        if (isMobile()) {
            str1 = "";
            //str1 = "<div class='table-container' ><table id='tableBody' style='WORD-BREAK: break-all;text-align:center;width:100%'>"/* + "<tr style='color:blue'>" *//* +"<td  style='width:2%'>"*/;
        }else 
            str1 = "<div class='table-container' ><table id='tableBody' border='1' style='WORD-BREAK: break-all;text-align:center;width:100%'>"/* + "<tr style='color:blue'>" *//* +"<td  style='width:2%'>"*/;

		Literal ltl1 = new Literal();
		ltl1.Text = str1;
		placeHolder2.Controls.Add(ltl1);

        if (!isMobile())
        {

        Literal ltla = new Literal();
        ltla.Text = "<tr style='color:blue'>";
        placeHolder2.Controls.Add(ltla);


        int iGM = int.Parse(Session["isGroupManager"].ToString());
		if ((iGM == 1 || iGM == 2)&& !isMobile()) {
            Literal ltl2 = new Literal();
            ltl2.Text = "<td  style='width:2%'><input id='CheckAll' type='checkbox' onclick='selectAll(this);'/><br />全选</td>";
            placeHolder2.Controls.Add(ltl2);
        }


		string lkbStr = "";
		for (int ColIndex = 0; ColIndex < fieldsCount; ColIndex++)
		{
			//Field f = tableFields[ColIndex];
			//if (!f.show) continue;
			if (picLongchar[ColIndex] != 12)
			{
				lkbStr = "<td style='font-size:15px; width:" + fieldTableShowLength[ColIndex] + "%";
				Literal ltl3 = new Literal();
				ltl3.Text = lkbStr + "'>";
				placeHolder2.Controls.Add(ltl3);

				LinkButton lkb = new LinkButton();
				lkb.CommandName = dt.Columns[ColIndex].Caption;

				lkb.Command += new CommandEventHandler(lkb_Command);
				lkb.ID = "lkb" + dt.Columns[ColIndex].Caption;
				placeHolder2.Controls.Add(lkb);

				if (dt.Columns[ColIndex].Caption == sortExpression)
				{
					if (sortDirection == "ASC")
					{
						lkb.Text = dt.Columns[ColIndex].Caption + "↑";
					}
					else
						if (sortDirection == "DESC")
					{
						lkb.Text = dt.Columns[ColIndex].Caption + "↓";
					}
				}
				else
				{
					lkb.Text = dt.Columns[ColIndex].Caption;
				}

				Literal ltl4 = new Literal();

				ltl4.Text = "</td>";
				placeHolder2.Controls.Add(ltl4);
			}
		}

		int iGroupManager = int.Parse(Session["isGroupManager"].ToString());
		if (iGroupManager == 1 || iGroupManager == 2)
		{
			Literal ltlExecutor = new Literal();
			ltlExecutor.Text = "<td>跟踪人</td>";
			placeHolder2.Controls.Add(ltlExecutor);

		}
		Literal ltl5 = new Literal();
		//ltl5.Text = "<td  style ='width :" + InputParams.lkbSearch + "px'>查看</td><td  style ='width :" + InputParams.lkbModify + "'>修改</td><td  style ='width:" + InputParams.lkbClose + "'>追加记录</td><td  style ='width :" + InputParams.lkbDelete + "'>删除</td></tr>";
		ltl5.Text = "<td  style ='white-space:nowrap;width:6%'>操作</td></tr>";
		placeHolder2.Controls.Add(ltl5);
        }

        //生成表的内容
        if (dt.Rows.Count != 0)
		{
			string strRowColor = "#FFFFFF";

			if (pagerIndex != pageCount)
			{
				for (int rdCount = 0; rdCount < InputParams.pageSize; rdCount++)
				{
					//Field f = tableFields[rdCount];
					//if (!f.show) continue;


					if (rdCount % 2 == 0)
					{
						strRowColor = InputParams.rowColor1;
					}

					else
					{
						strRowColor = InputParams.rowColor2;
					}

					Literal ltl11 = new Literal();

                    if (isMobile())
                    {
                        ltl11.Text = "";
                        //ltl11.Text = "<tr  style ='background-color :" + InputParams.rowColor3 + "'>";
                    }
                    else
                    {
                        ltl11.Text = "<tr  style ='background-color :" + strRowColor + "'>";
                    }

					placeHolder2.Controls.Add(ltl11);
                    int iGM2 = int.Parse(Session["isGroupManager"].ToString());
                    if ((iGM2 == 1 || iGM2 == 2) && !isMobile())
                    {
                        Literal ltl12 = new Literal();
                        ltl12.Text = "<td rowspan='" + rowSpan.ToString() + "'>";
                        placeHolder2.Controls.Add(ltl12);
                    }
                    CheckBox ckb = new CheckBox();
					ckb.ID = dt.Rows[(pagerIndex - 1) * InputParams.pageSize + rdCount][fieldsCount].ToString() + " " + rdCount;
					string strLinkID = ckb.ID;

                    int iGM3 = int.Parse(Session["isGroupManager"].ToString());
                    if ((iGM3 == 1 || iGM3 == 2) && !isMobile())
                    {
                        placeHolder2.Controls.Add(ckb);
                        Literal ltl13 = new Literal();
                        ltl13.Text = " </td>";
                        placeHolder2.Controls.Add(ltl13);
                    }

					AddRow(dt, rdCount, efTs, strLinkID);
				}
				Response.Write("<br/>");
			}
			else
			{
				for (int rdCount = 0; rdCount < recordCount - (pagerIndex - 1) * InputParams.pageSize; rdCount++)
				{
					//Field f = tableFields[rdCount];
					//if (!f.show) continue;
					if (rdCount % 2 == 0)
					{
						strRowColor = InputParams.rowColor1;
					}

					else
					{
						strRowColor = InputParams.rowColor2;
					}
					Literal ltl11 = new Literal();
                    if (isMobile())
                    {
                        ltl11.Text = "";
                        //ltl11.Text = "<tr  style ='background-color :" +InputParams.rowColor3+ "'>" ;
                    }
                    else {
                        ltl11.Text = "<tr  style ='background-color :" + strRowColor + "'>" /*+ "<td  rowspan='" + rowSpan.ToString() + "'>" + strShowSpace*/;
                    }
					placeHolder2.Controls.Add(ltl11);

                    //全选列
                    int iGM4 = int.Parse(Session["isGroupManager"].ToString());
                    if ((iGM4 == 1 || iGM4 == 2) && !isMobile())
                    {
                        Literal ltl12 = new Literal();
                        ltl12.Text = "<td  rowspan='" + rowSpan.ToString() + "'>" + strShowSpace;
                        placeHolder2.Controls.Add(ltl12);
                    }

                    CheckBox ckb = new CheckBox();
					ckb.ID = dt.Rows[(pagerIndex - 1) * InputParams.pageSize + rdCount][fieldsCount].ToString() + " " + rdCount;
					string strLinkID = ckb.ID;
                    Session["modifyId"] = strLinkID;
                     
                    int iGM5 = int.Parse(Session["isGroupManager"].ToString());
                    if ((iGM5 == 1 || iGM5 == 2) && !isMobile())
                    {
						placeHolder2.Controls.Add(ckb);
						Literal ltl13 = new Literal();
						ltl13.Text = " </td>";
						placeHolder2.Controls.Add(ltl13);
					}


                    AddRow(dt, rdCount, efTs, strLinkID);
				}
			}
            Literal ltl6 = new Literal();
            if (isMobile())
            {
                ltl6.Text = "</div>";
                placeHolder2.Controls.Add(ltl6);
            }
            else {
                ltl6.Text = "</table></div>";
                placeHolder2.Controls.Add(ltl6);
            }


			WebCustomControl1_1.TotalRecord = recordCount;
			WebCustomControl1_2.TotalRecord = recordCount;
			//WebCustomControl1_1.PageSize = InputParams.pageSize;
			//WebCustomControl1_1.ItemSize = InputParams.itemSize;
		}
	}



	/// <summary>
	/// 向页面输出的表格中添加一行
	/// </summary>
	/// <param name="dt"></param>
	/// <param name="rdCount"></param>
	/// <param name="efTs"></param>
	void AddRow(DataTable dt, int rdCount, EnumField[][] efTs, string strLinkID)
    {
        string strShowSpace = "&nbsp;";
        string strLine1 = "";
        string strLine2 = "";
        int flag = 0;
        string[] arrStrTaskID = strLinkID.Split(' ');
        int record_id = int.Parse(arrStrTaskID[0].ToString());

        string strRowColor = "#FFFFFF";
        if (rdCount % 2 == 0)
        {
            strRowColor = InputParams.rowColor1;
        }

        else
        {
            strRowColor = InputParams.rowColor2;
        }

        for (int ColIndex = 0; ColIndex < fieldsCount; ColIndex++)
        {
            
            if (isMobile() && (/*ColIndex == 6 || */ ColIndex == 7 || ColIndex == 8 || ColIndex == 9 || ColIndex == 10 || ColIndex == 11 || ColIndex == 12 || ColIndex == 13) ) 
                continue;
            
            switch (picLongchar[ColIndex])
            {
                case 11:

                    string strText = dt.Rows[(pagerIndex - 1) * InputParams.pageSize + rdCount][ColIndex].ToString().Trim();

                    Literal ltl11 = new Literal();
                    if (isMobile() && dt.Columns[ColIndex].ColumnName == "公司名称")
                    {
                        ltl11.Text = "<div style='border-radius: 16px; box-shadow: 0 4px 6px -1px rgba(0,0,0,0.1), 0 2px 4px -1px rgba(0,0,0,0.06); padding: 16px; background: white; margin-bottom: 16px;'>"+
                            "<div style='display: flex; justify-content: space-between; align-items: center;'>";
                        //ltl11.Text = "<tr  style ='background-color :" +InputParams.rowColor3+ "'>" ;
                    }
                    else
                    {
                        ltl11.Text = "";
                    }
                    placeHolder2.Controls.Add(ltl11);

                    if (strText.Length >= 100 && !isMobile())
                    {
                        strText = "style ='width:200'>" + strText.Substring(0, 95) + "...";
                    }
                    else if (strText.Length >= 12 && dt.Columns[ColIndex].ColumnName == "公司名称")
                    {
                        strText = ">" + strText.Substring(0, 10) + "...";
                    }
                    else if (ColIndex == 6 && isMobile() && strText.Length >= 56) {
                        strText = "</br><div style='font-size: 15px;/*margin: 0 2ch;*/text-align: left; '>" + strText.Substring(0, 56) + "...";
                    } else if (ColIndex == 6 && isMobile()) {
                        strText = "</br><div style='font-size: 15px;/*margin: 0 2ch;*/text-align: left; '>" + strText;
                    }
                    else
                    {
                        strText = ">" + strText;
                    }
                    if (dt.Columns[ColIndex].ColumnName == "公司名称" && isMobile())
                    {
                        strText = "style='font-size:15px;'> <a href='SeeDetail.aspx?TaskID=" + record_id + "'target='blank'><h2 style='color:black; font-weight: bold;font-size:18px;line-height: 1.75rem;'" + strText + "</h2></a>";
                    }
                    else if (dt.Columns[ColIndex].ColumnName == "公司名称" && !isMobile())
                    {
                        strText = "> <a href='SeeDetail.aspx?TaskID=" + record_id + "' target='blank'" + strText + "</a>";
                    }

                    if (ColIndex == 6 && isMobile())
                    {
                        strLine1 += strText + "</div></div>";
                    }
                    else if (isMobile())
                    {
                        strLine1 += "<div  " + strText + "</div>";
                    }
                    else {
                        strLine1 += "<td  " + strText + "</td>";
                    }

                    
                    //else if (ColIndex == 0 && isMobile()) {
                    //    strLine1 += "<td style='text-align: left;'> " + strText + "</br><span style='color:#6a6a6a;'> ";
                    //}
                    continue;
                case 12:
                    // strLine2 += "<tr style ='background-color :" + strRowColor + "'>";
                    //strLine2 += "<td style='height:" + InputParams.longCharShowHeight + "'  colspan='" + colSpan.ToString() + "'>" + dt.Columns[ColIndex].Caption + "：" + dt.Rows[(pagerIndex - 1) * InputParams.pageSize + rdCount][ColIndex] + "</td>";
                    strLine2 += "<td style='white-space: nowrap;height:" + InputParams.longCharShowHeight + "'  colspan='" + colSpan.ToString() + "'>" + dt.Columns

[ColIndex].Caption + "：" + dt.Rows[(pagerIndex - 1) * InputParams.pageSize + rdCount][ColIndex] + strShowSpace + "</td>";
                    continue;

                case 13:
                    if (ColIndex == 3 && isMobile())
                    {
                        strLine1 +=  "  跟踪："+ "<span style='color:#6a6a6a;font-weight: bold;font-size=13px;'>" + dt.Rows[(pagerIndex - 1) * InputParams.pageSize + rdCount][ColIndex] + "</span>次";
                    }
                    else {
                        strLine1 += "<td>" + dt.Rows[(pagerIndex - 1) * InputParams.pageSize + rdCount][ColIndex] + "</td>";
                    }
                    continue;
                case 19:                                                                                          //浮点型
                    strLine1 += "<td>" + dt.Rows[(pagerIndex - 1) * InputParams.pageSize + rdCount][ColIndex] + "</td>";
                    continue;

                case 14:
                    DateTime dt2;

                    string result = "";
                    bool res = DateTime.TryParse(dt.Rows[(pagerIndex - 1) * InputParams.pageSize + rdCount][ColIndex].ToString(), out dt2);

                    if (res)
                    {
                        result = FormatDateTime(dt2);
                    }
                    else {
                        dt2 = DateTime.MinValue; // 默认值
                    }

                    if (ColIndex == 2 && isMobile())
                    {
                        strLine1 += "<div  style='color:#6a6a6a;text-align: left;font-size: 13px;'>" + " 添加:" + dt2.Date.ToShortDateString()
                                        + " " + dt2.Hour.ToString() + ":" + dt2.Minute.ToString() ;
                    }
                    else if (ColIndex == 4 && isMobile() && res)
                    {
                        //Literal ltr14 = new Literal();
                        //ltr14.Text = "</br>";
                        //placeHolder2.Controls.Add(ltr14);
                        strLine1 += "<div style='display: flex; justify-content: space-between; align-items: center;'><span>" + "上次联系：" + result + "</span>";
                    }
                    else if (ColIndex == 4 && isMobile() && !res)
                    {
                        strLine1 += "<div style='display: flex; justify-content: space-between; align-items: center;'><span>" + "上次联系：" + result + "</span>";
                    }
                    else if (ColIndex == 5 && isMobile())
                    {
                        strLine1 += "<span>下次联系：" + result + "</span></div>";
                    }
                    else if (ColIndex == 5 && isMobile() && !res)
                    {
                        strLine1 += "<span>" + "下次联系：" + "-         " + "</span></div>";
                    }
                    else
                    {
                        strLine1 += "<td style='white-space: nowrap;' >" + dt2.Date.ToShortDateString() + " " + dt2.Hour.ToString() + ":" + dt2.Minute.ToString() + "</td>";
                    }
					
                        //DateTime dt2 = DateTime.Parse(dt.Rows[(pagerIndex - 1) * InputParams.pageSize + rdCount][ColIndex].ToString());
                        //strLine1 += "<td style='white-space: nowrap;' >" + dt2.Date.ToShortDateString() + " " + dt2.Hour.ToString() + ":" + dt2.Minute.ToString() + "</td>";
                    continue;
                case 15:

                    string text = "";
                    if (flag != 2)
                    {
                        if (int.Parse(dt.Rows[(pagerIndex - 1) * InputParams.pageSize + rdCount][ColIndex].ToString()) == 1)
                            text = "是";
                        else
                            text = "否";
                        flag++;

                    }
                    else
                    {
                        if (int.Parse(dt.Rows[(pagerIndex - 1) * InputParams.pageSize + rdCount][ColIndex].ToString()) == 1)
                            text = "男";
                        else
                            text = "女";
                        flag = 1;
                    }

                    strLine1 += "<td>" + text + "</td>";
                    continue;
                case 16:
                    strLine1 += "<td><img  alt=''  height='" + InputParams.picTableHeight + "'  src='" + dt.Rows[(pagerIndex - 1) * InputParams.pageSize +

rdCount][ColIndex] + "'/></td>";
                    continue;

                default:
                    string ss1 = dt.Rows[(pagerIndex - 1) * InputParams.pageSize + rdCount][ColIndex].ToString();


                    int enumItem = int.Parse(dt.Rows[(pagerIndex - 1) * InputParams.pageSize + rdCount][ColIndex].ToString());
                    int enumT = picLongchar[ColIndex];
                    int enumLen;
                    for (enumLen = 0; enumLen < efTs[enumT].Length; enumLen++)
                    {
                        if (efTs[enumT][enumLen].enumItem == enumItem)
                        {
                            break;
                        }
                    }
                    if (enumLen == efTs[enumT].Length)
                    {
                        enumLen = enumLen - 1;
                    }
                    

                    if (ColIndex == 1 && !isMobile())
                    {
                        strLine1 += "<td>" + (new Common()).GetTown(int.Parse(ss1)) + "</td>";
                    }
                    else if (ColIndex == 1 && isMobile())
                    {
                        strLine1 += "<span style='color:#008B45'>" + efTs[enumT][enumLen].itemDetail + "</span></div>";
                    }
                    else if (ColIndex == 2 && isMobile()) {
                        strLine1 +=  efTs[enumT][enumLen].itemDetail;
                    }
                    else
                    {
                        strLine1 += "<td style='white-space:nowrap'>" + efTs[enumT][enumLen].itemDetail + "</td>";
                    }
                    continue;
            }
        }

        Literal ltl13 = new Literal();
        ltl13.Text = strLine1 + "" + strLine2;
        placeHolder2.Controls.Add(ltl13);

        

        int iGroupManager = int.Parse(Session["isGroupManager"].ToString());
        if (iGroupManager == 1 || iGroupManager == 2)
        {

            //string[] arrStrTaskID = strLinkID.Split(' ');
            //int record_id = int.Parse(arrStrTaskID[0].ToString());


            Literal ltlExecutor = new Literal();
            ltlExecutor.Text = "<td> " + (new Common()).GetUserName(record_id) + "</td>";
            placeHolder2.Controls.Add(ltlExecutor);

        }
        if (!isMobile()) {
            Literal ltl131 = new Literal();
            //ltl131.Text = "<td  rowspan='" + rowSpan.ToString() + "'>";
            ltl131.Text = "<td>";
            placeHolder2.Controls.Add(ltl131);
        }
        //获取记录的ID
        #region 获取记录的ID
        string[] arrStrTaskID_See = strLinkID.Split(' ');
        int taskID = int.Parse(arrStrTaskID_See[0].ToString());
        Session["pagerIndex"] = ViewState["pagerIndex"].ToString();
        Session["searchStr"] = ViewState["searchStr"].ToString();
		#endregion 获取记录的ID

		//HyperLink lkbSee = new HyperLink();
		//lkbSee.ID = "lkbSee " + strLinkID;
		//lkbSee.Text = "查看";
		//lkbSee.Target = "_blank";
		//lkbSee.NavigateUrl = "SeeDetail.aspx?TaskID=" + taskID;


		//placeHolder2.Controls.Add(lkbSee);

		//Literal ltlSee = new Literal();
		//ltlSee.Text = "</td><td  rowspan='" + rowSpan.ToString() + "'>";
		//placeHolder2.Controls.Add(ltlSee);

		//HyperLink lkbModify = new HyperLink();
		//lkbModify.ID = "lkbModify " + strLinkID;
		//lkbModify.Text = "修改";
		//lkbModify.Target = "_blank";
		//lkbModify.NavigateUrl = "Modify.aspx?recordId=" + taskID;
		//placeHolder2.Controls.Add(lkbModify);

		LinkButton lkbModify = new LinkButton();
		lkbModify.ID = "lkbModify " + strLinkID;
		lkbModify.Text = "修改";
		lkbModify.Command += new CommandEventHandler(lkbModify_Command);
		lkbModify.CommandArgument = strLinkID;
        if (!isMobile()) {
            placeHolder2.Controls.Add(lkbModify);
        }

        if (!isMobile())
        {
            Literal ltl14 = new Literal();
            //ltl14.Text = "</td><td  rowspan='" + rowSpan.ToString() + "'>";
            ltl14.Text = "<span><br />";
            placeHolder2.Controls.Add(ltl14);
        }
		HyperLink lkbReport = new HyperLink();
		lkbReport.ID = "lkbReport " + strLinkID;
		lkbReport.Text = "追加记录";
		//lkbReport.Command += new CommandEventHandler(lkbReport_Command);
		//lkbReport.CommandArgument = strLinkID;
		lkbReport.Target = "_blank";
		lkbReport.NavigateUrl = "addRecord.aspx?id=" + taskID;
        if (!isMobile())
        {
            placeHolder2.Controls.Add(lkbReport);
        }
        //Literal ltl15 = new Literal();
        //ltl15.Text = "</td><td  rowspan='" + rowSpan.ToString() + "'>";
        //placeHolder2.Controls.Add(ltl15);

        //LinkButton lkbDelete = new LinkButton();
        //lkbDelete.ID = "lkbDelete " + strLinkID;
        //lkbDelete.Text = "删除";
        //lkbDelete.Command += new CommandEventHandler(lkbDelete_Command);
        //lkbDelete.CommandArgument = strLinkID;
        //placeHolder2.Controls.Add(lkbDelete);
        //((LinkButton)placeHolder2.FindControl("lkbDelete " + strLinkID)).Attributes.Add("onclick", "return confirm('确认删除?')");
        if (!isMobile())
        {
            Literal ltl134 = new Literal();
            ltl134.Text = "</span></td></tr>";
            placeHolder2.Controls.Add(ltl134);
        }
        else  {
            Literal ltl135 = new Literal();
            ltl135.Text = "</div>";
            placeHolder2.Controls.Add(ltl135);
        }


    }

    //    <summary>
    //   将查找结果表输出
    //   </summary>
    //   <param name = "dt" ></ param >
    //    < param name="sortExpression"></param>
    //   <param name = "sortDirection" ></ param >


    //    protected void ShowResult(DataTable dt, string sortExpression, string sortDirection)
    //    {
    //        placeHolder2.Controls.Clear();
    //        placeHolder2.EnableViewState = true;
    //        EnumField[][] efTs = EnumField();
    //        string strShowSpace = "&nbsp;";

    //        //生成表头strTop
    //        //string str1 = "<table id='tableBody'   border='1' style='background-color:" + InputParams.resultTableColor + "'>" + "<tr style='color:Blue'>" + "<td style='color:#871f78; width:" + InputParams.ckbLength + "'>";
    //        string str1 = "";
    //        if (isMobile())
    //        {
    //            str1 = "<div class='table-container' ><table id='tableBody' style='WORD-BREAK: break-all;text-align:center;width:100%'>"/* + "<tr style='color:blue'>" *//* +"<td  style='width:2%'>"*/;
    //        }
    //        else
    //            str1 = "<div class='table-container' ><table id='tableBody' border='1' style='WORD-BREAK: break-all;text-align:center;width:100%'>"/* + "<tr style='color:blue'>" *//* +"<td  style='width:2%'>"*/;

    //        Literal ltl1 = new Literal();
    //        ltl1.Text = str1;
    //        placeHolder2.Controls.Add(ltl1);

    //        if (!isMobile())
    //        {

    //            Literal ltla = new Literal();
    //            ltla.Text = "<tr style='color:blue'>";
    //            placeHolder2.Controls.Add(ltla);


    //            int iGM = int.Parse(Session["isGroupManager"].ToString());
    //            if ((iGM == 1 || iGM == 2) && !isMobile())
    //            {
    //                Literal ltl2 = new Literal();
    //                ltl2.Text = "<td  style='width:2%'><input id='CheckAll' type='checkbox' onclick='selectAll(this);'/><br />全选</td>";
    //                placeHolder2.Controls.Add(ltl2);
    //            }


    //            string lkbStr = "";
    //            for (int ColIndex = 0; ColIndex < fieldsCount; ColIndex++)
    //            {
    //                //Field f = tableFields[ColIndex];
    //                //if (!f.show) continue;
    //                if (picLongchar[ColIndex] != 12)
    //                {
    //                    lkbStr = "<td style='font-size:15px; width:" + fieldTableShowLength[ColIndex] + "%";
    //                    Literal ltl3 = new Literal();
    //                    ltl3.Text = lkbStr + "'>";
    //                    placeHolder2.Controls.Add(ltl3);

    //                    LinkButton lkb = new LinkButton();
    //                    lkb.CommandName = dt.Columns[ColIndex].Caption;

    //                    lkb.Command += new CommandEventHandler(lkb_Command);
    //                    lkb.ID = "lkb" + dt.Columns[ColIndex].Caption;
    //                    placeHolder2.Controls.Add(lkb);

    //                    if (dt.Columns[ColIndex].Caption == sortExpression)
    //                    {
    //                        if (sortDirection == "ASC")
    //                        {
    //                            lkb.Text = dt.Columns[ColIndex].Caption + "↑";
    //                        }
    //                        else
    //                            if (sortDirection == "DESC")
    //                        {
    //                            lkb.Text = dt.Columns[ColIndex].Caption + "↓";
    //                        }
    //                    }
    //                    else
    //                    {
    //                        lkb.Text = dt.Columns[ColIndex].Caption;
    //                    }

    //                    Literal ltl4 = new Literal();

    //                    ltl4.Text = "</td>";
    //                    placeHolder2.Controls.Add(ltl4);
    //                }
    //            }

    //            int iGroupManager = int.Parse(Session["isGroupManager"].ToString());
    //            if (iGroupManager == 1 || iGroupManager == 2)
    //            {
    //                Literal ltlExecutor = new Literal();
    //                ltlExecutor.Text = "<td>跟踪人</td>";
    //                placeHolder2.Controls.Add(ltlExecutor);

    //            }
    //            Literal ltl5 = new Literal();
    //            //ltl5.Text = "<td  style ='width :" + InputParams.lkbSearch + "px'>查看</td><td  style ='width :" + InputParams.lkbModify + "'>修改</td><td  style ='width:" + InputParams.lkbClose + "'>追加记录</td><td  style ='width :" + InputParams.lkbDelete + "'>删除</td></tr>";
    //            ltl5.Text = "<td  style ='white-space:nowrap;width:6%'>操作</td></tr>";
    //            placeHolder2.Controls.Add(ltl5);
    //        }

    //        //生成表的内容
    //        if (dt.Rows.Count != 0)
    //        {
    //            string strRowColor = "#FFFFFF";

    //            if (pagerIndex != pageCount)
    //            {
    //                for (int rdCount = 0; rdCount < InputParams.pageSize; rdCount++)
    //                {
    //                    //Field f = tableFields[rdCount];
    //                    //if (!f.show) continue;


    //                    if (rdCount % 2 == 0)
    //                    {
    //                        strRowColor = InputParams.rowColor1;
    //                    }

    //                    else
    //                    {
    //                        strRowColor = InputParams.rowColor2;
    //                    }

    //                    Literal ltl11 = new Literal();

    //                    if (isMobile())
    //                    {
    //                        ltl11.Text = "<tr  style ='background-color :" + InputParams.rowColor3 + "'>";
    //                    }
    //                    else
    //                    {
    //                        ltl11.Text = "<tr  style ='background-color :" + strRowColor + "'>";
    //                    }

    //                    placeHolder2.Controls.Add(ltl11);
    //                    int iGM2 = int.Parse(Session["isGroupManager"].ToString());
    //                    if ((iGM2 == 1 || iGM2 == 2) && !isMobile())
    //                    {
    //                        Literal ltl12 = new Literal();
    //                        ltl12.Text = "<td rowspan='" + rowSpan.ToString() + "'>";
    //                        placeHolder2.Controls.Add(ltl12);
    //                    }
    //                    CheckBox ckb = new CheckBox();
    //                    ckb.ID = dt.Rows[(pagerIndex - 1) * InputParams.pageSize + rdCount][fieldsCount].ToString() + " " + rdCount;
    //                    string strLinkID = ckb.ID;

    //                    int iGM3 = int.Parse(Session["isGroupManager"].ToString());
    //                    if ((iGM3 == 1 || iGM3 == 2) && !isMobile())
    //                    {
    //                        placeHolder2.Controls.Add(ckb);
    //                        Literal ltl13 = new Literal();
    //                        ltl13.Text = " </td>";
    //                        placeHolder2.Controls.Add(ltl13);
    //                    }

    //                    AddRow(dt, rdCount, efTs, strLinkID);
    //                }
    //                Response.Write("<br/>");
    //            }
    //            else
    //            {
    //                for (int rdCount = 0; rdCount < recordCount - (pagerIndex - 1) * InputParams.pageSize; rdCount++)
    //                {
    //                    //Field f = tableFields[rdCount];
    //                    //if (!f.show) continue;

    //                    if (rdCount % 2 == 0)
    //                    {
    //                        strRowColor = InputParams.rowColor1;
    //                    }

    //                    else
    //                    {
    //                        strRowColor = InputParams.rowColor2;
    //                    }
    //                    Literal ltl11 = new Literal();
    //                    if (isMobile())
    //                    {
    //                        ltl11.Text = "<tr  style ='background-color :" + InputParams.rowColor3 + "'>";
    //                    }
    //                    else
    //                    {
    //                        ltl11.Text = "<tr  style ='background-color :" + strRowColor + "'>" /*+ "<td  rowspan='" + rowSpan.ToString() + "'>" + strShowSpace*/;
    //                    }
    //                    placeHolder2.Controls.Add(ltl11);

    //                    //全选列
    //                    int iGM4 = int.Parse(Session["isGroupManager"].ToString());
    //                    if ((iGM4 == 1 || iGM4 == 2) && !isMobile())
    //                    {
    //                        Literal ltl12 = new Literal();
    //                        ltl12.Text = "<td  rowspan='" + rowSpan.ToString() + "'>" + strShowSpace;
    //                        placeHolder2.Controls.Add(ltl12);
    //                    }

    //                    CheckBox ckb = new CheckBox();
    //                    ckb.ID = dt.Rows[(pagerIndex - 1) * InputParams.pageSize + rdCount][fieldsCount].ToString() + " " + rdCount;
    //                    string strLinkID = ckb.ID;
    //                    Session["modifyId"] = strLinkID;

    //                    int iGM5 = int.Parse(Session["isGroupManager"].ToString());
    //                    if ((iGM5 == 1 || iGM5 == 2) && !isMobile())
    //                    {
    //                        placeHolder2.Controls.Add(ckb);
    //                        Literal ltl13 = new Literal();
    //                        ltl13.Text = " </td>";
    //                        placeHolder2.Controls.Add(ltl13);
    //                    }


    //                    AddRow(dt, rdCount, efTs, strLinkID);
    //                }
    //            }

    //            Literal ltl6 = new Literal();
    //            ltl6.Text = "</table></div>";
    //            placeHolder2.Controls.Add(ltl6);

    //            WebCustomControl1_1.TotalRecord = recordCount;
    //            WebCustomControl1_2.TotalRecord = recordCount;
    //            //WebCustomControl1_1.PageSize = InputParams.pageSize;
    //            //WebCustomControl1_1.ItemSize = InputParams.itemSize;
    //        }
    //    }



    //    /// <summary>
    //    /// 向页面输出的表格中添加一行
    //    /// </summary>
    //    /// <param name="dt"></param>
    //    /// <param name="rdCount"></param>
    //    /// <param name="efTs"></param>
    //    void AddRow(DataTable dt, int rdCount, EnumField[][] efTs, string strLinkID)
    //    {
    //        string strShowSpace = "&nbsp;";
    //        string strLine1 = "";
    //        string strLine2 = "";
    //        int flag = 0;
    //        string[] arrStrTaskID = strLinkID.Split(' ');
    //        int record_id = int.Parse(arrStrTaskID[0].ToString());

    //        string strRowColor = "#FFFFFF";
    //        if (rdCount % 2 == 0)
    //        {
    //            strRowColor = InputParams.rowColor1;
    //        }

    //        else
    //        {
    //            strRowColor = InputParams.rowColor2;
    //        }

    //        for (int ColIndex = 0; ColIndex < fieldsCount; ColIndex++)
    //        {

    //            if (isMobile() && (/*ColIndex == 6 || */ ColIndex == 8 || ColIndex == 9 || ColIndex == 10 || ColIndex == 11 || ColIndex == 12 || ColIndex == 13))
    //                continue;

    //            switch (picLongchar[ColIndex])
    //            {
    //                case 11:

    //                    string strText = dt.Rows[(pagerIndex - 1) * InputParams.pageSize + rdCount][ColIndex].ToString().Trim();
    //                    if (strText.Length >= 100 && !isMobile())
    //                    {
    //                        strText = "style ='width:200'>" + strText.Substring(0, 95) + "...";
    //                    }
    //                    else if (strText.Length >= 12 && dt.Columns[ColIndex].ColumnName == "公司名称")
    //                    {
    //                        strText = ">" + strText.Substring(0, 10) + "...";
    //                    }
    //                    else if (ColIndex == 7 && isMobile() && strText.Length >= 56)
    //                    {
    //                        strText = "<tr style='height: 60px; '><td  style='text-align: justify;max-width:100%;overflow-wrap: break-word;white-space: normal;width: 100%;max-width: 100 %;box-sizing: border-box; " +
    //                             "overflow: hidden;" + "line-height: 1.5em;" + "'colspan='" + colSpan.ToString() + "'><div style='font-size: 15px;/*margin: 0 2ch;*/text-align: left; '>" + strText.Substring(0, 56) + "...";
    //                    }
    //                    else if (ColIndex == 7 && isMobile())
    //                    {
    //                        strText = "<tr><td  style='text-align: justify;max-width:100%;overflow-wrap: break-word;white-space: normal;width: 100%;max-width: 100 %;box-sizing: border-box; " +
    //                             "overflow: hidden;" + "line-height: 1.5em;" + "'colspan='" + colSpan.ToString() + "'><div style='font-size: 15px;/*margin: 0 2ch;*/text-align: left; '>" + strText;
    //                    }
    //                    else
    //                    {
    //                        strText = ">" + strText;
    //                    }


    //                    if (dt.Columns[ColIndex].ColumnName == "公司名称" && isMobile())
    //                    {
    //                        strText = "style='font-size:15px;'> <a href='SeeDetail.aspx?TaskID=" + record_id + "' target='blank'" + strText + "</a>";
    //                    }
    //                    else if (dt.Columns[ColIndex].ColumnName == "公司名称" && !isMobile())
    //                    {
    //                        strText = "> <a href='SeeDetail.aspx?TaskID=" + record_id + "' target='blank'" + strText + "</a>";
    //                    }

    //                    if (ColIndex == 7 && isMobile())
    //                    {
    //                        strLine1 += strText + "</td>";
    //                    }
    //                    else
    //                    {
    //                        strLine1 += "<td  " + strText + "</td>";
    //                    }


    //                    //else if (ColIndex == 0 && isMobile()) {
    //                    //    strLine1 += "<td style='text-align: left;'> " + strText + "</br><span style='color:#6a6a6a;'> ";
    //                    //}
    //                    continue;
    //                case 12:
    //                    // strLine2 += "<tr style ='background-color :" + strRowColor + "'>";
    //                    //strLine2 += "<td style='height:" + InputParams.longCharShowHeight + "'  colspan='" + colSpan.ToString() + "'>" + dt.Columns[ColIndex].Caption + "：" + dt.Rows[(pagerIndex - 1) * InputParams.pageSize + rdCount][ColIndex] + "</td>";
    //                    strLine2 += "<td style='white-space: nowrap;height:" + InputParams.longCharShowHeight + "'  colspan='" + colSpan.ToString() + "'>" + dt.Columns

    //[ColIndex].Caption + "：" + dt.Rows[(pagerIndex - 1) * InputParams.pageSize + rdCount][ColIndex] + strShowSpace + "</td>";
    //                    continue;

    //                case 13:
    //                    if (ColIndex == 3 && isMobile())
    //                    {
    //                        strLine1 += "<td>" + "跟踪" + "<span style='color:#008B45;font-weight: bold;'>" + dt.Rows[(pagerIndex - 1) * InputParams.pageSize + rdCount][ColIndex] + "</span>次</td>";
    //                    }
    //                    else
    //                    {
    //                        strLine1 += "<td>" + dt.Rows[(pagerIndex - 1) * InputParams.pageSize + rdCount][ColIndex] + "</td>";
    //                    }
    //                    continue;
    //                case 19:                                                                                          //浮点型
    //                    strLine1 += "<td>" + dt.Rows[(pagerIndex - 1) * InputParams.pageSize + rdCount][ColIndex] + "</td>";
    //                    continue;

    //                case 14:
    //                    DateTime dt2;

    //                    string result = "";
    //                    bool res = DateTime.TryParse(dt.Rows[(pagerIndex - 1) * InputParams.pageSize + rdCount][ColIndex].ToString(), out dt2);

    //                    if (res)
    //                    {
    //                        result = FormatDateTime(dt2);
    //                    }
    //                    else
    //                    {
    //                        dt2 = DateTime.MinValue; // 默认值
    //                    }

    //                    if (ColIndex == 4 && isMobile() && res)
    //                    {
    //                        //Literal ltr14 = new Literal();
    //                        //ltr14.Text = "</br>";
    //                        //placeHolder2.Controls.Add(ltr14);
    //                        strLine1 += "<td style='white-space: nowrap;text-align: left;' >" + "下次：" + result + "</br>";
    //                    }
    //                    else if (ColIndex == 4 && isMobile() && !res)
    //                    {
    //                        strLine1 += "<td id='underTd' style='white-space: nowrap;text-align: left;' >" + "下次：" + "-         " + "</br>";
    //                    }
    //                    else if (ColIndex == 5 && isMobile())
    //                    {
    //                        strLine1 += "上次：" + result + "</td>";
    //                    }
    //                    else if (ColIndex == 6 && isMobile())
    //                    {
    //                        strLine1 += "</tr><tr><td  style='color:#6a6a6a;text-align: left;font-size: 12px;'>" + dt2.Date.ToShortDateString()
    //                                        + " " + dt2.Hour.ToString() + ":" + dt2.Minute.ToString() + " 添加" + "</td >";
    //                    }
    //                    else
    //                    {
    //                        strLine1 += "<td style='white-space: nowrap;' >" + dt2.Date.ToShortDateString() + " " + dt2.Hour.ToString() + ":" + dt2.Minute.ToString() + "</td>";
    //                    }

    //                    //DateTime dt2 = DateTime.Parse(dt.Rows[(pagerIndex - 1) * InputParams.pageSize + rdCount][ColIndex].ToString());
    //                    //strLine1 += "<td style='white-space: nowrap;' >" + dt2.Date.ToShortDateString() + " " + dt2.Hour.ToString() + ":" + dt2.Minute.ToString() + "</td>";

    //                    continue;

    //                case 15:

    //                    string text = "";
    //                    if (flag != 2)
    //                    {
    //                        if (int.Parse(dt.Rows[(pagerIndex - 1) * InputParams.pageSize + rdCount][ColIndex].ToString()) == 1)
    //                            text = "是";
    //                        else
    //                            text = "否";
    //                        flag++;

    //                    }
    //                    else
    //                    {
    //                        if (int.Parse(dt.Rows[(pagerIndex - 1) * InputParams.pageSize + rdCount][ColIndex].ToString()) == 1)
    //                            text = "男";
    //                        else
    //                            text = "女";
    //                        flag = 1;
    //                    }

    //                    strLine1 += "<td>" + text + "</td>";
    //                    continue;
    //                case 16:
    //                    strLine1 += "<td><img  alt=''  height='" + InputParams.picTableHeight + "'  src='" + dt.Rows[(pagerIndex - 1) * InputParams.pageSize +

    //rdCount][ColIndex] + "'/></td>";
    //                    continue;

    //                default:
    //                    string ss1 = dt.Rows[(pagerIndex - 1) * InputParams.pageSize + rdCount][ColIndex].ToString();


    //                    int enumItem = int.Parse(dt.Rows[(pagerIndex - 1) * InputParams.pageSize + rdCount][ColIndex].ToString());
    //                    int enumT = picLongchar[ColIndex];
    //                    int enumLen;
    //                    for (enumLen = 0; enumLen < efTs[enumT].Length; enumLen++)
    //                    {
    //                        if (efTs[enumT][enumLen].enumItem == enumItem)
    //                        {
    //                            break;
    //                        }
    //                    }
    //                    if (enumLen == efTs[enumT].Length)
    //                    {
    //                        enumLen = enumLen - 1;
    //                    }


    //                    if (ColIndex == 1 && !isMobile())
    //                    {
    //                        strLine1 += "<td>" + (new Common()).GetTown(int.Parse(ss1)) + "</td>";
    //                    }
    //                    else if (ColIndex == 1 && isMobile())
    //                    {
    //                        strLine1 += "<td style='white-space:nowrap;'><span style='color:#008B45'>" + efTs[enumT][enumLen].itemDetail + "</span></br>";
    //                    }
    //                    else if (ColIndex == 2 && isMobile())
    //                    {
    //                        strLine1 += efTs[enumT][enumLen].itemDetail + "</td>";
    //                    }
    //                    else
    //                    {
    //                        strLine1 += "<td style='white-space:nowrap'>" + efTs[enumT][enumLen].itemDetail + "</td>";
    //                    }
    //                    continue;
    //            }
    //        }

    //        Literal ltl13 = new Literal();
    //        ltl13.Text = strLine1 + "" + strLine2;
    //        placeHolder2.Controls.Add(ltl13);

    //        int iGroupManager = int.Parse(Session["isGroupManager"].ToString());
    //        if (iGroupManager == 1 || iGroupManager == 2)
    //        {

    //            //string[] arrStrTaskID = strLinkID.Split(' ');
    //            //int record_id = int.Parse(arrStrTaskID[0].ToString());


    //            Literal ltlExecutor = new Literal();
    //            ltlExecutor.Text = "<td> " + (new Common()).GetUserName(record_id) + "</td>";
    //            placeHolder2.Controls.Add(ltlExecutor);

    //        }
    //        if (!isMobile())
    //        {
    //            Literal ltl131 = new Literal();
    //            //ltl131.Text = "<td  rowspan='" + rowSpan.ToString() + "'>";
    //            ltl131.Text = "<td>";
    //            placeHolder2.Controls.Add(ltl131);
    //        }
    //        //获取记录的ID
    //        #region 获取记录的ID
    //        string[] arrStrTaskID_See = strLinkID.Split(' ');
    //        int taskID = int.Parse(arrStrTaskID_See[0].ToString());
    //        Session["pagerIndex"] = ViewState["pagerIndex"].ToString();
    //        Session["searchStr"] = ViewState["searchStr"].ToString();
    //        #endregion 获取记录的ID

    //        //HyperLink lkbSee = new HyperLink();
    //        //lkbSee.ID = "lkbSee " + strLinkID;
    //        //lkbSee.Text = "查看";
    //        //lkbSee.Target = "_blank";
    //        //lkbSee.NavigateUrl = "SeeDetail.aspx?TaskID=" + taskID;


    //        //placeHolder2.Controls.Add(lkbSee);

    //        //Literal ltlSee = new Literal();
    //        //ltlSee.Text = "</td><td  rowspan='" + rowSpan.ToString() + "'>";
    //        //placeHolder2.Controls.Add(ltlSee);

    //        //HyperLink lkbModify = new HyperLink();
    //        //lkbModify.ID = "lkbModify " + strLinkID;
    //        //lkbModify.Text = "修改";
    //        //lkbModify.Target = "_blank";
    //        //lkbModify.NavigateUrl = "Modify.aspx?recordId=" + taskID;
    //        //placeHolder2.Controls.Add(lkbModify);

    //        LinkButton lkbModify = new LinkButton();
    //        lkbModify.ID = "lkbModify " + strLinkID;
    //        lkbModify.Text = "修改";
    //        lkbModify.Command += new CommandEventHandler(lkbModify_Command);
    //        lkbModify.CommandArgument = strLinkID;
    //        if (!isMobile())
    //        {
    //            placeHolder2.Controls.Add(lkbModify);
    //        }

    //        if (!isMobile())
    //        {
    //            Literal ltl14 = new Literal();
    //            //ltl14.Text = "</td><td  rowspan='" + rowSpan.ToString() + "'>";
    //            ltl14.Text = "<span><br />";
    //            placeHolder2.Controls.Add(ltl14);
    //        }
    //        HyperLink lkbReport = new HyperLink();
    //        lkbReport.ID = "lkbReport " + strLinkID;
    //        lkbReport.Text = "追加记录";
    //        //lkbReport.Command += new CommandEventHandler(lkbReport_Command);
    //        //lkbReport.CommandArgument = strLinkID;
    //        lkbReport.Target = "_blank";
    //        lkbReport.NavigateUrl = "addRecord.aspx?id=" + taskID;
    //        if (!isMobile())
    //        {
    //            placeHolder2.Controls.Add(lkbReport);
    //        }
    //        //Literal ltl15 = new Literal();
    //        //ltl15.Text = "</td><td  rowspan='" + rowSpan.ToString() + "'>";
    //        //placeHolder2.Controls.Add(ltl15);

    //        //LinkButton lkbDelete = new LinkButton();
    //        //lkbDelete.ID = "lkbDelete " + strLinkID;
    //        //lkbDelete.Text = "删除";
    //        //lkbDelete.Command += new CommandEventHandler(lkbDelete_Command);
    //        //lkbDelete.CommandArgument = strLinkID;
    //        //placeHolder2.Controls.Add(lkbDelete);
    //        //((LinkButton)placeHolder2.FindControl("lkbDelete " + strLinkID)).Attributes.Add("onclick", "return confirm('确认删除?')");
    //        if (!isMobile())
    //        {
    //            Literal ltl134 = new Literal();
    //            ltl134.Text = "</span></td></tr>";
    //            placeHolder2.Controls.Add(ltl134);
    //        }
    //        else
    //        {
    //            Literal ltl135 = new Literal();
    //            ltl135.Text = "</div></tr>";
    //            placeHolder2.Controls.Add(ltl135);
    //        }


    //    }


    /// <summary>
    /// 单个删除
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void lkbDelete_Command(object sender, CommandEventArgs e)
    {
        string strTaskID = e.CommandArgument.ToString();
        string[] arrStrTaskID = strTaskID.Split(' ');
        int taskID = int.Parse(arrStrTaskID[0].ToString());

        try
        {

            SqlConnection conn = new SqlConnection(ConnStr);
            if (conn.State.ToString() == "Closed")
            {
                conn.Open();
            }

            //string strDelete = " DELETE FROM  " + mainTable + "  WHERE " + pKey + "=" + taskID;
            //update by tcf 2010-3-1
            string strDelete = " Update " + mainTable + " Set isDel = 1 Where " + pKey + "=" + taskID;

            SqlCommand comm = new SqlCommand(strDelete, conn);
            comm.ExecuteNonQuery();
            if (conn.State.ToString() == "Open")
            {
                conn.Close();
            }


            Page_Load(sender, e);
        }
        catch (SqlException ex)
        {
            Response.Write(ex.ToString());
        }

    }

    /// <summary>
    /// 追加记录
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void lkbReport_Command(object sender, CommandEventArgs e)
    {

        string strTaskID = e.CommandArgument.ToString();
        string[] arrStrTaskID = strTaskID.Split(' ');
        int taskID = int.Parse(arrStrTaskID[0].ToString());
        try
        {

            Session["pagerIndex"] = ViewState["pagerIndex"].ToString();
            Session["searchStr"] = ViewState["searchStr"].ToString();
            Response.Redirect("AddRecord.aspx?id=" + taskID);
        }

        catch (SqlException ex)
        {
            Response.Write(ex.ToString());
        }

    }

    /// <summary>
    /// 查看单个任务
    /// </summary>
    /// <param name="sendor"></param>
    /// <param name="e"></param>
    protected void lkbSee_Command(object sendor, CommandEventArgs e)
    {
        string strTaskID = e.CommandArgument.ToString();
        string[] arrStrTaskID = strTaskID.Split(' ');
        int taskID = int.Parse(arrStrTaskID[0].ToString());

        Session["pagerIndex"] = ViewState["pagerIndex"].ToString();
        Session["searchStr"] = ViewState["searchStr"].ToString();
        RegisterStartupScript("js", "<script>window.open('SeeDetail.aspx?TaskID=" + taskID + "', '_blank', 'height=500,width=700,top=0,left=0,scrollbars=yes,resizable=yes')</script>");

        // Response.Redirect("SeeDetail.aspx?TaskID=" + taskID);
    }


    /// <summary>
    /// 单个修改
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void lkbModify_Command(object sender, CommandEventArgs e)
    {
        string strTaskID = e.CommandArgument.ToString();
        string[] arrStrTaskID = strTaskID.Split(' ');
        int taskID = int.Parse(arrStrTaskID[0].ToString());
        int[] arrModify = new int[1];
        arrModify[0] = taskID;

        Session["myId"] = taskID;    //当前客户ID【跟踪表】
        Session["ckbsModify"] = arrModify;

        Session["pagerIndex"] = ViewState["pagerIndex"].ToString();
        Session["searchStr"] = ViewState["searchStr"].ToString();

        Page.ClientScript.RegisterStartupScript(this.GetType(), "", "<script>window.open('Modify.aspx?recordId=" + taskID + "', '_blank')</script>");
        //RegisterStartupScript("js", "<script>window.open('Modify.aspx?recordId=" + taskID + "', '_blank')</script>");

        //Response.Write("<script>window.open('Modify.aspx?recordId=' + taskID,'_blank')</script>");
        //Response.Redirect("Modify.aspx?recordId=" + taskID);
    }



    protected void lkbBrowseUser_Click(object sender, EventArgs e)
    {
        Session["supPage"] = "Default.aspx";
        Response.Redirect("BrowseUsers.aspx?");
    }
    protected void ChooseUsers_Click(object sender, EventArgs e)
    {
        Session["supPage"] = "Default.aspx";
        Response.Redirect("BrowseUsers.aspx");
    }

    /// <summary>
    /// 获取申请状态
    /// </summary>
    void GetUserApplyStatus()
    {
        string userId = Session["user_id"].ToString();
        string groupId = Session["group_id"].ToString();
        string isManager = Session["isGroupManager"].ToString();

        if (isManager == "1" || isManager == "2")
        {
            lbUserRole.Text = "管理员";
            lkBtnApplyAdmin.Visible = false;
            return;
        }
        lbUserRole.Text = "普通用户";
        lkBtnApplyAdmin.Visible = true;
        lkBtnApplyAdmin.Enabled = false;
        string sql = $"select * from ApplyAdminRecord where user_id = {userId}";
        DataTable dataTable = Common.ExecuteQuery(sql);
        if (dataTable.Rows.Count > 0)
        {
            DataRow row = dataTable.Rows[0];
            switch (row["deal_result"].ToString())
            {
                case "-1":
                    lkBtnApplyAdmin.Text = "被拒绝";
                    break;
                case "0":
                    lkBtnApplyAdmin.Text = "审批中";
                    break;
                case "1":
                    lkBtnApplyAdmin.Visible = false;
                    break;
                default:
                    break;
            }
        }
        else
        {
            lkBtnApplyAdmin.Text = "申请成为管理员";
            lkBtnApplyAdmin.Enabled = true;
        }
    }

    protected void lkBtnApplyAdmin_Click(object sender, EventArgs e)
    {
        string userId = Session["user_id"].ToString();
        string groupId = Session["group_id"].ToString();
        string isManager = Session["isGroupManager"].ToString();
        if (isManager.Equals("1") || isManager.Equals("2"))
        {
            Response.Write(Common.alertMsg("已经是管理员了,不用申请了"));
            return;
        }
        DateTime dateTime = DateTime.Now;
        string sql = $"insert into ApplyAdminRecord(user_id, group_id,apply_time) values({userId}, {groupId},'{dateTime}')";
        int row = Common.ExecuteNonQuery(sql);
        Response.Redirect(Request.Path);
    }
}

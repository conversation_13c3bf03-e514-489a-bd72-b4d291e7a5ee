﻿<HTML>
<HEAD>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="pop.css">

<script language="JavaScript" src="img.js"></script>

<script language="javascript">
var sAction = "INSERT";
var sTitle = "插入";

var oControl;
var oSelection;
var sRangeType;
var bModify = false;
var bImg = false;

var sUrl = "http://";
var sAlt = "";
var sTarget = "_self";
var sText = "";

var oTarget;
var oTarget2;
var oUrl;
var oAlt;

var sCheckFlag = "url";
var wbtbWin;
if (window.opener){
	wbtbWin=launchParameters["wbtb"];
	//oSelection = wbtbWin.document.getElementById("WBTB_Composition").contentWindow.document.selection.createRange();
	//sRangeType = wbtbWin.document.getElementById("WBTB_Composition").contentWindow.document.selection.type;
}else{
	wbtbWin=dialogArguments;
	oSelection = wbtbWin.WBTB_Composition.document.selection.createRange();
	sRangeType = wbtbWin.WBTB_Composition.document.selection.type;
}

// IE里才可以修改
if (window.opener){
}else{
	if (sRangeType == "Control")
	{
		if (oSelection.item(0).tagName == "IMG"){
			if (oSelection.item(0).parentElement.tagName == "A") {
				bModify = true;
				oControl = oSelection.item(0).parentElement;
				sText = oControl.outerHTML;
			}else{
				sText = oSelection.item(0).outerHTML;
			}
			bImg = true;
		}
	}else if (oSelection.parentElement().tagName == "A"){
		bModify = true;
		oControl = oSelection.parentElement();
		sText = oControl.innerHTML;
	}else{
		sText = oSelection.htmlText;
	}
}

if (bModify){
	sAction = "MODI";
	sTitle = "修改";
	sCheckFlag = "url";
	sUrl = oControl.href;
	sAlt = oControl.title;
	sTarget = oControl.target;
}

document.write("<title>" + sTitle + "超链接</title>");


// 初始值
function InitDocument(){
	oTarget = document.getElementById("d_target");
	oTarget2 = document.getElementById("d_target2");
	oUrl = document.getElementById("d_url");
	oAlt = document.getElementById("d_alt");

	SearchSelectValue(oTarget, sTarget);
	
	if (sTarget != "_self" && sTarget != "_top" && sTarget != "_blank" && sAction == "MODI")
	{
		document.getElementById("d_cb1").checked = true;
		oTarget2.value = sTarget;
		CheckChange(false);
	}else{
		CheckChange(true);
	}
	
	oUrl.value = sUrl;
	oAlt.value = sAlt;
}

function CheckChange(sel)
{
	oTarget.disabled = !sel;
	oTarget2.disabled = sel;
}


// 本窗口返回值
function ReturnValue(){
	sUrl = oUrl.value;
	sAlt = oAlt.value;
	if (oTarget.disabled)
	{
		sTarget = oTarget2.value;
	}else{
		sTarget = oTarget.value;
	}
	
	if (sUrl=="" || sUrl=="http://") {window.close();return;};	
	if (sAction == "MODI") {
		oControl.href = sUrl;
		oControl.title = sAlt;
		oControl.target = sTarget;
	}else{
		var sHTML = '<a href="'+sUrl+'" title="'+sAlt+'" target="'+sTarget+'" >';
		if (sText == "")
			sHTML += sUrl;
		else
			sHTML += sText;
		sHTML += '</a>';
	//alert(sHTML);
	//return;
		if (bImg)
			oSelection.item(0).parentElement.removeChild(oSelection.item(0));
		wbtbWin.WBTB_InsertHtml(sHTML);
	}

	//window.returnValue = null;
	window.close();
}

// 搜索下拉框值与指定值匹配，并选择匹配项
function SearchSelectValue(o_Select, s_Value){
	for (var i=0;i<o_Select.length;i++){
		if (o_Select.options[i].value == s_Value){
			o_Select.selectedIndex = i;
			return true;
		}
	}
	return false;
}

</script>

<body bgColor="menu" onload="InitDocument()"  topmargin="5" leftmargin="5">

<table width="100%" border="0" cellpadding="0" cellspacing="0" align="center" >
<tr style="padding:2 0">
	<td>
	<fieldset>
	<legend>链接</legend>
	<table border="0" cellpadding="3" cellspacing="0">
		<tr><td colspan="9" height="5"></td></tr>
		<tr>
			<td colspan="2">
			地址: &nbsp;<input type="text" id="d_url" style="width:200px" size=30 value="" >
			</td>
		</tr>
		<tr>
			<td>
			目标:&nbsp; 
			<select id="d_target">
			<option value="_self">_self</option>
			<option value="_top">_top</option>
			<option value="_blank">_blank</option>
			</select>
			</td>
			<td align="right">
			<input type="checkbox" class="cb" id="d_cb1" name="d_cb1" onclick="CheckChange(!this.checked);"><label for="d_cb1">
			自定义</label> 
			<input type="text" id="d_target2" size="6" >
			</td>
		</tr>
		<tr>
			<td colspan=2>
			说明: &nbsp;<input type="text" id="d_alt" size="38" value="" style="width:200px">
			</td>
		</tr>
		</table>
	</fieldset>
	</td>
</tr>
<tr style="padding:2 0"><td align=right>
	<button id="Ok" onclick="ReturnValue();">  确定  </button>&nbsp;&nbsp;
	<button onclick="window.close();">  取消  </button>
</td></tr>
</table>

</body>
</html>
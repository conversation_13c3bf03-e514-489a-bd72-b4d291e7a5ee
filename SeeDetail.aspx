﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="SeeDetail.aspx.cs" Inherits="SeeTask" %>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title>查看记录详情</title>
    <meta name="viewport" content="width=device-width,height=device-height,initial-scale=1.0,user-scalable=no">

<%--    <script src="https://cdn.tailwindcss.com" type="text/javascript"></script>
    <script type="text/javascript">
    tailwind.config = {
        plugins: [tailwindcssLineClamp],
    };
    </script>
    <script src="https://unpkg.com/@tailwindcss/line-clamp@0.4.0" type="text/javascript"></script>--%>

    <script type="text/javascript">
        function showTab(id) {
          document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
          document.querySelectorAll('.section').forEach(sec => sec.classList.add('hidden'));
          document.getElementById(id).classList.remove('hidden');
          if (id === 'info') {
            document.querySelector('.tab:nth-child(1)').classList.add('active');
          } else {
            document.querySelector('.tab:nth-child(2)').classList.add('active');
          }
        }
	</script>
    <style type="text/css">
        body, table, tr, td {
            font-size: 18px;
            font-family: 宋体;

        }
        #Table1 {
            width:50%;
        }
        .mobile-footer {
            display: none;
        }

        @media (min-width: 769px) {
        body {
            position: relative;
            left: 26%;  /* 仅电脑端左偏移25% */

        }
        }

        @media (max-width: 768px) {
        body, table, tr, td {
            font-size: 16px;
            font-family: 宋体;           
            max-width:100%; 
            overflow-x: auto;
            height:auto;
            position: relative;
            margin: -20px 0 0 0
/*            margin:0px;*/
        }
        #Table1 {
            width:100%;
        }
/*        .mobile-footer {
        display: block;
        position: fixed;
        bottom: 0;
        left: 0;
        width: 100%;
        text-align: center;
        background-color: white;
        padding: 10px;
        z-index: 3;
        }*/
    .container {
      max-width: 100%;
      width: 100%;
      margin: 0;
      padding: 0;
    }
    .tabs {
      display: flex;
      background-color: #fff;
      border-bottom: 1px solid #ddd;
    }
    .tab {
      flex: 1;
      text-align: center;
      padding: 12px;
      font-weight: bold;
      cursor: pointer;
      color: #666;
    }
    .tab.active {
      color: #006699;
      border-bottom: 2px solid #006699;
      background-color: #f2f8fb;
    }
    .section {
      padding: 16px;
      background-color: #fff;
    }
    .hidden {
      display: none;
    }
    h1 {
      font-size: 20px;
      margin-bottom: 12px;
    }
    .label {
      font-weight: bold;
      color: #666;
    }
    .value {
      margin-bottom: 8px;
    }
    .record {
      border-top: 1px solid #eee;
      padding-top: 12px;
      margin-top: 12px;
    }
    .record h3 {
      font-size: 16px;
      margin-bottom: 6px;
    }
    .record p {
      margin: 4px 0;
      font-size: 14px;
    }

    }
    </style>

</head>
<body >
    <form id="form1" method="post" runat="server">
            <table id="Table1" border="1" cellpadding="1" cellspacing="1" style=" background-color: #E5FDFE; vertical-align: middle;   top: 20px;">
                <tr>
                    <td style="color: Purple; height: 25px; font-size: 20px; font-family: 幼圆;">客户跟踪系统 | 查看跟踪记录
                    </td>
                </tr>
                <tr>
                    <td>
                        <div class="container">
                        <div class="tabs">
                            <asp:PlaceHolder ID="placeHolder1" runat="server"></asp:PlaceHolder>
                        </div>
                        </div>

                        <br />
                        <br />
                    </td>

                </tr>
                <tr>
                    <td align="center">
                        <%-- --%>
                        <div id="deskFooterDiv" runat="server" >
                        <br />
                        <asp:LinkButton ID="LinkButton1" ForeColor="#871f78" Text="追加记录" runat="server" OnClick="hlkAddRecod_Click"  
                            Style="text-decoration: none; border: 1px solid #871f78; padding: 2px 8px; display: inline-block; border-radius: 10px;
                            background-color: #E5FDFE; "/>
                        <asp:LinkButton ID="LinkButton2" ForeColor="#871f78" Font-Underline="False" Text="修改" runat="server" OnClick="hlkModify_Click" 
                            Style="text-decoration: none; border: 1px solid #871f78; padding: 2px 8px; display: inline-block; border-radius: 10px;
                            background-color: #E5FDFE; "/>
                        <asp:LinkButton ID="LinkButton3" ForeColor="#871f78" Font-Underline="False" Text="关闭" runat="server" OnClick="hlkReturn_Click" 
                            Style="text-decoration: none; border: 1px solid #871f78; padding: 2px 8px; display: inline-block; border-radius: 10px;
                            background-color: #E5FDFE; "/>
                        <br />
                        <br />
                        </div>
                        <div id="mobileFooterDiv" runat="server"  style="position: fixed; bottom: 0; left: 0; width: 100%; text-align: center; background-color: white; padding: 10px; z-index: 3;">
<%--                        <br />--%>
                        <asp:LinkButton ID="hlkAddRecod" ForeColor="#871f78" Text="追加记录" runat="server" OnClick="hlkAddRecod_Click"  
                            Style="text-decoration: none; border: 1px solid #871f78; padding: 2px 8px; display: inline-block; border-radius: 10px;
                            background-color: #E5FDFE; "/>
                        <asp:LinkButton ID="hlkModify" ForeColor="#871f78" Font-Underline="False" Text="修改" runat="server" OnClick="hlkModify_Click" 
                            Style="text-decoration: none; border: 1px solid #871f78; padding: 2px 8px; display: inline-block; border-radius: 10px;
                            background-color: #E5FDFE; "/>
                        <asp:LinkButton ID="hlkReturn" ForeColor="#871f78" Font-Underline="False" Text="关闭" runat="server" OnClick="hlkReturn_Click" 
                            Style="text-decoration: none; border: 1px solid #871f78; padding: 2px 8px; display: inline-block; border-radius: 10px;
                            background-color: #E5FDFE; "/>
<%--                        <br />
                        <br />--%>
                        </div>
                    </td>
                </tr>
            </table>
    </form>
</body>
</html>

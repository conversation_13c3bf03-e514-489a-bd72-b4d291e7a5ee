﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="ADD.aspx.cs" Inherits="ADD" %>

<html xmlns="http://www.w3.org/1999/xhtml">
<head id="Head1" runat="server">
    <title>添加客户信息</title>
    <%--日历--%>
    <meta name="viewport" content="width=device-width, initial-scale=0.67,user-scalable=no">

    <style type="text/css">
        body, table, tr, td {
            font-size: 16px;
            font-family: 宋体;
        }
        #addTable {
            width:80%;
        }
        @media (min-width: 769px) {
        body {
            position: relative;
            
        }

        #M_Td1 {
            text-align: center;
        }

        }

        @media (max-width: 768px) {
        body, table, tr, td {
            font-size: 22px;
            font-family: 宋体;
            max-width:100%; 
/*            width:350px;*/
            overflow-x: auto;
            height:auto;
            margin-top:0%;
            margin-left:0.3%;
            position: relative;
            margin-bottom:-10%;
        }
        #fanHui {
                /*margin-left:55%;*/
            }
        #addTable {
            width:100%;
        }
        .textbox1 {
            width: 100%;
            padding: 8px;
            font-size: 25px;
/*            border: 1px solid #ccc;*/
            border-radius: 4px;
            box-sizing: border-box;
        }
        .textbox2 {
            font-size:17px;
        }
        .textbox3 {
            font-size:17px;
        }
        .textbox13 {
            font-size:17px;
        }
        .textbox4 {
            font-size:17px;
            font-size: 25px;
            width: 100%;
            padding: 8px;

        }
        .time-dropdown {
            font-size:17px;
        }
        .time-dropdown option {
            font-size: 14px;
        }
        .time-dropdown2 {
            font-size:17px;
        }
        .time-dropdown2 option {
            font-size: 14px;
        }
        .time-dropdown3 {
            font-size:17px;
        }
        .time-dropdown3 option {
            font-size: 14px;
        }
        .navbar {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            font-size: 26px;
        }

        .navbar span {
            opacity: 0.8;
        }

        .container {
            padding: 20px;
        }

        .card {
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            width: 100%;
            max-width: 600px;
            margin: auto;
            box-shadow: 0 2px 6px rgba(0,0,0,0.05);
            box-sizing: border-box;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            font-size: 27px;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }

        .form-group span,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 8px;
            font-size: 24px;
/*            border: 1px solid #ccc;*/
            border-radius: 4px;
            box-sizing: border-box;
        }

        .form-group textarea {
            height: 80px;
            resize: vertical;
        }

        .form-actions {
            text-align: center;
            margin-top: 20px;
        }

        .form-actions button {
            padding: 8px 16px;
            margin: 0 10px;
            font-size: 14px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        .btn-primary {
            background-color: #007bff;
            color: white;
        }

        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
            #btnAdd {
                font-size:26px;
            }
            #LinkButton1 {
                font-size:26px;

            }

    }
    </style>
</head>
<%--日历--%>

<script src="js/time.js" type="text/javascript"></script>

<body>
    <form id="form1" method="post" runat="server">
<%--        <div id="fanHui" style="text-align: center;" >
            <asp:HyperLink ID="HyperLink1" runat="server" NavigateUrl="~/Default.aspx" ForeColor="#3366FF" Font-Underline="False"
                Style="text-decoration: none; border: 1px solid #871f78; padding: 2px 8px; display: inline-block; border-radius: 10px;
                            background-color: #E6E8FA; ">←返回首页</asp:HyperLink>
        </div>--%>
        <table id="addTable"  border="1" align="center" style="background: #e6e8fa; background-position: center; border-color: #666666">
            <tr>
                <td id="M_Td1" >
                    <div id="div1" runat="server">
                        &nbsp;
                    </div>
                </td>
            </tr>
            <tr align="center">
                <td align="center" style="border: 0px">
                    <br />
                    &nbsp;<asp:LinkButton ID="btnAdd" Width="80px" runat="server" Text="添加" OnClick="btnAdd_Click" 
                        Style="text-decoration: none; border: 1px solid #871f78; padding: 2px 8px; display: inline-block; border-radius: 10px;
                            background-color: #E6E8FA; "/>&nbsp;&nbsp;&nbsp;&nbsp;
                    <asp:LinkButton ID="LinkButton1" Font-Underline="False" runat="server" PostBackUrl="~/Default.aspx"
                        Style="text-decoration: none; border: 1px solid #871f78; padding: 2px 8px; display: inline-block; border-radius: 10px;
                            background-color: #E6E8FA; ">查看客户</asp:LinkButton><br />
                    <br />
                </td>
            </tr>
        </table>
    </form>
</body>
</html>


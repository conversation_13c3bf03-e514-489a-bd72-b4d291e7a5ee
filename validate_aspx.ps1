# ASP.NET页面格式验证脚本
# 检查Default.aspx文件中的服务器控件格式是否正确

Write-Host "正在验证 Default.aspx 文件格式..." -ForegroundColor Green

$filePath = "Default.aspx"
$content = Get-Content $filePath -Raw

# 检查常见的格式问题
$issues = @()

# 1. 检查未闭合的服务器控件
$unclosedControls = [regex]::Matches($content, '<asp:\w+[^>]*[^/]>(?![^<]*</asp:\w+>)')
if ($unclosedControls.Count -gt 0) {
    $issues += "发现 $($unclosedControls.Count) 个可能未正确闭合的服务器控件"
}

# 2. 检查跨行但格式不正确的控件
$lines = Get-Content $filePath
for ($i = 0; $i -lt $lines.Count - 1; $i++) {
    $currentLine = $lines[$i].Trim()
    $nextLine = $lines[$i + 1].Trim()
    
    # 检查以asp:开头但不以>或/>结尾的行，且下一行不是属性继续
    if ($currentLine -match '^<asp:\w+' -and $currentLine -notmatch '[/>]$' -and $nextLine -notmatch '^\s*\w+\s*=') {
        $issues += "第 $($i + 1) 行可能存在格式问题: $currentLine"
    }
}

# 3. 检查是否有HTML标签嵌套在服务器控件内
$nestedHtml = [regex]::Matches($content, '<asp:[^>]*>[\s\S]*?<(?!asp:)[a-zA-Z]')
if ($nestedHtml.Count -gt 0) {
    $issues += "发现 $($nestedHtml.Count) 个服务器控件内可能包含HTML标签"
}

# 4. 检查属性值是否正确引用
$unquotedAttributes = [regex]::Matches($content, '<asp:[^>]*\s+\w+\s*=\s*[^"\'>\s][^>\s]*[^"\'>]')
if ($unquotedAttributes.Count -gt 0) {
    $issues += "发现 $($unquotedAttributes.Count) 个可能未正确引用的属性值"
}

# 输出结果
if ($issues.Count -eq 0) {
    Write-Host "✅ 验证通过！Default.aspx 文件格式正确。" -ForegroundColor Green
    Write-Host ""
    Write-Host "文件统计信息:" -ForegroundColor Cyan
    Write-Host "- 总行数: $($lines.Count)" -ForegroundColor White
    Write-Host "- 服务器控件数量: $([regex]::Matches($content, '<asp:\w+').Count)" -ForegroundColor White
    Write-Host "- 文件大小: $([math]::Round((Get-Item $filePath).Length / 1KB, 2)) KB" -ForegroundColor White
} else {
    Write-Host "⚠️  发现以下潜在问题:" -ForegroundColor Yellow
    foreach ($issue in $issues) {
        Write-Host "  - $issue" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "验证完成！" -ForegroundColor Green

# 额外检查：统计美化相关的元素
Write-Host ""
Write-Host "美化元素统计:" -ForegroundColor Cyan
$emojiCount = [regex]::Matches($content, '[\u{1F300}-\u{1F9FF}]').Count
$cssClassCount = [regex]::Matches($content, 'CssClass="[^"]*"').Count
$inlineStyleCount = [regex]::Matches($content, 'style="[^"]*"').Count

Write-Host "- 表情符号数量: $emojiCount" -ForegroundColor White
Write-Host "- CSS类引用: $cssClassCount" -ForegroundColor White
Write-Host "- 内联样式: $inlineStyleCount" -ForegroundColor White

Write-Host ""
Write-Host "🎨 页面美化已完成，可以安全运行！" -ForegroundColor Magenta

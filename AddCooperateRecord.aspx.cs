﻿using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Data.SqlClient;

public partial class AddRecord : System.Web.UI.Page
{
    const string connStr = " Data Source=" + InputParams.DataBaseServer + "; uid=" + InputParams.userName + "; pwd=" + InputParams.password + "; DATABASE=" + InputParams.dbName;
 
    protected void Page_Load(object sender, EventArgs e)
    {   
        if (Request.Params["id"] == null)
            Response.Redirect("Default.aspx");

    }
    protected void Button1_Click(object sender, EventArgs e)
    {

        SqlConnection conn = new SqlConnection(connStr);
        if (conn.State.ToString() == "Closed")
        {
            conn.Open();
        }

        string strSql = "insert into CooperationRecord(follow_client_id,description,follow_time,follow_executor) values("+Request.Params["id"].ToString()+",'"+TextBox1.Text.Trim()+"','"+DateTime.Now.ToString()+"',"+Session["user_id"].ToString()+")" ;
        SqlCommand comm = new SqlCommand(strSql, conn);
        comm.ExecuteNonQuery();

        string strSql1 = "update Cooperation set last_follow_time='"+DateTime.Now.ToString()+ "' where id="+ Request.Params["id"].ToString();
        SqlCommand comm1 = new SqlCommand(strSql1, conn);
        comm1.ExecuteNonQuery();

        if (conn.State.ToString() == "Open")
        {
            conn.Close();
        }

        Response.Redirect("SeeCooperate.aspx?message=1");
    }
}

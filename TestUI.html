<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客户管理系统 - UI 预览</title>
    <link href="Styles/modern.css" rel="stylesheet" type="text/css" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 14px;
            line-height: 1.6;
            color: #2d3748;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 20px;
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 1200px;
            margin: 0 auto;
            overflow: hidden;
        }

        .header-section {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .content-section {
            padding: 30px;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 12px 24px;
            border: none;
            border-radius: 12px;
            font-size: 14px;
            font-weight: 500;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.3s ease;
            gap: 8px;
            margin: 5px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(79, 70, 229, 0.4);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(79, 70, 229, 0.6);
        }

        .btn-secondary {
            background: #f8fafc;
            color: #475569;
            border: 2px solid #e2e8f0;
        }

        .btn-secondary:hover {
            background: #e2e8f0;
            transform: translateY(-1px);
        }

        .btn-danger {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
        }

        .btn-danger:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
        }

        .demo-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            margin: 20px 0;
        }

        .demo-table th {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            padding: 15px 12px;
            text-align: left;
            font-weight: 600;
            font-size: 14px;
        }

        .demo-table td {
            padding: 12px;
            border-bottom: 1px solid #f1f5f9;
            font-size: 13px;
            color: #374151;
        }

        .demo-table tr:hover {
            background: #f8fafc;
        }

        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-active { background: #d1fae5; color: #065f46; }
        .status-pending { background: #fef3c7; color: #92400e; }
        .status-completed { background: #dbeafe; color: #1e40af; }

        .form-control {
            width: 100%;
            padding: 10px 12px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
            margin: 5px 0;
        }

        .form-control:focus {
            outline: none;
            border-color: #4f46e5;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部区域 -->
        <div class="header-section">
            <div style="display: flex; align-items: center; justify-content: center; gap: 15px;">
                <span style="font-size: 32px;">👥</span>
                <h1 style="margin: 0;">客户管理系统</h1>
                <span style="font-size: 32px;">📈</span>
            </div>
            <p style="margin: 10px 0 0 0; opacity: 0.9; font-size: 16px;">专业的客户关系管理平台</p>
        </div>

        <!-- 内容区域 -->
        <div class="content-section">
            <!-- 用户信息区域 -->
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px; flex-wrap: wrap; gap: 15px;">
                <div style="display: flex; align-items: center; gap: 10px;">
                    <span style="font-size: 24px; color: #4f46e5;">👤</span>
                    <span style="font-size: 16px; color: #1e293b; font-weight: 500;">欢迎回来，</span>
                    <span style="font-size: 16px; color: #4f46e5; font-weight: bold;">管理员</span>
                </div>
                <div style="display: flex; align-items: center; gap: 15px;">
                    <div style="display: flex; align-items: center; gap: 8px; padding: 8px 12px; background: rgba(79, 70, 229, 0.1); border-radius: 8px;">
                        <span style="color: #4f46e5; font-size: 14px;">🛡️</span>
                        <span style="font-size: 14px; color: #4f46e5;">当前角色：管理员</span>
                    </div>
                    <button class="btn btn-danger">🚪 注销</button>
                </div>
            </div>

            <!-- 搜索区域 -->
            <div style="background: white; border-radius: 12px; padding: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.05); margin-bottom: 20px;">
                <h3 style="color: #1e293b; margin-bottom: 15px; display: flex; align-items: center; gap: 8px;">
                    <span style="color: #4f46e5;">🔍</span>
                    搜索条件
                </h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">
                    <input type="text" class="form-control" placeholder="公司名称">
                    <input type="text" class="form-control" placeholder="联系人">
                    <input type="text" class="form-control" placeholder="电话">
                    <select class="form-control">
                        <option>客户状态</option>
                        <option>待联系</option>
                        <option>跟进中</option>
                        <option>已确认</option>
                    </select>
                </div>
            </div>

            <!-- 操作按钮区域 -->
            <div style="display: flex; gap: 15px; flex-wrap: wrap; align-items: center; margin-bottom: 20px;">
                <button class="btn btn-primary">🔍 开始搜索</button>
                <button class="btn btn-primary">➕ 添加客户</button>
                <button class="btn btn-danger">🗑️ 批量删除</button>
                <button class="btn btn-secondary">✏️ 批量修改</button>
            </div>

            <!-- 数据表格 -->
            <table class="demo-table">
                <thead>
                    <tr>
                        <th>选择</th>
                        <th>公司名称</th>
                        <th>联系人</th>
                        <th>电话</th>
                        <th>客户状态</th>
                        <th>意向指数</th>
                        <th>最后跟踪时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><input type="checkbox"></td>
                        <td>阿里巴巴集团</td>
                        <td>张三</td>
                        <td>138****1234</td>
                        <td><span class="status-badge status-active">跟进中</span></td>
                        <td>⭐⭐⭐⭐⭐</td>
                        <td>2025-01-08 10:30</td>
                        <td>
                            <button class="btn btn-secondary" style="padding: 4px 8px; font-size: 12px;">查看</button>
                            <button class="btn btn-secondary" style="padding: 4px 8px; font-size: 12px;">编辑</button>
                        </td>
                    </tr>
                    <tr>
                        <td><input type="checkbox"></td>
                        <td>腾讯科技</td>
                        <td>李四</td>
                        <td>139****5678</td>
                        <td><span class="status-badge status-pending">待联系</span></td>
                        <td>⭐⭐⭐⭐</td>
                        <td>2025-01-07 15:20</td>
                        <td>
                            <button class="btn btn-secondary" style="padding: 4px 8px; font-size: 12px;">查看</button>
                            <button class="btn btn-secondary" style="padding: 4px 8px; font-size: 12px;">编辑</button>
                        </td>
                    </tr>
                    <tr>
                        <td><input type="checkbox"></td>
                        <td>百度公司</td>
                        <td>王五</td>
                        <td>137****9012</td>
                        <td><span class="status-badge status-completed">已确认</span></td>
                        <td>⭐⭐⭐</td>
                        <td>2025-01-06 09:15</td>
                        <td>
                            <button class="btn btn-secondary" style="padding: 4px 8px; font-size: 12px;">查看</button>
                            <button class="btn btn-secondary" style="padding: 4px 8px; font-size: 12px;">编辑</button>
                        </td>
                    </tr>
                </tbody>
            </table>

            <!-- 分页区域 -->
            <div style="text-align: center; margin-top: 20px;">
                <div style="display: inline-block; background: white; padding: 15px 25px; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.05);">
                    <button class="btn btn-secondary">⏮️ 首页</button>
                    <button class="btn btn-secondary">⬅️ 上页</button>
                    <span style="margin: 0 15px; color: #64748b;">第 1 页，共 10 页</span>
                    <button class="btn btn-secondary">下页 ➡️</button>
                    <button class="btn btn-secondary">尾页 ⏭️</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 添加按钮点击效果
        document.querySelectorAll('.btn').forEach(button => {
            button.addEventListener('click', function(e) {
                // 创建波纹效果
                const ripple = document.createElement('span');
                const rect = this.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                const x = e.clientX - rect.left - size / 2;
                const y = e.clientY - rect.top - size / 2;
                
                ripple.style.cssText = `
                    position: absolute;
                    width: ${size}px;
                    height: ${size}px;
                    left: ${x}px;
                    top: ${y}px;
                    background: rgba(255, 255, 255, 0.5);
                    border-radius: 50%;
                    transform: scale(0);
                    animation: ripple 0.6s linear;
                    pointer-events: none;
                `;
                
                this.style.position = 'relative';
                this.style.overflow = 'hidden';
                this.appendChild(ripple);
                
                setTimeout(() => ripple.remove(), 600);
            });
        });

        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes ripple {
                to {
                    transform: scale(4);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>

﻿using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Data.SqlClient;

public partial class AddTask : System.Web.UI.Page
{

    int id;
    int tbxLongCharLength = 500;
    int tbxLongCharHeight = 120;
    public const int AddCount = 1;
    string strDescription;

    public struct structAdd
    {
        public int addIndex;
        public int addIndexCount;
        public string[,] addStr;//第一列存放userid, 第二列存放insertStr,第三列存放开始时间到的提醒

        public structAdd(int aIndex, int aIndexCount, string[,] aStr)
        {
            addIndex = aIndex;
            addIndexCount = aIndexCount;
            addStr = aStr;
        }
    }
    structAdd[] arrAdd = new structAdd[AddCount];

    public const string AETable = "Tasks";
    const string ConnStr = " Data Source=" + InputParams.DataBaseServer + "; uid=" + InputParams.userName + "; pwd=" + InputParams.password + "; DATABASE=" + InputParams.dbName;

    //iHM为0时，不生成时分控件，为1时，生成时分控件
    //int iHM = 0; 
    int iHM = 1;
    int[,] arrCycleType = new int[AddCount, 2];//该数组记录任务的重复性信息，第一个分量记录是否为周期性任务及哪种周期性任务，第二个分量记录周期数目
    public EnumField[][] EnumField()
    {
        EnumField[] enum0 ={ new EnumField(0, "日任务"), new EnumField(1, "周任务"), new EnumField(2, "月任务"), new EnumField(3, "年任务") };
        EnumField[] enum1 ={ new EnumField(0, "不重复"), new EnumField(1, "日重复"), new EnumField(2, "周重复"), new EnumField(3, "月重复"), new EnumField(4, "年重复") };
        EnumField[] enum2 ={ new EnumField(1, "一星级"), new EnumField(2, "二星级"), new EnumField(3, "三星级"), new EnumField(4, "四星级"), new EnumField(5, "五星级") };
        EnumField[][] efTs ={ enum0, enum1, enum2 };
        return efTs;
    }

    public Field[] InitFields()
    {
        Field fld0 = new Field(0, "TaskName", "任务名称", 0, EnumFieldType.charType, 0, 0, "", 0, "“任务名称”为必填项，没有填写！");
        Field fld1 = new Field(0, "Executor", "执行者", 0, EnumFieldType.numberType, 0, 0, "ChooseUser.aspx", 0, "");
        Field fld2 = new Field(0, "Type", "任务类型", 0, EnumFieldType.enumType, 0, 0, "", 0, "");
        Field fld3 = new Field(0, "Priority", "优先级", 0, EnumFieldType.enumType, 0, 2, "", 0, "");
        Field fld4 = new Field(0, "CycleType", "重复性", 0, EnumFieldType.enumType, 0, 1, "", 0, "");
        Field fld5 = new Field(0, "CycleCount", "重复数", 0, EnumFieldType.numberType, 0, 0, "", 0, "");
        Field fld6 = new Field(0, "PlanStartTime", "预计开始时间", 0, EnumFieldType.dateType, 0, 0, "", 0, "");
        Field fld7 = new Field(0, "PlanOverTime", "最后完成期限", 0, EnumFieldType.dateType, 0, 0, "", 0, "");
        Field fld8 = new Field(0, "PlanSpendTime", "预计用时", 0, EnumFieldType.numberType, 0, 0, "", 0, "");
        Field fld9 = new Field(0, "Description", "任务描述", 0, EnumFieldType.longcharType, 0, 0, "", 0, "");
        Field[] flds ={ fld0, fld1, fld2, fld3, fld4, fld5, fld6, fld7, fld8, fld9 };
        return flds;
    }

    protected void Page_Load(object sender, EventArgs e)
    {
        if (Request.QueryString["id"] != null)
        {
            id = int.Parse(Request.QueryString["id"]);
        }
        if (!Page.IsPostBack)
        {
            Session["AddCount"] = AddCount;
            for (int addIndex = 0; addIndex < AddCount; addIndex++)
            {
                string str1 = "tbxExecutor" + addIndex;
                string str2 = "tbxExecutor" + addIndex + "2";

                if (Request.Cookies["user"] != null)
                {
                    HttpCookie cookie = Request.Cookies["user"];
                    if (id != 1)//页面首次加载且不是从查找用户的页面返回
                    {
                        Session[str1] = cookie.Values["nickname"].ToString();
                        string[] strUser = new string[1]; //该数组存放登录者本人
                        strUser[0] = cookie.Values["user_id"].ToString();
                        Session[str2] = strUser;
                        string str4H1 = "ddlPlanStartTime" + addIndex + "H";
                        string str4M1 = "ddlPlanStartTime" + addIndex + "M";
                        Session[str4H1] = "08";
                        Session[str4M1] = "00";
                        string str4H2 = "ddlPlanOverTime" + addIndex + "H";
                        string str4M2 = "ddlPlanOverTime" + addIndex + "M";
                        Session[str4H2] = "19";
                        Session[str4M2] = "00";
                    }
                }
            }
        }
        GeneAddMould();
    }


    /// <summary>
    /// 添加任务
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void btnAdd_Click(object sender, EventArgs e)
    {
        GetInsertStr();
        try
        {
            Add();
            Response.Redirect("AddTask.aspx");
        }
        catch (SqlException ex)
        {
            Response.Write(ex.ToString());
        }
    }


    /// <summary>
    /// “点此查找”处理代码
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void lbt_Command(object sender, CommandEventArgs e)
    {
        Field[] fields = InitFields();
        EnumField[][] efTs = EnumField();
        string strTbxAdd = e.CommandName.ToString();
        string[] arrTbxAdd = strTbxAdd.Split('&');

        for (int addIndex = 0; addIndex < AddCount; addIndex++)
        {
            PlaceHolder pHolder = (PlaceHolder)div1.FindControl("placeHolder" + addIndex);

            Session["addIndex"] = int.Parse(arrTbxAdd[1].ToString());
            Session["AddCount"] = AddCount;
            for (int fieldIndex = 0; fieldIndex < fields.Length; fieldIndex++)
            {
                string strFldName = fields[fieldIndex].fieldName + addIndex;
                switch (fields[fieldIndex].fieldType)
                {
                    case EnumFieldType.numberType:

                        //string str31 = "tbx" + addIndex + "Year";
                        //Session[str31] = ((TextBox)(pHolder.FindControl(str31))).Text.Trim().ToString();

                        //string str32 = "tbx" + addIndex + "Month";
                        //Session[str32] = ((TextBox)(pHolder.FindControl(str32))).Text.Trim().ToString();

                        //string str33 = "tbx" + addIndex + "Week";
                        //Session[str33] = ((TextBox)(pHolder.FindControl(str33))).Text.Trim().ToString();

                        //string str34 = "tbx" + addIndex + "Day";
                        //Session[str34] = ((TextBox)(pHolder.FindControl(str34))).Text.Trim().ToString();

                        string str35 = "tbx" + addIndex + "Hour";
                        Session[str35] = ((TextBox)(pHolder.FindControl(str35))).Text.Trim().ToString();

                        string str36 = "tbx" + addIndex + "Minute";
                        Session[str36] = ((TextBox)(pHolder.FindControl(str36))).Text.Trim().ToString();
                        continue;

                    case EnumFieldType.enumType:
                        string str1 = "ddl" + strFldName;
                        DropDownList ddl = (DropDownList)pHolder.FindControl(str1);
                        Session[str1] = ddl.Text;
                        continue;

                    case EnumFieldType.boolType:
                        string str2 = "rb" + strFldName + "1";
                        if (((RadioButton)pHolder.FindControl(str2)).Checked == true)
                        {
                            Session[str2] = 1;
                        }
                        else
                        {
                            Session[str2] = 0;
                        }
                        continue;

                    case EnumFieldType.dateType:
                        string str3 = "tbx" + strFldName;
                        Session[str3] = ((TextBox)(pHolder.FindControl(str3))).Text.Trim().ToString();
                        string str3H = "ddl" + strFldName + "H";
                        string str3M = "ddl" + strFldName + "M";
                        Session[str3H] = ((DropDownList)(pHolder.FindControl(str3H))).Text.Trim().ToString();
                        Session[str3M] = ((DropDownList)(pHolder.FindControl(str3M))).Text.Trim().ToString();
                        continue;

                    case EnumFieldType.charType:
                        string str4 = "tbx" + strFldName;
                        Session[str4] = ((TextBox)(pHolder.FindControl(str4))).Text.Trim().ToString();
                        continue;


                }
            }
        }
        Session["supPage"] = "AddTask.aspx";
        string strDire = e.CommandArgument.ToString() + "?tbxName=" + arrTbxAdd[0].ToString();
        Response.Redirect(strDire);
    }

    /// <summary>
    /// 生成添加模板
    /// </summary>
    /// <param name="placeHolder"></param>
    /// <param name="fields"></param>
    public void GeneAddMould()
    {
        div1.Controls.Clear();
        Field[] fields = InitFields();
        EnumField[][] efTs = EnumField();

        for (int addIndex = 0; addIndex < AddCount; addIndex++)
        {
            PlaceHolder placeHolder = new PlaceHolder();
            placeHolder.ID = "placeHolder" + addIndex;
            div1.Controls.Add(placeHolder);
            PlaceHolder pHolder = (PlaceHolder)div1.FindControl("placeHolder" + addIndex);
            if (addIndex != 0)
            {
                Literal ltlHR = new Literal();
                ltlHR.Text = "<HR/>";
                placeHolder.Controls.Add(ltlHR);
            }

            //Literal ltlTag = new Literal();
            //ltlTag.Text = "第" + (addIndex + 1) + "条";
            //placeHolder.Controls.Add(ltlTag);

            for (int fieldIndex = 0; fieldIndex < fields.Length; fieldIndex++)
            {
                //生成换行符
                Label lbl1 = new Label();
                lbl1.Text = "<br/>";
                pHolder.Controls.Add(lbl1);

                if (fields[fieldIndex].fieldName == "Description")
                {
                    Literal ltlD = new Literal();
                    ltlD.Text = "<br/>";
                    placeHolder.Controls.Add(ltlD);
                }

                //生成字段标签，即显示名称
                Label lblTag = new Label();
                lblTag.Text = fields[fieldIndex].fieldShowName + "：" + " ";
                lblTag.ForeColor = System.Drawing.ColorTranslator.FromHtml(InputParams.lblFieldShowNameColor);
                pHolder.Controls.Add(lblTag);


                if (fields[fieldIndex].fieldName == "Description")
                {
                    Literal ltlD = new Literal();
                    ltlD.Text = "<br/>";
                    placeHolder.Controls.Add(ltlD);
                }
                string strFldName = fields[fieldIndex].fieldName + addIndex;
                switch (fields[fieldIndex].fieldType)
                {
                    case EnumFieldType.charType:
                        TextBox tbx1 = new TextBox();
                        string str1 = "tbx" + strFldName;
                        tbx1.ID = "tbx" + strFldName;
                        tbx1.Width = InputParams.tbxCharLength;
                        tbx1.Height = InputParams.tbxHeight;
                        tbx1.TextMode = TextBoxMode.SingleLine;
                        if (id == 1)
                        {
                            tbx1.Text = Session[str1].ToString();
                        }
                        pHolder.Controls.Add(tbx1);

                        if (fields[fieldIndex].attentionMessage != "")
                        {
                            Literal ltlX = new Literal();
                            ltlX.Text = "&nbsp;<b style='color :red; font-size :9px'  > ★</b>";
                            pHolder.Controls.Add(ltlX);
                        }

                        //if (fields[fieldIndex].attentionMessage != "")
                        //{
                        //    RequiredFieldValidator rfv = new RequiredFieldValidator();
                        //    rfv.ID = "rfv" + strFldName;
                        //    rfv.ErrorMessage = fields[fieldIndex].attentionMessage.ToString();
                        //    rfv.ControlToValidate = "tbx" + strFldName;
                        //    pHolder.Controls.Add(rfv);

                        //}
                        //((TextBox)pHolder.FindControl("tbx" + strFldName)).Attributes.Add("onMouseOver", "this.style.backgroundColor='" + InputParams.tbxOnmouseoverColor + "';");
                        //((TextBox)pHolder.FindControl("tbx" + strFldName)).Attributes.Add("onMouseOut", "this.style.backgroundColor='" + InputParams.tbxOnmouseoutColor + "';");
                        continue;

                    case EnumFieldType.longcharType:

                        if (fields[fieldIndex].fieldName != "Description")
                        {
                            TextBox tbx2 = new TextBox();
                            string str2 = "tbx" + strFldName;
                            tbx2.ID = "tbx" + strFldName;
                            tbx2.Width = tbxLongCharLength;
                            tbx2.Height = tbxLongCharHeight;
                            tbx2.TextMode = TextBoxMode.MultiLine;
                            //tbx2.Wrap = true;
                            //tbx2.Style.Add("overflow", "hidden");
                            if (id == 1)
                            {
                                tbx2.Text = Session[str2].ToString();
                            }
                            pHolder.Controls.Add(tbx2);
                            //((TextBox)pHolder.FindControl("tbx" + strFldName)).Attributes.Add("onMouseOver", "this.style.backgroundColor='" + InputParams.tbxOnmouseoverColor + "';");
                            //((TextBox)pHolder.FindControl("tbx" + strFldName)).Attributes.Add("onMouseOut", "this.style.backgroundColor='" + InputParams.tbxOnmouseoutColor + "';");
                        }
                        continue;


                    case EnumFieldType.numberType:
                        if (fields[fieldIndex].fieldName == "PlanSpendTime")
                        {

                            ////年
                            //TextBox tbxD1 = new TextBox();
                            //tbxD1.Width = 40;
                            //tbxD1.ID = "tbx" + addIndex + "Year";
                            //string strD1 = "tbx" + addIndex + "Year";
                            //if (id == 1)
                            //{
                            //    tbxD1.Text = Session[strD1].ToString();
                            //}
                            //placeHolder.Controls.Add(tbxD1);

                            //Literal litSP1 = new Literal();
                            //litSP1.Text = "年 ";
                            //litSP1.ID = "ltl" + addIndex + "Year";
                            //placeHolder.Controls.Add(litSP1);

                            ////月
                            //TextBox tbxD2 = new TextBox();
                            //tbxD2.Width = 40;
                            //tbxD2.ID = "tbx" + addIndex + "Month";
                            //string strD2 = "tbx" + addIndex + "Month";
                            //if (id == 1)
                            //{
                            //    tbxD2.Text = Session[strD2].ToString();
                            //}
                            //placeHolder.Controls.Add(tbxD2);

                            //Literal litSP2 = new Literal();
                            //litSP2.Text = "月 ";
                            //litSP2.ID = "ltl" + addIndex + "Month";
                            //placeHolder.Controls.Add(litSP2);

                            ////周
                            //TextBox tbxD3 = new TextBox();
                            //tbxD3.Width = 40;
                            //tbxD3.ID = "tbx" + addIndex + "Week";
                            //string strD3 = "tbx" + addIndex + "Week";
                            //if (id == 1)
                            //{
                            //    tbxD3.Text = Session[strD3].ToString();
                            //}
                            //placeHolder.Controls.Add(tbxD3);

                            //Literal litSP3 = new Literal();
                            //litSP3.Text = "周 ";
                            //litSP3.ID = "ltl" + addIndex + "Week";
                            //placeHolder.Controls.Add(litSP3);


                            ////日
                            //TextBox tbxD4 = new TextBox();
                            //tbxD4.Width = 40;
                            //tbxD4.ID = "tbx" + addIndex + "Day";
                            //string strD4 = "tbx" + addIndex + "Day";
                            //if (id == 1)
                            //{
                            //    tbxD4.Text = Session[strD4].ToString();
                            //}
                            //placeHolder.Controls.Add(tbxD4);

                            //Literal litSP4 = new Literal();
                            //litSP4.Text = "日 ";
                            //litSP4.ID = "ltl" + addIndex + "Day";
                            //placeHolder.Controls.Add(litSP4);


                            //时
                            TextBox tbxD5 = new TextBox();
                            tbxD5.Width = 40;
                            tbxD5.ID = "tbx" + addIndex + "Hour";
                            string strD5 = "tbx" + addIndex + "Hour";
                            if (id == 1)
                            {
                                tbxD5.Text = Session[strD5].ToString();
                            }
                            placeHolder.Controls.Add(tbxD5);

                            Literal litSP5 = new Literal();
                            litSP5.Text = "小时 ";
                            litSP5.ID = "ltl" + addIndex + "Hour";
                            placeHolder.Controls.Add(litSP5);


                            //分
                            TextBox tbxD6 = new TextBox();
                            tbxD6.Width = 40;
                            tbxD6.ID = "tbx" + addIndex + "Minute";
                            string strD6 = "tbx" + addIndex + "Minute";
                            if (id == 1)
                            {
                                tbxD6.Text = Session[strD6].ToString();
                            }
                            placeHolder.Controls.Add(tbxD6);

                            Literal litSP6 = new Literal();
                            litSP6.Text = "分钟 ";
                            litSP6.ID = "ltl" + addIndex + "Minute";
                            placeHolder.Controls.Add(litSP6);

                        }
                        else
                        {
                            TextBox tbx3 = new TextBox();
                            string str3 = "tbx" + strFldName;
                            tbx3.ID = "tbx" + strFldName;
                            tbx3.Width = InputParams.tbxNumberLength;
                            tbx3.Height = InputParams.tbxHeight;
                            tbx3.Text = "";
                            if (fieldIndex == 1)
                            {
                                if (id == 1)
                                {
                                    string[] arrReturnField = (string[])(Session[str3]);

                                    for (int iReturnIndex = 0; iReturnIndex < arrReturnField.Length; iReturnIndex++)
                                    {
                                        tbx3.Text += arrReturnField[iReturnIndex].ToString() + " ";
                                    }
                                    //tbx3.Text = Session[str3].ToString();

                                }
                                else
                                {
                                    tbx3.Text = Session[str3].ToString();

                                }
                            }
                            pHolder.Controls.Add(tbx3);
                            //((TextBox)pHolder.FindControl("tbx" + strFldName)).Attributes.Add("onMouseOver", "this.style.backgroundColor='" + InputParams.tbxOnmouseoverColor + "';");
                            //((TextBox)pHolder.FindControl("tbx" + strFldName)).Attributes.Add("onMouseOut", "this.style.backgroundColor='" + InputParams.tbxOnmouseoutColor + "';");
                            if (fields[fieldIndex].linkPage.ToString() != "")
                            {
                                LinkButton lbt = new LinkButton();
                                lbt.Text = "  点此查找执行者";
                                lbt.ID = "lbt" + strFldName;
                                lbt.Command += new CommandEventHandler(lbt_Command);
                                lbt.Command += new CommandEventHandler(lbt_Command);
                                lbt.CommandName = "tbx" + fields[fieldIndex].fieldName + "&" + addIndex;//添加多个任务
                                lbt.CommandArgument = fields[fieldIndex].linkPage.Trim().ToString();
                                pHolder.Controls.Add(lbt);
                            }
                        }
                        continue;

                    case EnumFieldType.dateType:
                        TextBox tbx4 = new TextBox();
                        string str4 = "tbx" + strFldName;
                        tbx4.Text = DateTime.Now.AddDays(1).ToShortDateString().ToString();
                        tbx4.ID = "tbx" + strFldName;
                        tbx4.Width = InputParams.tbxDateLength;
                        tbx4.Height = InputParams.tbxHeight;
                        if (id == 1)
                        {
                            tbx4.Text = Session[str4].ToString();
                        }
                        pHolder.Controls.Add(tbx4);
                        ((TextBox)pHolder.FindControl("tbx" + strFldName)).Attributes.Add("onclick", "javascript:calendar()");
                        ((TextBox)pHolder.FindControl("tbx" + strFldName)).Attributes.Add("onclick", "javascript:calendar()");

                        string str4H = "ddl" + strFldName + "H";
                        string str4M = "ddl" + strFldName + "M";

                        if (iHM == 1)
                        {
                            Literal ltlSpace1 = new Literal();
                            ltlSpace1.Text = "&nbsp;";
                            placeHolder.Controls.Add(ltlSpace1);

                            DropDownList ddlH = new DropDownList();
                            ddlH.ID = "ddl" + strFldName + "H";
                            ddlH.Width = InputParams.ddlTimeWidth;
                            for (int iH = 0; iH < 24; iH++)
                            {
                                ListItem li = new ListItem();
                                li.Text = iH.ToString();
                                li.Value = iH.ToString();
                                if ((Session[str4H] != null) && (int.Parse(Session[str4H].ToString()) == iH))
                                {
                                    li.Selected = true;

                                }
                                ddlH.Items.Add(li);
                            }

                            placeHolder.Controls.Add(ddlH);

                            Label lblH = new Label();
                            lblH.Text = "时";
                            placeHolder.Controls.Add(lblH);

                            DropDownList ddlM = new DropDownList();
                            ddlM.ID = "ddl" + strFldName + "M";
                            ddlM.Width = InputParams.ddlTimeWidth;

                            for (int iM = 0; iM < 60; iM++)
                            {
                                ListItem li = new ListItem();
                                li.Text = iM.ToString();
                                li.Value = iM.ToString();
                                if ((Session[str4M] != null) && (int.Parse(Session[str4M].ToString()) == iM))
                                {
                                    li.Selected = true;
                                }
                                ddlM.Items.Add(li);
                            }

                            placeHolder.Controls.Add(ddlM);

                            Label lblM = new Label();
                            lblM.Text = "分";
                            placeHolder.Controls.Add(lblM);
                        }
                        continue;

                    case EnumFieldType.boolType:
                        RadioButton rbY = new RadioButton();
                        RadioButton rbN = new RadioButton();
                        string strY = "rb" + strFldName + "1";
                        string strN = "rb" + strFldName + "2";
                        rbY.ID = "rb" + strFldName + "1";
                        rbY.GroupName = "gn" + strFldName;
                        rbY.Text = "是";
                        rbY.ForeColor = System.Drawing.ColorTranslator.FromHtml(InputParams.rbYNColor);
                        rbN.ID = "rb" + strFldName + "2";
                        rbN.GroupName = "gn" + strFldName;
                        rbN.Text = "否";
                        rbN.ForeColor = System.Drawing.ColorTranslator.FromHtml(InputParams.rbYNColor);
                        pHolder.Controls.Add(rbY);
                        pHolder.Controls.Add(rbN);

                        if (id == 1)
                        {
                            if (Session[strY].ToString() != null)
                            {
                                if (Session[strY].ToString() == "1")
                                {
                                    ((RadioButton)pHolder.FindControl(strY)).Checked = true;
                                }
                                else
                                {
                                    ((RadioButton)pHolder.FindControl(strN)).Checked = true;

                                }
                            }
                        }
                        continue;

                    case EnumFieldType.picType:
                        TextBox tbx5 = new TextBox();
                        string str5 = "tbx" + strFldName;
                        tbx5.ID = "tbx" + strFldName;
                        tbx5.Width = InputParams.tbxCharLength;
                        tbx5.Height = InputParams.tbxHeight;
                        tbx5.TextMode = TextBoxMode.MultiLine;
                        tbx5.Wrap = true;
                        tbx5.Style.Add("overflow", "hidden");

                        if (id == 1)
                        {
                            tbx5.Text = Session[str5].ToString();
                        }
                        pHolder.Controls.Add(tbx5);
                        //((TextBox)pHolder.FindControl("tbx" + strFldName)).Attributes.Add("onMouseOver", "this.style.backgroundColor='" + InputParams.tbxOnmouseoverColor + "';");
                        //((TextBox)pHolder.FindControl("tbx" + strFldName)).Attributes.Add("onMouseOut", "this.style.backgroundColor='" + InputParams.tbxOnmouseoutColor + "';");
                        continue;

                    case EnumFieldType.enumType:
                        DropDownList ddl = new DropDownList();
                        string str6 = "ddl" + strFldName;
                        ddl.ID = "ddl" + strFldName;
                        int enumT = fields[fieldIndex].enumTag;
                        ddl.Width = InputParams.ddlWidth;
                        ddl.AutoPostBack = false;

                        for (int enumLen = 0; enumLen < efTs[enumT].Length; enumLen++)
                        {
                            ListItem li = new ListItem();
                            li.Value = efTs[enumT][enumLen].enumItem.ToString();
                            li.Text = efTs[enumT][enumLen].itemDetail;
                            if (id == 1)
                            {
                                if ((Session[str6] != null) && (efTs[enumT][enumLen].enumItem.ToString() == Session[str6].ToString()))
                                {
                                    li.Selected = true;
                                }
                            }
                            ddl.Items.Add(li);
                        }
                        pHolder.Controls.Add(ddl);
                        //((DropDownList)pHolder.FindControl("ddl" + strFldName)).Attributes.Add("onMouseOver", "this.style.backgroundColor='" + InputParams.tbxOnmouseoverColor + "';");
                        //((DropDownList)pHolder.FindControl("ddl" + strFldName)).Attributes.Add("onMouseOut", "this.style.backgroundColor='" + InputParams.tbxOnmouseoutColor + "';");
                        continue;
                }
            }
        }
    }



    /// <summary>
    /// 获得添加记录的SQL插入字符串
    /// </summary>
    /// <param name="placeHolder"></param>
    /// <param name="fields"></param>
    /// <returns></returns>
    public void GetInsertStr()
    {
        Field[] fields = InitFields();
        EnumField[][] efTs = EnumField();
        string strTaskName = "";
        string strNoteInfo1;
        //string strNoteInfo2;
        string strPlanStartTime;
        //string strPlanOverTime;

        #region addIndex
        for (int addIndex = 0; addIndex < AddCount; addIndex++)
        {
            PlaceHolder pHolder = (PlaceHolder)div1.FindControl("placeHolder" + addIndex);

            string strEID = "tbxExecutor" + addIndex;
            int arrLength = 0;

            if (Session[strEID] != null)
            {

                string[] arrKey = (string[])(Session[strEID + "2"]);
                arrLength = arrKey.Length;
                arrAdd[addIndex].addIndex = addIndex;
                arrAdd[addIndex].addIndexCount = arrKey.Length;
                string[,] aStr = new string[arrLength, 3];

                #region forIreturnIndex
                for (int iReturnIndex = 0; iReturnIndex < arrLength; iReturnIndex++)
                {
                    string strIns = "INSERT INTO " + AETable + "( CreateTime,";
                    string strVal = " VALUES ( '" + DateTime.Now.ToString() + "',";

                    for (int fieldIndex = 0; fieldIndex < fields.Length; fieldIndex++)
                    {
                        string strFldName = fields[fieldIndex].fieldName + addIndex;
                        if (fieldIndex != 0)
                        {
                            strIns += "," + fields[fieldIndex].fieldName;
                            if (fieldIndex != fields.Length)
                            {
                                strVal += ",";
                            }
                        }
                        else
                        {
                            strIns += fields[fieldIndex].fieldName;
                        }

                        switch (fields[fieldIndex].fieldType)
                        {
                            case EnumFieldType.numberType:
                                if (fields[fieldIndex].fieldName != "PlanSpendTime")
                                {
                                    if (((TextBox)pHolder.FindControl("tbx" + strFldName)).Text != "")
                                    {
                                        if (Session["tbx" + strFldName + "2"] != null)
                                        {
                                            string[] akey = (string[])(Session[strEID + "2"]);
                                            strVal += int.Parse(akey[iReturnIndex].ToString());
                                            if (fieldIndex == 1) //该字段为执行者
                                            {
                                                aStr[iReturnIndex, 0] = akey[iReturnIndex].ToString();
                                            }
                                        }

                                    }

                                    else
                                    {
                                        strVal += "' '";
                                    }
                                }

                                else
                                {
                                    try
                                    {
                                        //int iYear = 0;
                                        //TextBox tbxYear = (TextBox)(pHolder.FindControl("tbx" + addIndex + "Year"));
                                        //if (tbxYear.Text.ToString() != "")
                                        //{
                                        //    iYear = int.Parse(tbxYear.Text.ToString());
                                        //}
                                        //int iMonth = 0;
                                        //TextBox tbxMonth = (TextBox)(pHolder.FindControl("tbx" + addIndex + "Month"));
                                        //if (tbxMonth.Text.ToString() != "")
                                        //{
                                        //    iMonth = int.Parse(tbxMonth.Text.ToString());
                                        //}
                                        //int iWeek = 0;
                                        //TextBox tbxWeek = (TextBox)(pHolder.FindControl("tbx" + addIndex + "Week"));
                                        //if (tbxWeek.Text.ToString() != "")
                                        //{
                                        //    iWeek = int.Parse(tbxWeek.Text.ToString());
                                        //}
                                        //int iDay = 0;
                                        //TextBox tbxDay = (TextBox)(pHolder.FindControl("tbx" + addIndex + "Day"));
                                        //if (tbxDay.Text.ToString() != "")
                                        //{
                                        //    iDay = int.Parse(tbxDay.Text.ToString());
                                        //}
                                        int iHour = 0;
                                        TextBox tbxHour = (TextBox)(pHolder.FindControl("tbx" + addIndex + "Hour"));
                                        if (tbxHour.Text.ToString() != "")
                                        {
                                            iHour = int.Parse(tbxHour.Text.ToString());
                                        }
                                        int iMinute = 0;
                                        TextBox tbxMinute = (TextBox)(pHolder.FindControl("tbx" + addIndex + "Minute"));
                                        if (tbxMinute.Text.ToString() != "")
                                        {
                                            iMinute = int.Parse(tbxMinute.Text.ToString());
                                        }

                                        int spendTime = 0;
                                        spendTime = iHour * 60 + iMinute;//以分钟为单位
                                        //spendTime = (iYear * 365 * 24 + iMonth * 30 * 24 + iWeek * 7 * 24 + iDay * 24 + iHour) * 60 + iMinute;//以分钟为单位

                                        strVal += spendTime;
                                    }
                                    catch
                                    {
                                        ClientScript.RegisterClientScriptBlock(this.GetType(), "info", "<script language=\"javascript\" type=\"text/javascript\">alert(\"请检查你的输入格式是否正确！\")</script>");

                                    }

                                }
                                continue;


                            case EnumFieldType.dateType:
                                if (((TextBox)pHolder.FindControl("tbx" + strFldName)).Text != "")
                                {
                                    string strDataTime = ((TextBox)pHolder.FindControl("tbx" + strFldName)).Text.Trim().ToString();

                                    if (iHM == 1)
                                    {
                                        DropDownList ddlH = (DropDownList)(pHolder.FindControl("ddl" + strFldName + "H"));
                                        DropDownList ddlM = (DropDownList)(pHolder.FindControl("ddl" + strFldName + "M"));
                                        string strH = "00";
                                        if (ddlH.Text != "")
                                        {
                                            strH = ddlH.Text.Trim().ToString();
                                        }
                                        string strM = "00";
                                        if (ddlM.Text != "")
                                        {
                                            strM = ddlM.Text.Trim().ToString();
                                        }
                                        strDataTime += " " + strH + ":" + strM + ":" + "00";
                                    }

                                    if (fields [fieldIndex ].fieldName =="PlanStartTime")
                                    {
                                        strPlanStartTime = strDataTime;
                                        strNoteInfo1 = "任务：" + strTaskName + "，开始时间已到，请开始执行！";
                                        aStr[iReturnIndex, 2] = strNoteInfo1 + "&" + strPlanStartTime;
                                    }

                                    //任务最后完成期限到的提醒被取消
                                    //if (fieldIndex == 6)
                                    //{
                                    //    strPlanOverTime = strDataTime;
                                    //    strNoteInfo2 = "任务：" + strTaskName + "，结束时间到了，请提交汇报！";
                                    //    strInserts[addIndex, 2] = strNoteInfo2 + "&" + strPlanOverTime;
                                    //}

                                    DateTime dt = DateTime.Parse(strDataTime);
                                    strVal += "'" + dt.ToString() + "'";
                                }
                                continue;

                            case EnumFieldType.enumType:
                                string strDdl = ((DropDownList)pHolder.FindControl("ddl" + strFldName)).Text.Trim();
                                int iValue = 0;
                                string strText;
                                int enumT = fields[fieldIndex].enumTag;
                                for (int enumLen = 0; enumLen < efTs[enumT].Length; enumLen++)
                                {
                                    iValue = efTs[enumT][enumLen].enumItem;
                                    strText = efTs[enumT][enumLen].itemDetail;
                                    if (strDdl == iValue.ToString())
                                    {
                                        break;
                                    }
                                }
                                strVal += iValue.ToString();
                                continue;

                            case EnumFieldType.boolType:

                                if (((RadioButton)pHolder.FindControl("rb" + strFldName + "1")).Checked)
                                {
                                    strVal += "1";
                                }
                                else
                                {
                                    strVal += "0";
                                }
                                continue;

                            default:

                                if (fields[fieldIndex].fieldName == "Description")
                                {
                                    strDescription = Body.Value.Trim();
                                    if (strDescription != "")
                                    {
                                        strDescription = strDescription.Replace("</P>", "");
                                        strDescription = strDescription.Replace("<P>", "");
                                        strDescription = strDescription.Replace("\r\n", "<br/>");
                                    }
                                    strVal += "'" + strDescription + "'";
                                }
                                else

                                    if (((TextBox)pHolder.FindControl("tbx" + strFldName)).Text != "")
                                    {
                                        if (fieldIndex == 0)
                                        {
                                            strTaskName = ((TextBox)pHolder.FindControl("tbx" + strFldName)).Text.Trim();
                                        }
                                        strVal += "'" + ((TextBox)pHolder.FindControl("tbx" + strFldName)).Text.Trim() + "'";
                                    }
                                    else
                                    {
                                        strVal += "' '";
                                    }
                                continue;
                        }
                    }
                    strIns += ")";
                    strVal += ")";
                    aStr[iReturnIndex, 1] = strIns + strVal + " SELECT @@IDENTITY";
                    arrAdd[addIndex].addStr = aStr;
                }


                #endregion forIreturnIndex
            }
            else //选择的执行者为空
            {

            }
        }
        #endregion addIndex
    }


    /// <summary>
    /// 向数据库添加记录
    /// </summary>
    /// <param name="strInsert"></param>
    public void Add()
    {
        SqlConnection conn = new SqlConnection(ConnStr);

        if (conn.State.ToString() == "Closed")
        {
            conn.Open();
        }

        SqlDataAdapter da;
        DataSet ds;
        string strSql = "";
        int taskID = 0;
        int userID = 0;

        for (int addIndex = 0; addIndex < AddCount; addIndex++)
        {
            for (int iReturnField = 0; iReturnField < arrAdd[addIndex].addIndexCount; iReturnField++)
            {
                ds = new DataSet();
                strSql = arrAdd[addIndex].addStr[iReturnField, 1];
                da = new SqlDataAdapter(strSql, conn);

                da.Fill(ds);
                taskID = 0;
                userID = int.Parse(arrAdd[addIndex].addStr[iReturnField, 0].ToString());

                string[] noteContents1 = arrAdd[addIndex].addStr[iReturnField, 2].ToString().Split('&');
                string noteContent1 = noteContents1[0].ToString();
                DateTime dt1 = DateTime.Parse(noteContents1[1].ToString());


                //最后完成期限的提醒被取消
                //string[] noteContents2 = strInserts[addIndex, 2].ToString().Split('&');
                //string noteContent2 = noteContents2[0].ToString();
                //DateTime dt2 = DateTime.Parse(noteContents2[1].ToString());

                int sortID = 3;
                int noteState = 0;
                int nCount = ds.Tables[0].Rows.Count;
                if (nCount != 0)
                {
                    taskID = int.Parse(Convert.ToString(ds.Tables[0].Rows[0][0]));
                }
                if (taskID != 0)
                {
                    string strAddNote1 = "INSERT INTO eventStateHanding (user_id,inner_sort_id,sort_id,sortName,bDealed,eventTime) VALUES(";
                    strAddNote1 += userID + "," + taskID + "," + sortID + ",'" + noteContent1 + "'," + noteState + ",'" + dt1 + "')";

                    //string strAddNote2 = "INSERT INTO eventStateHanding (user_id,inner_sort_id,sort_id,sortName,bDealed,eventTime) VALUES(";
                    //strAddNote2 += userID + "," + taskID + "," + sortID + ",'" + noteContent2 + "'," + noteState + ",'" + dt2 + "')";

                    string strSqlCheck = "select * from eventStateHanding where user_id =" + userID + " and inner_sort_id = " + taskID + " and sort_id=" + sortID;
                    SqlCommand comm = new SqlCommand(strSqlCheck, conn);
                    SqlDataReader dr = comm.ExecuteReader();
                    if (!dr.Read())
                    {
                        dr.Close();

                        SqlCommand comm1 = new SqlCommand(strAddNote1, conn);
                        comm1.ExecuteNonQuery();
                        //SqlCommand comm2 = new SqlCommand(strAddNote2, conn);
                        //comm2.ExecuteNonQuery();

                    }

                }

            }

        }

        if (conn.State.ToString() == "Open")
        {
            conn.Close();
        }
    }
}
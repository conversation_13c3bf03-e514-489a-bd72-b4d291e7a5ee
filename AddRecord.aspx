﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="AddRecord.aspx.cs" Inherits="AddRecord" %>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html xmlns="http://www.w3.org/1999/xhtml" >
<head runat="server">
    <title>添加跟踪记录</title>
    <meta name="viewport" content="width=device-width,height=device-height,initial-scale=0.65,user-scalable=no">
    <style type="text/css">


        .textArea {
            width:450px;
        }
        @media (min-width: 769px) {
        body {
            position: relative;
            left: 26%;
        }
        #addReTable {
           width: 50%;
           vertical-align: middle;
        }
        #txtDays {
            font-size: 16.5px;
        }
        #lblResult {
           font-size: 19.5px;
        }
        }
        @media (max-width: 768px) {
        body, table, tr, td {
            font-size: 22px;
            font-family: 宋体;
            max-width: 100%;
            overflow-x: auto;
            margin:0.2% 0 0 0;
/*            margin-top:0.2%;*/
            /* width:350px;*/ 
            position: relative;
        }
        #addReTable {
                width: 100%;
        }
        .textArea {
            width: 98%;
            max-width: 100vw;  
        }
        .time-input {
           font-size: 16px;
        }
        #TextBox1 {
           font-size: 22px;

        }
        #txtDays {
            font-size: 19px;
        }
        #lblResult {
           font-size: 23px;
        }
        #imageUpload {
            display:none;
        }
        .preview-item {
            width: 90%;  
        }
       .preview-item img {
            width: 50%;
            height: 50%;
        }
/*        #Button1 {
            font-size: 13pt; 
            margin: 2px;
            width: 100px; 
            height: 30px;
        }*/
        }
</style>
</head>

<script type="text/javascript" src="js/time.js"></script>
<script type="text/javascript"> 
        // 全局变量
		let imageDataList = [];
		let currentFiles = [];

		// 初始化 - 等待DOM加载完成
		document.addEventListener('DOMContentLoaded', function() {
            // 获取DOM元素
        const imageUpload = document.getElementById('imageUpload');
		const imagePreview = document.getElementById('imagePreview');
		const statusText = document.getElementById('statusText');
		const btnSubmit = document.getElementById('btnSubmit');
			const hdnImageData =document.getElementById('hdnImageData');

		// 文件选择事件
		imageUpload.addEventListener('change', function(e) {
                const files = Array.from(e.target.files);

		// 清除无效选择
		if (files.length === 0) return;

		// 重置状态文字
		if (statusText) statusText.style.display = 'none';

        // 处理每个文件
        files.forEach(file => {
            // 验证文件类型和大小
            if (!file.type.match('image.*')) {
			    alert(`文件 ${file.name} 不是图片类型`);
		        return;
            }
                    
            if (file.size > 5 * 1024 * 1024) {
			    alert(`文件 ${file.name} 超过5MB大小限制`);
		        return;
            }

		// 添加到当前文件列表
		currentFiles.push(file);

		const reader = new FileReader();

		reader.onload = function(e) {
        // 创建预览元素
        const previewItem = document.createElement('div');
		previewItem.className = 'preview-item';

            const img = document.createElement('img');

		img.src = e.target.result;

		// 关键修改：添加图片尺寸控制样式
		//img.style.maxWidth = '250px';      // 最大宽度限制
		//img.style.maxHeight = '250px';     // 最大高度限制
		//img.style.objectFit = 'contain';   // 保持比例完整显示图片
        //img.style.borderRadius = '4px';    // 可选：添加圆角美化

		const removeBtn = document.createElement('div');
		removeBtn.className = 'remove-btn';
		removeBtn.innerHTML = '×';

		// 移除图片事件
		removeBtn.onclick = function() {
			// 从预览中移除
			previewItem.remove();
            // 从数组中移除
            const index = currentFiles.findIndex(f => f.name === file.name);
		    if (index !== -1) {
			    currentFiles.splice(index, 1);
		    imageDataList.splice(index, 1);
                                }

		    // 如果没有图片了，显示状态文字
		    if (currentFiles.length === 0 && statusText) {
			    statusText.style.display = 'block';
                                }

		    // 更新隐藏字段
		    updateHiddenField();
        };

		previewItem.appendChild(img);
		previewItem.appendChild(removeBtn);
		imagePreview.appendChild(previewItem);
		// 存储Base64数据
		const base64Data = e.target.result.split(',')[1];
		imageDataList.push({
			name: file.name,
	        type: file.type,
	        data: base64Data
                        });

		// 更新隐藏字段
		updateHiddenField();
                    };
		reader.readAsDataURL(file);
        });

		// 重置文件输入
		e.target.value = '';
        });

		// 提交按钮事件
		btnSubmit.addEventListener('click', function() {
                // 显示加载状态
                const originalText = btnSubmit.innerHTML;
		btnSubmit.innerHTML = '提交中...';
		btnSubmit.disabled = true;

                // 在实际应用中，这里应该是表单提交或AJAX请求
                // 例如: document.forms[0].submit();

                // 模拟提交到后端
        setTimeout(() => {
			alert('随访记录提交成功！');

		// 重置UI
		btnSubmit.innerHTML = originalText;
		btnSubmit.disabled = false;

		// 清空图片相关数据
		imagePreview.innerHTML = '';
		currentFiles = [];
		imageDataList = [];
		updateHiddenField();

		// 显示状态文字
		if (statusText) {
			statusText.style.display = 'block';
                    }
                }, 1000);
            });

		// 更新隐藏字段的函数
		function updateHiddenField() {
                // 将图片数据转为JSON字符串
             const jsonData = JSON.stringify(imageDataList);
		// 更新隐藏字段的值
		document.getElementById('<%= hdnImageData.ClientID %>').value = jsonData;
            // 在实际应用中，您可以直接提交表单
            // 隐藏字段会随表单一起提交到后端
        }
        });
</script>
<body>
    <form id="form1" runat="server">
    <div>
          <table id="addReTable"  style="background-color: #E5FDFE;vertical-align: middle;">
              <tr align="center">
                <td style="text-align: center; height: 50px;">
                    <asp:Label ID="Label1" runat="server" Text="请输入跟踪的内容"></asp:Label><br />
                </td>
              </tr>
              <tr align="center">
                 <td>
                     <asp:TextBox ID="TextBox1" runat="server" Height="90px" TextMode="MultiLine" CssClass="textArea" ></asp:TextBox><br />
                     <%--<div class="upload-container">
                        <input type="file" id="imageUpload" accept="image/*" multiple style="display:none" />
                        <button type="button" class="btn-upload" onclick="document.getElementById('imageUpload').click()">
                            上传图片
                        </button>
                        <div class="image-preview" id="imagePreview"></div>
                        <asp:HiddenField ID="hdnImageData" runat="server" />
                    </div>--%>
                     <div class="upload-container">
                        <input type="file" id="imageUpload" accept="image/*" multiple class="hidden-field" />
                        <button type="button" class="btn-upload" onclick="document.getElementById('imageUpload').click()">
                            上传图片
                        </button>
                        <div class="file-info">支持 JPG/PNG 格式，每张不超过 5MB</div>
                        <div class="image-preview" id="imagePreview">
                            <div class="status-text" id="statusText">暂无图片，请上传照片</div>
                        </div>
                
                        <!-- 隐藏字段用于存储图片数据 -->
                        <asp:HiddenField ID="hdnImageData" runat="server" />
                    </div>
                 </td>
              </tr>
              <tr align="center">
                  <td>
                      <asp:Label ID="Label2" runat="server" Text="计划"></asp:Label>
<%--                      <asp:TextBox ID="FollowTime" runat="server" CssClass="time-input" Width="130px"></asp:TextBox>--%>
                      <asp:TextBox ID="txtDays" runat="server" AutoPostBack="True" OnTextChanged="txtDays_TextChanged" Width="50px" ></asp:TextBox>
                      <asp:Label ID="Label3" runat="server" Text="天后跟踪"></asp:Label>
                      <asp:Label ID="lblError" runat="server" ForeColor="Red" Visible="false" Text="此项必填！"></asp:Label>
                      <br />
                      <asp:Label ID="lblResult" runat="server" ForeColor="blue" Font-Bold="true" ></asp:Label>
                      <br />
                     
                  </td>              
              </tr>
              <tr align="center">
                  <td>
                       如：2009-1-18 15:01:02<br /><a style="color:Blue">(如不填写，则默认为当前时间)</a>
                  </td>
              </tr>
              <tr align="center">
                  <td>
<%--                       <asp:Label ID="Label5" runat="server" Text="计划下次跟踪时间:"></asp:Label>--%>
<%--                      <asp:TextBox ID="TextBox2" runat="server" Width="120px"></asp:TextBox>--%>
  <%--                     <asp:Label ID="Label6" runat="server" Text="天后"></asp:Label><br />--%>
<%--                      <asp:TextBox ID="txtDays" runat="server" AutoPostBack="True" OnTextChanged="txtDays_TextChanged"></asp:TextBox>
                      <asp:Label ID="lblResult" runat="server"></asp:Label>--%>
                  </td>
                  
              </tr>
              <tr align="center">
                  <td>
                      <span>
                       <%--<asp:LinkButton ID="Button1" runat="server" OnClick="Button1_Click" Text="添加" 
                           Style="text-decoration: none; border: 1px solid #871f78; padding: 2px 8px; display: inline-block; border-radius: 10px;
                            background-color: #E5FDFE; ">添加</asp:LinkButton>--%>
                       <asp:LinkButton ID="Button1" runat="server" OnClick="btnSubmit_Click" Text="添加" 
                           Style="text-decoration: none; border: 1px solid #871f78; padding: 2px 8px; display: inline-block; border-radius: 10px;
                            background-color: #E5FDFE; ">添加</asp:LinkButton>
                      </span>
                      &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                      <asp:LinkButton ID="LinkButton1" runat="server" Font-Underline="false" PostBackUrl="~/Default.aspx"
                          Style="text-decoration: none; border: 1px solid #871f78; padding: 2px 8px; display: inline-block; border-radius: 10px;
                            background-color: #E5FDFE; ">返回</asp:LinkButton>
                  </td>                  
              </tr>
             </table>
            </div>      

    </form>
</body>
</html>

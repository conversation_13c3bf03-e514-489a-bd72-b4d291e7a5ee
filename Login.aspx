﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="Login.aspx.cs" Inherits="Login" %>

<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>客户跟踪系统用户登录</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0,user-scalable=no">
    <%--user-scalable=no 禁止手动缩放属性--%>
    <style type="text/css">
        body, table, tr, td {

            font-size: 12px;
            font-family: 宋体;

        }

    @media (max-width: 768px) {

        body, table, tr, td {
            font-size: 12px;
            font-family: 宋体;
            width: 95%;
            height:40%;
            overflow-x: hidden;
            margin-top:28%;
            margin-bottom:10%;
            margin-left:-12%;
            position: relative;
            padding-top:4%;
        }
    }

   
        

    </style>
</head>
<body>
    <form id="form1" method="post" runat="server">
        <table cellpadding="1" cellspacing="0" border="0" width="40%" style="position: absolute; background-color: #F7F7F7; margin-top: 100px; left: 30%">
            <tr  style="position: relative;border: none;top: -14px; background-color: #cccccc; font-size: 35px; height: 45px; font-weight: bold">
                <td align="center" colspan="2"  style="font-size: medium;font-family: 幼圆">客户跟踪系统</td>
            </tr>
            <tr >
                <td align="center" valign="middle" style="width: 30%; border: 0px">手机或Email：</td>
                <td align="left" valign="middle" style="right:15%;border: 0px">
                    <asp:TextBox ID="tbxUserName" CssClass="input_common" runat="server"></asp:TextBox><br />
                    <asp:RequiredFieldValidator ID="RequiredFieldValidator1" runat="server" ControlToValidate="tbxUserName"
                        ErrorMessage="请输入手机号码或Email!"></asp:RequiredFieldValidator></td>
            </tr>
            <tr>
                <td align="center" valign="middle" style="border: 0px">密&nbsp;&nbsp;&nbsp;码：</td>
                <td align="left" valign="middle" style="right:15%;border: 0px">
                    <asp:TextBox ID="tbxPassword" TextMode="password" CssClass="input_common" runat="server"></asp:TextBox><br />
                    <asp:RequiredFieldValidator ID="RequiredFieldValidator2" runat="server" ControlToValidate="tbxPassword"
                        ErrorMessage="请输入密码！"></asp:RequiredFieldValidator></td>
            </tr>
            <tr>
                <%--<td align="left" style="border: 0px">&nbsp;
                </td>--%>
                <td align="center" colspan="2" style="border: 0px">
                    <asp:CheckBox ID="ckbAutoLogin" Text="自动登录" runat="server" />
                    &nbsp;<asp:Button Text="登录" ID="btnLogin" runat="server" Width="70" Height="30" OnClick="btnLogin_Click" />
                    <asp:HyperLink Text="用户注册" ID="btnRegister" runat="server" Width="60" NavigateUrl="Register.aspx" />
                </td>
            </tr>
        </table>
    </form>
</body>
</html>

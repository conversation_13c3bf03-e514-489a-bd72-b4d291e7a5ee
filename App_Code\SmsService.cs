﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using System.Web;

/// <summary>
/// SmsService 的摘要说明
/// </summary>
public class SmsService
{
    //private static readonly HttpClient httpClient = new HttpClient();
    public SmsService()
    {
        //
        // TODO: 在此处添加构造函数逻辑
        //
    }

    // 阿里云短信服务
    private static async Task SendSmsByAli(string phoneNumber, string verificationCode)
    {
        const string accessKeyId = "LTAIMzERFlMqdK4G"; // 阿里云 Access Key ID
        const string accessKeySecret = "utQdCmLfBux4NCoFglcuDvligCe7Bp"; // 阿里云 Access Key Secret
        const string signName = "Vv小秘书"; // 短信签名
        const string templateCode = "SMS_475125363"; // 短信模板 ID
        const string endpoint = "dysmsapi.aliyuncs.com";

        var config = new AlibabaCloud.OpenApiClient.Models.Config
        {
            AccessKeyId = accessKeyId,
            AccessKeySecret = accessKeySecret,
            Endpoint = endpoint
        };
        var sdkClient = new AlibabaCloud.SDK.Dysmsapi20170525.Client(config);
        var smsRequest = new AlibabaCloud.SDK.Dysmsapi20170525.Models.SendSmsRequest
        {
            PhoneNumbers = phoneNumber,
            SignName = signName,
            TemplateCode = templateCode,
            TemplateParam = new JObject { { "code", verificationCode } }.ToString()
        };

        try
        {
            var response = await sdkClient.SendSmsAsync(smsRequest);
            Logger.Log("response = " + JsonConvert.SerializeObject(response));
        }
        catch (Exception ex)
        {
            Logger.Log("error: " + ex.Message);
        }

    }

    private static async Task<bool> SendSmsByFeige(string phoneNumber, string verificationCode)
    {
        const string apikey = "N10903316aa";
        const string secret = "109033c34390d7056";
        const string platformSignID = "194430";
        const string verifyTemplateId = "151419";
        const string apiUrl = "https://api.4321.sh/sms/template";

        // 构建JSON请求内容
        var payload = new JObject
{
{ "apikey", apikey },
{ "secret", secret },
{ "mobile", phoneNumber },
{ "sign_id", platformSignID},
{ "template_id", verifyTemplateId },
{ "content", verificationCode }
};
        string jsonContent = payload.ToString();

        // 使用HttpClient发送请求
        using (HttpClient client = new HttpClient { Timeout = TimeSpan.FromSeconds(30000) })
        {
            try
            {
                StringContent content = new StringContent(jsonContent, Encoding.UTF8, "application/json");
                HttpResponseMessage response = await client.PostAsync(apiUrl, content);

                string responseContent = await response.Content.ReadAsStringAsync();


                // 解析 JSON 响应
                JObject jsonResponse = JObject.Parse(responseContent);
                int code;
                if (jsonResponse["code"] != null && jsonResponse["code"].Value<int?>() != null)
                {
                    code = jsonResponse["code"].Value<int>();
                    if (code != 0)
                    {
                        Logger.Log("手机号" + phoneNumber + " 短信发送失败: " + jsonResponse);
                    }
                }
                else
                {
                    code = -1;
                }

                return code == 0;
            }
            catch (Exception ex)
            {
                Logger.Log("发生未知错误: " + ex.Message);
                return false;
            }
        }
    }

    // 飞鸽云短信服务
    public static async Task<bool> SendSmsAsync(string phoneNumber, string verificationCode)
    {
        if (!InputParams.isDebug)
        {

            //await sendSmsByFeige(phoneNumber, verificationCode);  // 24/11/14 测试时账号禁用
            await SendSmsByAli(phoneNumber, verificationCode);
        }
        Logger.Log(string.Format("{0} 验证码: {1}", phoneNumber, verificationCode));

        return true;
    }
}
﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace WebControlLibrary1
{
    //[DefaultProperty("Text")]
    //[ToolboxData("<{0}:WebCustomControl1 runat=server></{0}:WebCustomControl1>")]
    public class WebCustomControl1 : System.Web.UI.WebControls.WebControl, IPostBackEventHandler
    {
        [Bindable(true)]
        [Category("Appearance")]
        [DefaultValue("")]
        [Localizable(true)]
        public string Text
        {
            get
            {
                String s = (String)ViewState["Text"];
                return ((s == null) ? String.Empty : s);
            }

            set
            {
                ViewState["Text"] = value;
            }
        }


        #region Variables and Constants

        public event EventHandler ChangePageClick;
        private string _BarBackGroundColor = "#F1F1F1";
        private string _BarLinkColor = "Navy";
        private string _BarCurrentColor = "#EEEEEE";
        private int _TotalRecord = 0;
        private int _TotalPage = 0;
        private int _PageSize =10;

        private int _CurrentPageIndex = 1;
        private int _ItemSize = 10;

        #endregion

        #region Properties

        [
        Description("分页条背景色"),
        Bindable(true),
        Category("Appearance"),
        DefaultValue("#F1F1F1")
        ]
        public string BarBackGroundColor
        {
            get { return _BarBackGroundColor; }
            set { _BarBackGroundColor = value; }
        }

        [
        Description("分页条带链接数字颜色"),
        Bindable(true),
        Category("Appearance"),
        DefaultValue("Navy")
        ]
        public string BarLinkColor
        {
            get { return _BarLinkColor; }
            set { _BarLinkColor = value; }
        }

        [
        Description("分页条当前页数字颜色"),
        Bindable(true),
        Category("Appearance"),
        DefaultValue("#EEEEEE")
        ]
        public string BarCurrentColor
        {
            get { return _BarCurrentColor; }
            set { _BarCurrentColor = value; }
        }

        [
        Description("总记录数"),
        Bindable(false),
        Category("Behavior"),
        DefaultValue(0)
        ]
        public int TotalRecord
        {
            get { return _TotalRecord; }
            set
            {
                foreach (char c in System.Convert.ToString(value))
                {
                    if (!Char.IsNumber(c))
                    {
                        _TotalRecord = 0;
                        break;
                    }
                }
                _TotalRecord = value;
            }
        }

        [
        Description("每页显示记录数"),
        Bindable(true),
        Category("Behavior"),
        DefaultValue(0)
        ]
        public int PageSize
        {
            get { return _PageSize; }
            set
            {
                foreach (char c in System.Convert.ToString(value))
                {
                    if (!Char.IsNumber(c))
                    {
                        _PageSize = 0;
                        break;
                    }
                }
                _PageSize = value;
            }
        }

        [
        Description("总页数"),
        Bindable(true),
        Category("Behavior"),
        DefaultValue(0)
        ]
        public int TotalPage
        {
            get { return _TotalPage; }
        }

        [
        Description("数字规格"),
        Bindable(true),
        Category("Behavior"),
        DefaultValue(10)
        ]
        public int ItemSize
        {
            get { return _ItemSize; }
            set
            {
                foreach (char c in System.Convert.ToString(value))
                {
                    if (!Char.IsNumber(c))
                    {
                        _ItemSize = 10;
                        break;
                    }
                }
                _ItemSize = value;
            }
        }

        [
        Description("当前页值"),
        Bindable(true),
        Category("Behavior"),
        DefaultValue(1)
        ]
        public int CurrentPageIndex
        {
            get { return _CurrentPageIndex; }
            set { _CurrentPageIndex = value; }
        }


        #endregion

        //定义Div的样式
        protected override void AddAttributesToRender(HtmlTextWriter writer)
        {
            writer.AddStyleAttribute("White-space", "nowrap");
            writer.AddStyleAttribute("Padding-Top", "2px");
            writer.AddStyleAttribute("Padding-Bottom", "2px");
            writer.AddStyleAttribute("Width", Width.ToString());
            writer.AddStyleAttribute("Height", Height.ToString());
            base.AddAttributesToRender(writer);
        }

        protected virtual void OnPageChangeClick(EventArgs e)
        {
            if (ChangePageClick != null)
            {
                ChangePageClick(this, e);
            }
        }

        public void RaisePostBackEvent(string eventArgument)
        {
            int PageIndex = int.Parse(eventArgument);
            this._CurrentPageIndex = PageIndex;
            OnPageChangeClick(new EventArgs());
        }

        /// <summary>
        /// 将此控件呈现给指定的输出参数。
        /// </summary>
        /// <param name="output"> 要写出到的 HTML 编写器 </param>
        protected override void RenderContents(HtmlTextWriter output)
        {
            this._TotalPage = ((this.TotalRecord / PageSize) * this.PageSize == this.TotalRecord) ? (this.TotalRecord / this.PageSize) : ((this.TotalRecord / this.PageSize) + 1);
            int BeginRecord = (this.CurrentPageIndex - 1) * this.PageSize + 1;
            int EndRecord = this.CurrentPageIndex * this.PageSize;
            EndRecord = (EndRecord > this.TotalRecord) ? this.TotalRecord : EndRecord;
            string PageInfo = "[共<font color=#CC0000>" + this.TotalPage.ToString() + "</font>页/当前第<font color=#CC0000>" + this.CurrentPageIndex.ToString() + "</font>页   共<font color=#CC0000>" + TotalRecord.ToString() + "</font>条记录,当前记录数<font color=#CC0000>" + BeginRecord.ToString() + "</font>到<font color=#CC0000>" + EndRecord.ToString() + "</font>]";
            string PageListStr = "";
            string PageIndexColor = "#0000C0";
            int SingleNumber = this.TotalPage - (TotalPage / ItemSize) * ItemSize; //得到分页后的尾数(比如：总共58页，按10页规格显示，则尾数为8)
            int IntPageForMax = (this.CurrentPageIndex - 1) / ItemSize;
            int MinInt = (1 + ItemSize * IntPageForMax);
            int MaxInt = ((IntPageForMax + 1) * ItemSize) > TotalPage ? TotalPage : ((IntPageForMax + 1) * ItemSize);
            if (this.TotalRecord == 0 || this.TotalPage == 0)
            {
                PageListStr = "<font color=" + PageIndexColor + ">0</font>";
                PageListStr = PageListStr + " [共<font color=#CC0000>0</font>页/当前第<font color=#CC0000>0</font>页   共<font color=#CC0000>0</font>条记录,当前记录数<font color=#CC0000>0</font>到<font color=#CC0000>0</font>]";
                output.Write(PageListStr);
            }
            else
            {
                if (this.TotalPage <= this.ItemSize)
                {
                    for (int i = 1; i <= TotalPage; i++)
                    {
                        PageIndexColor = CurrentPageIndex == i ? "#CC0000" : "#0000C0";
                        if (CurrentPageIndex == i)
                            PageListStr = PageListStr + " <a   style='font-size:14px; text-decoration:none' title='当前为第『" + i + "』页' href='#' id=\"" + this.UniqueID + "\"><font color=" + PageIndexColor + ">" +"["+ i.ToString() +"]"+ "</font></a>";
                        else
                            PageListStr = PageListStr + " <a   style='font-size:14px'  title='点击转到第『" + i + "』页' id=\"" + this.UniqueID + "\" href=\"javascript:" + Page.GetPostBackEventReference(this, i.ToString()) + "\"><font color=" + PageIndexColor + ">" + "[" + i.ToString() + "]" + "</font></a>";
                    }
                    PageListStr = PageListStr == "" ? "<font color=" + PageIndexColor + ">0</font>" : PageListStr;
                    PageListStr = PageListStr + " " + PageInfo;
                    output.Write(PageListStr);
                }
                else
                {
                    int MultiMinPageIndex = (IntPageForMax * ItemSize);
                    int MultiMaxPageIndex = ((IntPageForMax + 1) * ItemSize) + 1;

                    for (int i = MinInt; i <= MaxInt; i++)
                    {
                        PageIndexColor = CurrentPageIndex == i ? "#CC0000" : "#0000C0";
                        if (CurrentPageIndex == i)
                            PageListStr = PageListStr + " <a   style='font-size:14px; text-decoration:none'  title='当前为第『" + i + "』页' href='#' id=\"" + this.UniqueID + "\"><font color=" + PageIndexColor + ">" + "[" + i.ToString() + "]" + "</font></a>";
                        else
                            PageListStr = PageListStr + " <a   style='font-size:13px' title='点击转到第『" + i + "』页' id=\"" + this.UniqueID + "\" href=\"javascript:" + Page.GetPostBackEventReference(this, i.ToString()) + "\"><font color=" + PageIndexColor + ">" + "[" + i.ToString() + "]" + "</font></a>";
                    }
                    //当当前页数小于ItemSize且总的页数大于ItemSize时 
                    if (CurrentPageIndex <= ItemSize && TotalPage > ItemSize)
                    {
                        PageListStr = PageListStr + " <a id=\"" + this.UniqueID + "\" title='点击转到第『" + System.Convert.ToString(ItemSize + 1) + "』页' href=\"javascript:" + Page.GetPostBackEventReference(this, System.Convert.ToString(ItemSize + 1)) + "\"> ... </a>";


                        PageListStr = PageListStr == "" ? "<font color=" + PageIndexColor + ">0</font>" : PageListStr;

                        PageListStr = PageListStr + "  " + "<a id=\"" + this.UniqueID + "\" title='点击转到第『" + this.TotalPage.ToString() + "』页' href=\"javascript:" + Page.GetPostBackEventReference(this, this.TotalPage.ToString()) + "\">尾页</a>";

                    }

                    //当当前页数大于ItemSize，且总的页数减去当前页数大于等于尾数值页数时
                    if (this.CurrentPageIndex > ItemSize && (TotalPage - this.CurrentPageIndex) >= SingleNumber)
                    {


                        if ((MultiMinPageIndex <= TotalPage) && (TotalPage <= MultiMaxPageIndex))
                        {
                            PageListStr = "<a id=\"" + this.UniqueID + "\" title='点击转到第『" + MultiMinPageIndex + "』页' href=\"javascript:" + Page.GetPostBackEventReference(this, MultiMinPageIndex.ToString()) + "\"> ... </a>" + "  " + PageListStr.Trim();

                        }

                        else
                        {
                            PageListStr = "<a id=\"" + this.UniqueID + "\" title='点击转到第『" + MultiMinPageIndex + "』页' href=\"javascript:" + Page.GetPostBackEventReference(this, MultiMinPageIndex.ToString()) + "\"> ... </a>" + "  " + PageListStr.Trim() + "  " + "<a id=\"" + this.UniqueID + "\" title='点击转到第『" + MultiMaxPageIndex.ToString() + "』页' href=\"javascript:" + Page.GetPostBackEventReference(this, MultiMaxPageIndex.ToString()) + "\">...</a>";

                        }

                        PageListStr = "<a id=\"" + this.UniqueID + "\" title='点击转到第『" + "1" + "』页' href=\"javascript:" + Page.GetPostBackEventReference(this, "1") + "\">首页</a>" + "  " + PageListStr;
                        PageListStr = PageListStr + "  " + "<a id=\"" + this.UniqueID + "\" title='点击转到第『" + this.TotalPage.ToString() + "』页' href=\"javascript:" + Page.GetPostBackEventReference(this, this.TotalPage.ToString()) + "\">尾页</a>";

                    }

                    //当当前页数大于ItemSize，且总的页数减去当前页数小于等于尾数值页数时
                    if (CurrentPageIndex > 10 && (TotalPage - CurrentPageIndex) < SingleNumber)
                    {
                        PageListStr = "<a id=\"" + this.UniqueID + "\" title='点击转到第『" + MultiMinPageIndex + "』页' href=\"javascript:" + Page.GetPostBackEventReference(this, MultiMinPageIndex.ToString()) + "\">...</a>" + " " + PageListStr.Trim();
                        PageListStr = "<a id=\"" + this.UniqueID + "\" title='点击转到第『" + "1" + "』页' href=\"javascript:" + Page.GetPostBackEventReference(this, "1") + "\">首页</a>" + "  " + PageListStr;

                    }


                    PageListStr = PageListStr + "   " + PageInfo;
                    output.Write(PageListStr);
                }
            }
            base.RenderContents(output);
        }

    }
}

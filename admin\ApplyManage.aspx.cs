﻿using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class admin_ApplyManage : System.Web.UI.Page
{
    protected void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack)
        {
            BindGrid();
        }
    }

    private void BindGrid()
    {
        string groupId = Common.ToString(Session["group_id"]);
        var dt = Common.ExecuteQuery($"select t1.*,t2.nickname,t2.truename from ApplyAdminRecord t1,Users t2 where t1.user_id = t2.user_id and t1.group_id = {groupId}");
        GridView1.DataSource = dt;
        GridView1.DataBind();
    }

    protected void GridView1_SelectedIndexChanged(object sender, EventArgs e)
    {

    }


    protected void GridView1_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        if (e.CommandName == "Agree" || e.CommandName == "Refuse")
        {
            string adminUserId = Common.ToString(Session["user_id"]);
            string[] args = e.CommandArgument.ToString().Split(',');
            string id = args[0];
            string applyUserId = args[1];
            int agreeOrRefuse = e.CommandName == "Agree" ? 1 : -1;
            DateTime updateTime = DateTime.Now;

            string query = $"update ApplyAdminRecord set deal_user_id = {adminUserId}, is_deal = 1, deal_result = {agreeOrRefuse}, deal_time = '{updateTime}' where id = {id}";
            int res = Common.ExecuteNonQuery(query);
            if (res > 0)
            {
                Response.Write(Common.alertMsg("审批完成"));
                if (agreeOrRefuse == 1)
                {
                    query = $"update Users set isGroupManager = 2 where user_id = {applyUserId}";
                    res = Common.ExecuteNonQuery(query);
                    if (res > 0)
                    {
                        Logger.Log($"管理员id{adminUserId}批准用户{applyUserId}成为管理员成功");
                    }
                    else
                    {
                        Logger.Log($"管理员id{adminUserId}批准用户{applyUserId}成为管理员失败");
                    }
                }
            }
            else
            {
                Response.Write(Common.alertMsg("审批失败"));
            }
            BindGrid();
        }
        //else if (e.CommandName == "Page")
        //{
        //    GridView1.PageIndex = Convert.ToInt32(e.CommandArgument);
        //    BindGrid();
        //}
    }

    protected void GridView1_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        //Response.Write("GridView1_RowDataBound");
        if (e.Row.RowType == DataControlRowType.DataRow)
        {
            // 判断当前行是否为偶数行
            if (e.Row.RowIndex % 2 == 0)
            {
                e.Row.BackColor = System.Drawing.ColorTranslator.FromHtml(InputParams.rowColor1);
            }
            else
            {
                e.Row.BackColor = System.Drawing.ColorTranslator.FromHtml(InputParams.rowColor2);
            }

            Button btnAgree = (Button)e.Row.FindControl("btnAgree");
            Button btnRefuse = (Button)e.Row.FindControl("btnRefuse");

            string isDeal = DataBinder.Eval(e.Row.DataItem, "is_deal").ToString();
            TableCell cell4 = e.Row.Cells[5];
            if (!string.IsNullOrEmpty(isDeal))
            {
                if (isDeal == "0")
                {
                    cell4.Text = "未处理";
                }
                else
                {
                    cell4.Text = "已处理";
                    btnAgree.Enabled = false;
                    btnRefuse.Enabled = false;
                }
            }
            else
            {
                cell4.Text = "-";
            }

            string dealResult = DataBinder.Eval(e.Row.DataItem, "deal_result").ToString();
            TableCell cell5 = e.Row.Cells[6];
            if (!string.IsNullOrEmpty(dealResult))
            {
                switch (dealResult)
                {
                    case "1":
                        cell5.Text = "已同意";
                        break;
                    case "0":
                        cell5.Text = "审批中";
                        break;
                    case "-1":
                        cell5.Text = "已拒绝";
                        break;
                }
            }
            else
            {
                cell5.Text = "-";
            }

            string dealTime = DataBinder.Eval(e.Row.DataItem, "deal_time").ToString();
            TableCell cell6 = e.Row.Cells[7];
            if (string.IsNullOrEmpty(dealTime))
            {
                cell6.Text = "-";
            }
            else
            {
                cell6.Text = dealTime;
            }
        }
    }

    protected void GridView1_PageIndexChanging(object sender, GridViewPageEventArgs e)
    {
        GridView1.PageIndex = e.NewPageIndex;
        BindGrid();
    }
}
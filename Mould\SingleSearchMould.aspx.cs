﻿using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Data.SqlClient;
using System.IO;
using System.Text;

public partial class SingleSearchMould : System.Web.UI.Page
{


    /// <summary>
    /// 声明全局变量
    /// </summary>
    int recordCount;
    int pageCount;
    int fieldsCount;
    int longNum;
    int pagerIndex;
    int rowSpan;
    int colSpan;
    int[] picLongchar;
    int[] fieldTableShowLength;
    string subSearchStr;
    int lineLength = 2;

    #region 查找参数初始化
    //利用此模板设计查找页面时，只需要修改这一部分
    readonly string ConnStr = InputParams.connectionStr;
    public const string mainTable = "Tasks";
    public const string pKey = "TaskID";
    public  string[] singleSearch={"Tasks","Executor","6" };

    public EnumField[][] EnumField()
    {
        EnumField[] enum0 ={ new EnumField(1, "日任务"), new EnumField(2, "周任务"), new EnumField(3, "月任务"), new EnumField(4, "年任务") };
        EnumField[][] efTs ={ enum0 };
        return efTs;
    }
    public Table[] Tables()
    {
        string tblName1 = "Tasks";
        Field fld0 = new Field(0, "TaskName", "任务名称", 0, EnumFieldType.charType, 0, 0, "", 0, "");
        Field fld3 = new Field(3, "Type", "任务类型", 0, EnumFieldType.enumType, 0, 0, "", 0, "");
        Field fld4 = new Field(4, "CreateTime", "创建时间", 0, EnumFieldType.dateType, 0, 0, "", 0, "");
        Field fld5 = new Field(5, "Description", "任务描述", 0, EnumFieldType.longcharType, 0, 0, "", 0, "");
        Field[] flds1 ={ fld0, fld3, fld4, fld5 };
        Join jon1 = new Join("Creator", "Users", "UserID");
        Table table1 = new Table(tblName1, jon1, flds1);

        string tblName2 = "Users";
        Field fld1 = new Field(1, "RealName", "创建者", 80, EnumFieldType.charType, 0, 0, "", 0, "");
        Field fld2 = new Field(2, "CellPhone", "联系电话", 60, EnumFieldType.charType, 0, 0, "", 0, "");
        Field[] flds2 ={ fld1, fld2 };
        Join jon2 = new Join();
        Table table2 = new Table(tblName2, jon2, flds2);

        Table[] tables ={ table1, table2 };
        return tables;
    }
    #endregion

    /// <summary>
    /// 页面加载
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void Page_Load(object sender, EventArgs e)
    {
        WebCustomControl1_1.PageSize = InputParams.pageSize;
        WebCustomControl1_1.ItemSize = InputParams.itemSize;
        GenerateSearchMould();
        btnDelete.Attributes.Add("onclick", "return confirm('确认删除?')");
        string searchStr = "";
        string sortExpression = "";
        string sortDirection = "";
        pagerIndex = 1;

        if (!Page.IsPostBack)
        {
            searchStr = GetSearchStr();
            ViewState["searchStr"] = searchStr;
            ViewState["subSearchStr"] = subSearchStr;
        }
        if (ViewState["sortExpression"] != null)
        {
            sortExpression = ViewState["sortExpression"].ToString();
            sortDirection = ViewState["sortDirection"].ToString();
        }

        if (ViewState["searchStr"] != null)
        {
            searchStr = ViewState["searchStr"].ToString();
        }

        try
        {

            DataTable dt = GetSearchResultTable(searchStr, sortExpression, sortDirection);
            if ((ViewState["pagerIndex"] != null) && (pageCount >= int.Parse(ViewState["pagerIndex"].ToString())))
            {
                pagerIndex = int.Parse(ViewState["pagerIndex"].ToString());
            }
            else
            {
                pagerIndex = 1;

            }
            ViewState["pagerIndex"] = pagerIndex;
            ShowResult(dt, sortExpression, sortDirection);
        }
        catch (SqlException ex)
        {
            Response.Write(ex.ToString());
        }

    }

    /// <summary>
    /// 分页事件处理
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void pager_Click(object sender, EventArgs e)
    {
        pagerIndex = WebCustomControl1_1.CurrentPageIndex;
        ViewState["pagerIndex"] = pagerIndex;

        string sortExpression = "";
        string sortDirection = "";
        string searchStr = ViewState["searchStr"].ToString();

        if (ViewState["sortExpression"] != null)
        {
            sortExpression = ViewState["sortExpression"].ToString();
            sortDirection = ViewState["sortDirection"].ToString();
        }
        GenerateSearchMould();
        btnDelete.Attributes.Add("onclick", "return confirm('确认删除?')");
        DataTable dt = GetSearchResultTable(searchStr, sortExpression, sortDirection);
        ShowResult(dt, sortExpression, sortDirection);
    }

    /// <summary>
    /// 排序事件处理
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void lkb_Command(object sender, CommandEventArgs e)
    {
        string searchStr = ViewState["subSearchStr"].ToString();
        string sortExpression = e.CommandName;
        string sortDirection;

        if ((ViewState["sortExpression"] != null) && (ViewState["sortExpression"].ToString() == e.CommandName))
        {
            if ((ViewState["sortDirection"] != null) && (ViewState["sortDirection"].ToString() != "ASC"))
            {
                sortDirection = "ASC";
            }
            else
            {
                sortDirection = "DESC";
            }
        }
        else
        {
            sortDirection = "ASC";
            ViewState["sortExpression"] = e.CommandName;
        }

        ViewState["sortDirection"] = sortDirection;
        GenerateSearchMould();
        btnDelete.Attributes.Add("onclick", "return confirm('确认删除?')");
        DataTable dt = GetSearchResultTable(searchStr, sortExpression, sortDirection);
        ShowResult(dt, sortExpression, sortDirection);
    }

    /// <summary>
    /// 查找事件处理
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void btnSearch_Click(object sender, EventArgs e)
    {
        string searchStr = GetSearchStr();
        ViewState["searchStr"] = searchStr;
        ViewState["subSearchStr"] = subSearchStr;
        //pagerIndex = 1;

        string sortExpression = "";
        string sortDirection = "";

        GenerateSearchMould();
        btnDelete.Attributes.Add("onclick", "return confirm('确认删除?')");

        DataTable dt = GetSearchResultTable(searchStr, sortExpression, sortDirection);
        ShowResult(dt, sortExpression, sortDirection);
    }


    /// <summary>
    /// 删除事件处理
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void btnDelete_Click(object sender, EventArgs e)
    {
        try
        {
            SqlConnection conn = new SqlConnection(ConnStr);
            //和数据库建立连接
            if (conn.State.ToString() == "Closed")
            {
                conn.Open();
            }
            for (int i = 0; i < placeHolder2.Controls.Count; i++)
            {
                string str = this.placeHolder2.Controls[i].GetType().ToString();
                if (str == "System.Web.UI.WebControls.CheckBox")
                {
                    string strCkb = placeHolder2.Controls[i].ID.ToString();
                    if (((CheckBox)placeHolder2.FindControl(strCkb)).Checked)
                    {
                        string[] arrCkb = strCkb.Split(' ');
                        int RecordId = int.Parse(arrCkb[0]);
                        string strDelete = " DELETE FROM  " +mainTable  + "  WHERE " +pKey  + "=" + RecordId;
                        SqlCommand comm = new SqlCommand(strDelete, conn);
                        comm.ExecuteNonQuery();
                    }
                }
            }

            //释放数据库连接
            if (conn.State.ToString() == "Open")
            {
                conn.Close();
            }

            string sortExpression = "";
            string sortDirection = "";
            string searchStr = ViewState["searchStr"].ToString();

            if (ViewState["sortExpression"] != null)
            {
                sortExpression = ViewState["sortExpression"].ToString();
                sortDirection = ViewState["sortDirection"].ToString();
            }
            GenerateSearchMould();
            btnDelete.Attributes.Add("onclick", "return confirm('确认删除?')");
            DataTable dt = GetSearchResultTable(searchStr, sortExpression, sortDirection);

            if (pageCount > int.Parse(ViewState["pagerIndex"].ToString()) || pageCount == int.Parse(ViewState["pagerIndex"].ToString()))
            {
                pagerIndex = int.Parse(ViewState["pagerIndex"].ToString());

            }
            else
            {
                pagerIndex = int.Parse(ViewState["pagerIndex"].ToString()) - 1;
            }
            ViewState["pagerIndex"] = pagerIndex;
            WebCustomControl1_1.CurrentPageIndex = pagerIndex;
            ShowResult(dt, sortExpression, sortDirection);
            ClientScript.RegisterClientScriptBlock(this.GetType(), "info", "<script language=\"javascript\" type=\"text/javascript\">alert(\"删除成功！\");</script>");

        }
        catch (SqlException ex)
        {
            Response.Write("删除失败！");
            Response.Write(ex.ToString());
        }
    }


    /// <summary>
    /// 修改事件处理
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void btnModify_Click(object sender, EventArgs e)
    {
        int modifyCount = 0;
        for (int i = 0; i < placeHolder2.Controls.Count; i++)
        {
            string str = this.placeHolder2.Controls[i].GetType().ToString();
            if (str == "System.Web.UI.WebControls.CheckBox")
            {
                string strCkb = placeHolder2.Controls[i].ID.ToString();
                if (((CheckBox)placeHolder2.FindControl(strCkb)).Checked)
                {
                    modifyCount++;
                }
            }
        }

        int[] ckbsModify = new int[modifyCount];

        int modifyIndex = 0;
        for (int i = 0; i < placeHolder2.Controls.Count; i++)
        {
            string str = this.placeHolder2.Controls[i].GetType().ToString();
            if (str == "System.Web.UI.WebControls.CheckBox")
            {
                string strCkb = placeHolder2.Controls[i].ID.ToString();
                if (((CheckBox)placeHolder2.FindControl(strCkb)).Checked)
                {
                    string[] arrCkb = strCkb.Split(' ');
                    int RecordId = int.Parse(arrCkb[0]);
                    ckbsModify[modifyIndex] = RecordId;
                    modifyIndex++;
                }
            }
        }
        Session["ckbsModify"] = ckbsModify;
        Response.Redirect("ModifyTasks.aspx");
    }

    protected void btnAdd_Click(object sender, EventArgs e)
    {
        Response.Redirect("AddTask.aspx");
    }



    /// <summary>
    ///表格导出到Excel&Word
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void btnWord_Click(object sender, EventArgs e)
    {
        Export("application/ms-word", "SearchResult.doc");

    }
    protected void btnExcel_Click(object sender, EventArgs e)
    {
        Export("application/ms-excel", "SearchResult.xls");

    }
    public override void VerifyRenderingInServerForm(Control control)
    {

    }


    private void Export(string FileType, string FileName)
    {
        Response.Charset = "GB2312";
        Response.ContentEncoding = System.Text.Encoding.UTF8;
        Response.AppendHeader("Content-Disposition", "attachment;filename=" + HttpUtility.UrlEncode(FileName, Encoding.UTF8).ToString());
        Response.ContentType = FileType;
        this.EnableViewState = false;
        StringWriter tw = new StringWriter();
        HtmlTextWriter hw = new HtmlTextWriter(tw);
        placeHolder2.RenderControl(hw);
        Response.Write(tw.ToString());
        Response.End();
    }


    /// <summary>
    /// 生成查找模板
    /// </summary>
    protected void GenerateSearchMould()
    {
        placeHolder1.Controls.Clear();
        EnumField[][] efTs = EnumField();
        Table[] tables = Tables();
        Label lblHr1 = new Label();
        lblHr1.Text = "<hr/>";
        placeHolder1.Controls.Add(lblHr1);

        Label lblBr1 = new Label();
        lblBr1.Text = "<br/>";
        placeHolder1.Controls.Add(lblBr1);

        //计算查找字段总数
        fieldsCount = 0;
        btnDelete.Attributes.Add("onclick", "return confirm('确认删除?')");
        for (int tableIndex = 0; tableIndex < tables.Length; tableIndex++)
        {
            for (int fieldIndex = 0; fieldIndex < tables[tableIndex].fields.Length; fieldIndex++)
            {
                fieldsCount++;
            }
        }

        picLongchar = new int[fieldsCount];
        fieldTableShowLength = new int[fieldsCount];
        Label lblHead = new Label();
        lblHead.Text = " 设置查找条件：<br/>";
        lblHead.ForeColor = System.Drawing.ColorTranslator.FromHtml(InputParams.lblHeadColor);
        placeHolder1.Controls.Add(lblHead);

        int iLine = 0;
        int iPosition;

        #region forTable
        for (int tableIndex = 0; tableIndex < tables.Length; tableIndex++)
        {
            #region forField
            for (int fieldIndex = 0; fieldIndex < tables[tableIndex].fields.Length; fieldIndex++)
            {
                iPosition = tables[tableIndex].fields[fieldIndex].position;

                //生成换行符
                if ((iLine != 0) && (iLine % lineLength) == 0)
                {
                    Label lblBrs = new Label();
                    lblBrs.Text = "<br/>";
                    placeHolder1.Controls.Add(lblBrs);
                }
                iLine++;
                //生成空格
                if (iLine % lineLength == 0)
                {
                    Literal ltlSpaces = new Literal();
                    ltlSpaces.Text = "&nbsp; &nbsp; &nbsp; ";
                    placeHolder1.Controls.Add(ltlSpaces);
                }

                //生成字段显示名称
                Label lbl = new Label();
                lbl.Text = tables[tableIndex].fields[fieldIndex].fieldShowName + "：";
                lbl.ForeColor = System.Drawing.ColorTranslator.FromHtml(InputParams.lblFieldShowNameColor);
                placeHolder1.Controls.Add(lbl);

                fieldTableShowLength[iLine - 1] = int.Parse(tables[tableIndex].fields[fieldIndex].fieldTableShowLength.ToString());

                //生成填写选择条件的文本框
                #region switch

                string strFldName = tables[tableIndex].fields[fieldIndex].fieldName;

                switch (tables[tableIndex].fields[fieldIndex].fieldType)
                {
                    case EnumFieldType.charType:
                        picLongchar[iPosition] = 11;
                        TextBox tbx1 = new TextBox();
                        tbx1.ID = "tbx" + iLine.ToString() + strFldName;
                        tbx1.ToolTip = "不同的关键字之间请以空格隔开！";
                        tbx1.Width = InputParams.tbxCharLength;
                        tbx1.Height = InputParams.tbxHeight;
                        tbx1.TextMode = TextBoxMode.MultiLine;
                        tbx1.Wrap = true;
                        tbx1.Style.Add("overflow", "hidden");
                        placeHolder1.Controls.Add(tbx1);
                        ((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString() + strFldName)).Attributes.Add("onMouseOver", "this.style.backgroundColor='" + InputParams.tbxOnmouseoverColor + "';");
                        ((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString() + strFldName)).Attributes.Add("onMouseOut", "this.style.backgroundColor='" + InputParams.tbxOnmouseoutColor + "';");
                        continue;

                    case EnumFieldType.longcharType:
                        picLongchar[iPosition] = 12;
                        TextBox tbx2 = new TextBox();
                        tbx2.ID = "tbx" + iLine.ToString() + strFldName;
                        tbx2.ToolTip = "请输入字段包含的文字，不同的关键字之间请以空格隔开！";
                        tbx2.Width = InputParams.tbxLongCharLength;
                        tbx2.Height = InputParams.tbxHeight;
                        tbx2.TextMode = TextBoxMode.MultiLine;
                        //tbx2.Wrap = true;
                        //tbx2.Style.Add("overflow", "hidden");
                        placeHolder1.Controls.Add(tbx2);
                        ((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString() + strFldName)).Attributes.Add("onMouseOver", "this.style.backgroundColor='" + InputParams.tbxOnmouseoverColor + "';");
                        ((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString() + strFldName)).Attributes.Add("onMouseOut", "this.style.backgroundColor='" + InputParams.tbxOnmouseoutColor + "';");
                        continue;

                    case EnumFieldType.numberType:
                        picLongchar[iPosition] = 13;
                        TextBox tbx3 = new TextBox();
                        tbx3.ID = "tbx" + iLine.ToString() + strFldName + "1";
                        tbx3.Width = InputParams.tbxNumberLength;
                        tbx3.Height = InputParams.tbxHeight;

                        Label lblDao1 = new Label();
                        lblDao1.Text = "到";
                        lblDao1.ForeColor = System.Drawing.ColorTranslator.FromHtml(InputParams.lblFieldShowNameColor);
                        TextBox tbx4 = new TextBox();
                        tbx4.ID = "tbx" + iLine.ToString() + strFldName + "2";
                        tbx4.Width = InputParams.tbxNumberLength;
                        tbx4.Height = InputParams.tbxHeight;

                        placeHolder1.Controls.Add(tbx3);
                        placeHolder1.Controls.Add(lblDao1);
                        placeHolder1.Controls.Add(tbx4);

                        ((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString() + strFldName + "1")).Attributes.Add("onMouseOver", "this.style.backgroundColor='" + InputParams.tbxOnmouseoverColor + "';");
                        ((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString() + strFldName + "1")).Attributes.Add("onMouseOut", "this.style.backgroundColor='" + InputParams.tbxOnmouseoutColor + "';");
                        ((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString() + strFldName + "2")).Attributes.Add("onMouseOver", "this.style.backgroundColor='" + InputParams.tbxOnmouseoverColor + "';");
                        ((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString() + strFldName + "2")).Attributes.Add("onMouseOut", "this.style.backgroundColor='" + InputParams.tbxOnmouseoutColor + "';");
                        continue;

                    case EnumFieldType.dateType:
                        picLongchar[iPosition] = 14;
                        TextBox tbx5 = new TextBox();
                        //tbx4.Text = DateTime.Now.ToShortDateString();
                        tbx5.ID = "tbx" + iLine.ToString() + strFldName + "1";
                        tbx5.Width = InputParams.tbxDateLength;
                        tbx5.Height = InputParams.tbxHeight;

                        Label lblDao2 = new Label();
                        lblDao2.Text = "到";
                        lblDao2.ForeColor = System.Drawing.ColorTranslator.FromHtml(InputParams.lblFieldShowNameColor);
                        TextBox tbx6 = new TextBox();
                        tbx6.ID = "tbx" + iLine.ToString() + strFldName + "2";
                        tbx6.Width = InputParams.tbxDateLength;
                        tbx6.Height = InputParams.tbxHeight;

                        //tbx5.Text = DateTime.Now.ToShortDateString();

                        placeHolder1.Controls.Add(tbx5);
                        placeHolder1.Controls.Add(lblDao2);
                        placeHolder1.Controls.Add(tbx6);

                        ((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString() + strFldName + "1")).Attributes.Add("onClick", "javascript:calendar()");
                        ((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString() + strFldName + "2")).Attributes.Add("onClick", "javascript:calendar()");
                        ((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString() + strFldName + "1")).Attributes.Add("onClick", "javascript:calendar()");
                        ((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString() + strFldName + "2")).Attributes.Add("onClick", "javascript:calendar()");
                        continue;



                    case EnumFieldType.boolType:
                        picLongchar[iPosition] = 15;
                        RadioButton rbY = new RadioButton();
                        RadioButton rbN = new RadioButton();

                        rbY.ID = "rb" + iLine.ToString() + strFldName + "1";
                        rbY.GroupName = "gn" + strFldName;
                        rbY.Text = "是";
                        rbY.ForeColor = System.Drawing.ColorTranslator.FromHtml(InputParams.rbYNColor);
                        placeHolder1.Controls.Add(rbY);


                        rbN.ID = "rb" + iLine.ToString() + strFldName + "2";
                        rbN.GroupName = "gn" + strFldName;
                        rbN.Text = "否";
                        rbN.ForeColor = System.Drawing.ColorTranslator.FromHtml(InputParams.rbYNColor);
                        placeHolder1.Controls.Add(rbN);
                        //((RadioButton)placeHolder1.FindControl("rb" + iLine.ToString() + strFldName + "1")).Attributes.Add("onMouseOver", "this.style.backgroundColor='" + InputParams.tbxOnmouseoverColor + "';");
                        //((RadioButton)placeHolder1.FindControl("rb" + iLine.ToString() + strFldName + "1")).Attributes.Add("onMouseOut", "this.style.backgroundColor='" + InputParams.tbxOnmouseoutColor + "';");
                        //((RadioButton)placeHolder1.FindControl("rb" + iLine.ToString() + strFldName + "2")).Attributes.Add("onMouseOver", "this.style.backgroundColor='" + InputParams.tbxOnmouseoverColor + "';");
                        //((RadioButton)placeHolder1.FindControl("rb" + iLine.ToString() + strFldName + "2")).Attributes.Add("onMouseOut", "this.style.backgroundColor='" + InputParams.tbxOnmouseoutColor + "';");
                        continue;

                    case EnumFieldType.picType:
                        picLongchar[iPosition] = 16;
                        TextBox tbx7 = new TextBox();
                        tbx7.ID = "tbx" + iLine.ToString() + strFldName;
                        tbx7.ToolTip = "请输入图片路径！";
                        tbx7.Width = InputParams.tbxCharLength;
                        tbx7.Height = InputParams.tbxHeight;
                        tbx7.TextMode = TextBoxMode.MultiLine;
                        tbx7.Wrap = true;
                        tbx7.Style.Add("overflow", "hidden");
                        placeHolder1.Controls.Add(tbx7);
                        ((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString() + strFldName)).Attributes.Add("onMouseOver", "this.style.backgroundColor='" + InputParams.tbxOnmouseoverColor + "';");
                        ((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString() + strFldName)).Attributes.Add("onMouseOut", "this.style.backgroundColor='" + InputParams.tbxOnmouseoutColor + "';");
                        continue;

                    case EnumFieldType.enumType:
                        picLongchar[iPosition] = tables[tableIndex].fields[fieldIndex].enumTag;
                        DropDownList ddl = new DropDownList();
                        ddl.ID = "ddl" + iLine.ToString() + strFldName;
                        int enumT = tables[tableIndex].fields[fieldIndex].enumTag;
                        ddl.Width = InputParams.ddlWidth;
                        ddl.AutoPostBack = false;
                        placeHolder1.Controls.Add(ddl);

                        ListItem liTop = new ListItem();
                        liTop.Text = " 请选择 ";
                        ddl.Items.Add(liTop);
                        for (int enumLen = 0; enumLen < efTs[enumT].Length; enumLen++)
                        {
                            ListItem li = new ListItem();
                            li.Value = efTs[enumT][enumLen].enumItem.ToString();
                            li.Text = efTs[enumT][enumLen].itemDetail;
                            ddl.Items.Add(li);
                        }
                        ((DropDownList)placeHolder1.FindControl("ddl" + iLine.ToString() + strFldName)).Attributes.Add("onMouseOver", "this.style.backgroundColor='" + InputParams.tbxOnmouseoverColor + "';");
                        ((DropDownList)placeHolder1.FindControl("ddl" + iLine.ToString() + strFldName)).Attributes.Add("onMouseOut", "this.style.backgroundColor='" + InputParams.tbxOnmouseoutColor + "';");
                        continue;
                }

                #endregion switch
            }
            #endregion forField
        }
        #endregion  forTable
        longNum = 0;
        for (int i = 0; i < picLongchar.Length; i++)
        {
            if (picLongchar[i] == 12)
            {
                longNum++;
            }
        }
        rowSpan = longNum + 1;
        colSpan = fieldsCount - 1;
    }


    /// <summary>
    /// 获得查找字符串
    /// </summary>
    /// <returns></returns>
    protected string GetSearchStr()
    {
        EnumField[][] efTs = EnumField();
        Table[] tables = Tables();

        string strSearch;
        string strSelect = "SELECT DISTINCT ";
        string strFrom = " FROM ";
        string strWhere = " WHERE  1=1 ";
        string strOrderBy = "";
        int tag = 0;
        int iposition;
        string[] arrSearchStr = new string[fieldsCount];

        #region forTable
        int iLine = 0;
        for (int tableIndex = 0; tableIndex < tables.Length; tableIndex++)
        {
            string strTblName = tables[tableIndex].tableName;

            if (tableIndex == 0)
            {
                strFrom += strTblName;
            }
            else
            {
                strFrom += "," + strTblName;
            }
            if (tables[tableIndex].join.joinField != null)
            {
                strWhere += " AND " + strTblName + "." + tables[tableIndex].join.joinField + "=" + tables[tableIndex].join.joinRTable + "." + tables[tableIndex].join.joinRField;
            }

            #region forField
            for (int fieldIndex = 0; fieldIndex < tables[tableIndex].fields.Length; fieldIndex++)
            {
                iLine++;
                iposition = tables[tableIndex].fields[fieldIndex].position;
                string strFldName = tables[tableIndex].fields[fieldIndex].fieldName;
                if (tables[tableIndex].fields[fieldIndex].sortType != EnumSortType.NoSort)
                {
                    if (tag == 0)
                    {
                        strOrderBy += " ORDER BY " + strTblName + "." + strFldName + " " + tables[tableIndex].fields[fieldIndex].sortType;
                        tag = 1;
                    }
                    else
                        if (tag == 1)
                        {
                            strOrderBy += " ," + strTblName + "." + strFldName + " " + tables[tableIndex].fields[fieldIndex].sortType;
                        }
                }

                if (iposition == 0)
                {
                    arrSearchStr[iposition] = " " + strTblName + "." + strFldName + " AS " + "'" + tables[tableIndex].fields[fieldIndex].fieldShowName + "'";
                }
                else
                {
                    arrSearchStr[iposition] = ",";
                    arrSearchStr[iposition] += " " + strTblName + "." + strFldName + " AS " + "'" + tables[tableIndex].fields[fieldIndex].fieldShowName + "'";
                }
                switch (tables[tableIndex].fields[fieldIndex].fieldType)
                {

                    case EnumFieldType.numberType:
                        if ((((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString() + strFldName + "1")).Text != "") && (((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString() + strFldName + "2")).Text != ""))
                        {
                            strWhere += " AND ";
                            strWhere += strTblName + "." + strFldName + " >= " + float.Parse(((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString() + strFldName + "1")).Text);
                            strWhere += " AND ";
                            strWhere += strTblName + "." + strFldName + " <= " + float.Parse(((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString() + strFldName + "2")).Text);
                        }
                        continue;

                    case EnumFieldType.dateType:
                        if ((((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString() + strFldName + "1")).Text != "") && (((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString() + strFldName + "2")).Text != ""))
                        {
                            //strWhere += " AND ";
                            //strWhere += strTblName + "." + strFldName + " >= " + "'" + ((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString() + strFldName + "1")).Text + "'";
                            //strWhere += " AND ";
                            //strWhere += strTblName + "." + strFldName + " <= " + "'" + ((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString() + strFldName + "2")).Text + "'";

                            strWhere += " AND ";
                            strWhere += strTblName + "." + strFldName + " >= " + "'" + DateTime.Parse(((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString() + strFldName + "1")).Text) + "'";
                            strWhere += " AND ";
                            strWhere += strTblName + "." + strFldName + "<= " + "'" + DateTime.Parse(((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString() + strFldName + "2")).Text) + "'";

                        }
                        continue;

                    case EnumFieldType.enumType:
                        string strCho = "请选择";
                        string strDdl = ((DropDownList)placeHolder1.FindControl("ddl" + iLine.ToString() + strFldName)).Text.Trim();
                        if (strDdl != strCho.Trim())
                        {
                            int enumItem = int.Parse(strDdl);
                            strWhere += " AND ";
                            strWhere += strTblName + "." + strFldName + " =" + enumItem;
                        }
                        continue;

                    case EnumFieldType.boolType:

                        if (((RadioButton)placeHolder1.FindControl("rb" + iLine.ToString() + strFldName + "1")).Checked)
                        {
                            strWhere += " AND ";
                            strWhere += strTblName + "." + strFldName + " =1";
                        }
                        else
                            if (((RadioButton)placeHolder1.FindControl("rb" + iLine.ToString() + strFldName + "2")).Checked)
                            {
                                strWhere += " AND ";
                                strWhere += strTblName + "." + strFldName + " =0";
                            }
                        continue;

                    default:
                        if (((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString() + strFldName)).Text != "")
                        {
                            strWhere += " AND " + strTblName + "." + strFldName + " like" + "'%";
                            string str = ((TextBox)placeHolder1.FindControl("tbx" + iLine.ToString() + strFldName)).Text;
                            string[] strs = str.Split(' ');
                            for (int i = 0; i < strs.Length; i++)
                            {
                                if (i != strs.Length - 1)
                                {
                                    strWhere += strs[i].ToString();
                                    strWhere += "%";
                                }
                                else
                                {
                                    strWhere += strs[i].ToString();
                                    strWhere += "%'";
                                }
                            }
                        }
                        continue;

                }
            }
            #endregion forField
        }
        #endregion forTable
        int arrSearchStrIndex;
        for (arrSearchStrIndex = 0; arrSearchStrIndex < arrSearchStr.Length; arrSearchStrIndex++)
        {
            strSelect += arrSearchStr[arrSearchStrIndex];
        }


        strWhere +=" AND "+ singleSearch[0].Trim().ToString() + "." + singleSearch[1].Trim().ToString() + "='"+ int.Parse(singleSearch[2].Trim().ToString())+"'"; 
        strSearch = strSelect + " ," + mainTable  + "." + pKey + " " + strFrom + strWhere + strOrderBy;
        subSearchStr = strSelect + " ," +mainTable + "." + pKey + " " + strFrom + strWhere;

        return strSearch;
    }



    /// <summary>
    /// 生成查找结果表
    /// </summary>
    /// <param name="searchStr"></param>
    /// <param name="sortExpression"></param>
    /// <param name="sortDirection"></param>
    /// <returns></returns>
    protected DataTable GetSearchResultTable(string searchStr, string sortExpression, string sortDirection)
    {
        string connStr = ConnStr;
        SqlConnection conn = new SqlConnection(connStr);
        SqlDataAdapter da;
        if (conn.State.ToString() == "Closed")
        {
            conn.Open();
        }
        if (sortExpression != "")
        {
            searchStr = ViewState["subSearchStr"].ToString() + " ORDER BY " + sortExpression + " " + sortDirection;
        }

        da = new SqlDataAdapter(searchStr, connStr);

        DataTable dt = new DataTable();
        da.Fill(dt);

        recordCount = dt.Rows.Count;
        if (recordCount % InputParams.pageSize == 0)
        {
            pageCount = recordCount / InputParams.pageSize;
        }
        else
        {
            pageCount = (recordCount / InputParams.pageSize) + 1;
        }

        if (conn.State.ToString() == "Open")
        {
            conn.Close();
        }
        return dt;

    }


    /// <summary>
    /// 将查找结果表输出
    /// </summary>
    /// <param name="dt"></param>
    /// <param name="sortExpression"></param>
    /// <param name="sortDirection"></param>
    protected void ShowResult(DataTable dt, string sortExpression, string sortDirection)
    {
        placeHolder2.Controls.Clear();
        placeHolder2.EnableViewState = true;
        EnumField[][] efTs = EnumField();

        //生成表头strTop
        string str1 = "<table id='tableBody'   border='1' style='background-color:" + InputParams.resultTableColor + "'>" + "<tr style='color:Blue'>" + "<td  style='color:#871f78; width:" + InputParams.ckbLength + "'>";
        Literal ltl1 = new Literal();
        ltl1.Text = str1;
        placeHolder2.Controls.Add(ltl1);

        Literal ltl2 = new Literal();
        ltl2.Text = "<input id='CheckAll' type='checkbox' onclick='selectAll(this);'/>全选</td>";
        placeHolder2.Controls.Add(ltl2);

        string lkbStr = "";
        for (int ColIndex = 0; ColIndex < fieldsCount; ColIndex++)
        {
            if (picLongchar[ColIndex] != 12)
            {
                lkbStr = "<td style='font-size:15px; width:" + fieldTableShowLength[ColIndex];

                Literal ltl3 = new Literal();
                ltl3.Text = lkbStr + "'>";
                placeHolder2.Controls.Add(ltl3);

                LinkButton lkb = new LinkButton();
                lkb.CommandName = dt.Columns[ColIndex].Caption;
                lkb.Command += new CommandEventHandler(lkb_Command);
                lkb.ID = "lkb" + dt.Columns[ColIndex].Caption;
                placeHolder2.Controls.Add(lkb);

                if (dt.Columns[ColIndex].Caption == sortExpression)
                {
                    if (sortDirection == "ASC")
                    {
                        lkb.Text = dt.Columns[ColIndex].Caption + "↑";
                    }
                    else
                        if (sortDirection == "DESC")
                        {
                            lkb.Text = dt.Columns[ColIndex].Caption + "↓";
                        }
                }
                else
                {
                    lkb.Text = dt.Columns[ColIndex].Caption;
                }

                Literal ltl4 = new Literal();
                ltl4.Text = "</td>";
                placeHolder2.Controls.Add(ltl4);
            }
        }
        Literal ltl5 = new Literal();
        ltl5.Text = "</tr>";
        placeHolder2.Controls.Add(ltl5);

        //生成表的内容
        if (dt.Rows.Count != 0)
        {
            if (pagerIndex != pageCount)
            {
                for (int rdCount = 0; rdCount < InputParams.pageSize; rdCount++)
                {
                    Literal ltl11 = new Literal();
                    ltl11.Text = "<tr><td  rowspan='" + rowSpan.ToString() + "'>";
                    placeHolder2.Controls.Add(ltl11);

                    CheckBox ckb = new CheckBox();
                    ckb.ID = dt.Rows[(pagerIndex - 1) * InputParams.pageSize + rdCount][fieldsCount].ToString() + " " + rdCount;
                    placeHolder2.Controls.Add(ckb);

                    Literal ltl12 = new Literal();
                    ltl12.Text = " </td>";
                    placeHolder2.Controls.Add(ltl12);

                    AddRow(dt, rdCount, efTs);
                }
                Response.Write("<br/>");
            }
            else
            {
                for (int rdCount = 0; rdCount < recordCount - (pagerIndex - 1) * InputParams.pageSize; rdCount++)
                {
                    Literal ltl11 = new Literal();
                    ltl11.Text = "<tr><td  rowspan='" + rowSpan.ToString() + "'>";
                    placeHolder2.Controls.Add(ltl11);

                    CheckBox ckb = new CheckBox();
                    ckb.ID = dt.Rows[(pagerIndex - 1) * InputParams.pageSize + rdCount][fieldsCount].ToString() + " " + rdCount;

                    placeHolder2.Controls.Add(ckb);

                    Literal ltl12 = new Literal();
                    ltl12.Text = " </td>";
                    placeHolder2.Controls.Add(ltl12);

                    AddRow(dt, rdCount, efTs);
                }
            }

            Literal ltl6 = new Literal();
            ltl6.Text = "</table>";
            placeHolder2.Controls.Add(ltl6);

            WebCustomControl1_1.TotalRecord = recordCount;
            //WebCustomControl1_1.PageSize = InputParams.pageSize;
            //WebCustomControl1_1.ItemSize = InputParams.itemSize;
        }
    }



    /// <summary>
    /// 向页面输出的表格中添加一行
    /// </summary>
    /// <param name="dt"></param>
    /// <param name="rdCount"></param>
    /// <param name="efTs"></param>
    void AddRow(DataTable dt, int rdCount, EnumField[][] efTs)
    {

        string strLine1 = "";
        string strLine2 = "";
        for (int ColIndex = 0; ColIndex < fieldsCount; ColIndex++)
        {
            switch (picLongchar[ColIndex])
            {
                case 11:
                    strLine1 += "<td>" + dt.Rows[(pagerIndex - 1) * InputParams.pageSize + rdCount][ColIndex] + "</td>";
                    continue;
                case 12:
                    strLine2 += "<tr>";
                    strLine2 += "<td style='height:" + InputParams.longCharShowHeight + "'  colspan='" + colSpan.ToString() + "'>" + dt.Columns[ColIndex].Caption + "：" + dt.Rows[(pagerIndex - 1) * InputParams.pageSize + rdCount][ColIndex] + "</td></tr>";
                    continue;

                case 13:
                    strLine1 += "<td>" + dt.Rows[(pagerIndex - 1) * InputParams.pageSize + rdCount][ColIndex] + "</td>";
                    continue;

                case 14:
                    strLine1 += "<td>" + dt.Rows[(pagerIndex - 1) * InputParams.pageSize + rdCount][ColIndex] + "</td>";
                    continue;

                case 15:
                    strLine1 += "<td>" + dt.Rows[(pagerIndex - 1) * InputParams.pageSize + rdCount][ColIndex] + "</td>";
                    continue;
                case 16:
                    strLine1 += "<td><img  alt=''  height='" + InputParams.picTableHeight + "'  src='" + dt.Rows[(pagerIndex - 1) * InputParams.pageSize + rdCount][ColIndex] + "'/></td>";
                    continue;

                default:
                    string ss1 = dt.Rows[(pagerIndex - 1) * InputParams.pageSize + rdCount][ColIndex].ToString();

                    int enumItem = int.Parse(dt.Rows[(pagerIndex - 1) * InputParams.pageSize + rdCount][ColIndex].ToString());
                    int enumT = picLongchar[ColIndex];
                    int enumLen;
                    for (enumLen = 0; enumLen < efTs[enumT].Length; enumLen++)
                    {
                        if (efTs[enumT][enumLen].enumItem == enumItem)
                        {
                            break;
                        }
                    }
                    if (enumLen == efTs[enumT].Length)
                    {
                        enumLen = enumLen - 1;
                    }
                    strLine1 += "<td>" + efTs[enumT][enumLen].itemDetail + "</td>";
                    continue;
            }
        }

        Literal ltl13 = new Literal();
        ltl13.Text = strLine1 + "</tr>" + strLine2;
        placeHolder2.Controls.Add(ltl13);
    }

}
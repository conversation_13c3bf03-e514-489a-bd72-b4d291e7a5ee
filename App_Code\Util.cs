﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Web;

/// <summary>
/// Util 的摘要说明
/// </summary>
public class Util
{
    public Util()
    {
    }
    // 字符串是否为空
    public static bool IsNullOrEmpty(string input)
    {
        return string.IsNullOrEmpty(input);
    }

    // 字符串是否为空或仅包含空格
    public static bool IsNullOrWhiteSpace(string input)
    {
        return string.IsNullOrWhiteSpace(input);
    }

    // 去除字符串两端的空格
    public static string TrimWhitespace(string input)
    {
        return input?.Trim();
    }

    // 判断邮箱格式是否有效
    public static bool IsValidEmail(string email)
    {
        var emailPattern = @"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$";
        return Regex.IsMatch(email, emailPattern);
    }

    // 格式化日期
    public static string FormatDate(DateTime date, string format = "yyyy-MM-dd")
    {
        return date.ToString(format);
    }

    // 将字符串转换为日期，失败时返回null
    public static DateTime? TryParseDate(string input)
    {
        if (DateTime.TryParse(input, out DateTime date))
        {
            return date;
        }
        return null;
    }

    // 安全地对用户输入进行 HTML 编码
    public static string HtmlEncode(string input)
    {
        return System.Net.WebUtility.HtmlEncode(input);
    }

    // 安全地对 HTML 编码的输入进行解码
    public static string HtmlDecode(string input)
    {
        return System.Net.WebUtility.HtmlDecode(input);
    }

    // 判断字符串是否为数字
    public static bool IsNumeric(string input)
    {
        return double.TryParse(input, out _);
    }

    // 将字节数转换为可读格式（例如：1KB，10MB等）
    public static string FormatByteSize(long bytes)
    {
        string[] suffix = { "B", "KB", "MB", "GB", "TB" };
        int i = 0;
        double dblSByte = bytes;
        while (dblSByte >= 1024 && i < suffix.Length - 1)
        {
            dblSByte /= 1024;
            i++;
        }
        return string.Format("{0:0.##} {1}", dblSByte, suffix[i]);
    }

    // 生成随机字符串（包含字母和数字）
    public static string GenerateRandomString(int length)
    {
        const string validChars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890";
        StringBuilder result = new StringBuilder(length);
        Random random = new Random();

        for (int i = 0; i < length; i++)
        {
            result.Append(validChars[random.Next(validChars.Length)]);
        }

        return result.ToString();
    }

    // 获取当前时间戳（秒）
    public static long GetUnixTimestamp()
    {
        DateTimeOffset dateTimeOffset = DateTimeOffset.UtcNow;
        return dateTimeOffset.ToUnixTimeSeconds();
    }

    // 将 Unix 时间戳转换为 DateTime
    public static DateTime FromUnixTimestamp(long timestamp)
    {
        return DateTimeOffset.FromUnixTimeSeconds(timestamp).DateTime;
    }
}
﻿<html>
<head>
<title>图片头条新闻函数</title>
<meta content="text/html; charset=utf-8" http-equiv="content-type">
<link rel="stylesheet" type="text/css" href="pop.css">

<script type="text/javascript">

var wbtbWin;
if (window.opener){
	wbtbWin=launchParameters["wbtb"];
}else{
	wbtbWin=dialogArguments;
}


window.dialogWidth = "300px";
window.dialogHeight = "170px";

// 本窗口返回值
function ReturnValue(){
	
	var tmp;
	var str = "<div class=function>&lt;%=ImgHeadline(";
	tmp = document.theForm.num.value;
	if (tmp==""){
		str += "8,";
	}else{
		str += tmp +",";
	}
	tmp = document.theForm.type.options[document.theForm.type.selectedIndex].value;
	str += "\""+ tmp +"\",";
	tmp = document.theForm.width.value;
	if (tmp==""){
		str += "200,";
	}else{
		str += tmp +",";
	}
	str += document.theForm.showTitle.checked +",";
	tmp = document.theForm.maxLength.value;
	if (tmp==""){
		str += "20,";
	}else{
		str += tmp +",";
	}
	tmp = document.theForm.cols.value;
	if (tmp==""){
		str += "10";
	}else{
		str += tmp +"";
	}
	str += ")%&gt;</div>";
	
	wbtbWin.WBTB_InsertHtml(str);

//	window.returnValue = null;
	window.close();
}

</script>
</head>

<body>
<form name="theForm">
  <table border="0" cellpadding="5" align="center">
   <tr valign="baseline"> 
      <td>新闻数量<input name="num" type="text" size="10" value="8"></td>
      <td>显示方式<select name="type"><option value="normal" selected="selected">普通</option><option value="summary_left">左侧简介</option><option value="summary_right">右侧简介</option></select></td>
    </tr>
    <tr valign="baseline"> 
      <td>表格宽度<input name="width" type="text" size="10" value="200"></td>
      <td>显示标题<input name="showTitle" type="checkbox"></td>
    </tr>
    <tr valign="baseline"> 
      <td>标题长度<input name="maxLength" type="text" size="10" value="20"></td>
      <td>分列显示<input name="cols" type="text" size="3" value="10">列</td>
    </tr>
   <tr>
   	<td colspan="2" align="center">
   	 <button id="Ok" onclick="ReturnValue();">  确定  </button>&nbsp;
	<button onclick="window.close();">  取消  </button>
	</td>
   </tr>
  </table>
</form>
</body>
</html>

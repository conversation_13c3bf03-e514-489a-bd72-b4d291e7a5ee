﻿// 取通过URL传过来的参数 (格式如 ?Param1=Value1&Param2=Value2)
var URLParams = new Object() ;
var aParams = document.location.search.substr(1).split('&') ;
for (i=0 ; i < aParams.length ; i++) {
	var aParam = aParams[i].split('=') ;
	URLParams[aParam[0]] = aParam[1] ;
}


// 去空格，left,right,all可选
function BaseTrim(str){
	  lIdx=0;rIdx=str.length;
	  if (BaseTrim.arguments.length==2)
	    act=BaseTrim.arguments[1].toLowerCase()
	  else
	    act="all"
      for(var i=0;i<str.length;i++){
	  	thelStr=str.substring(lIdx,lIdx+1)
		therStr=str.substring(rIdx,rIdx-1)
        if ((act=="all" || act=="left") && thelStr==" "){
			lIdx++
        }
        if ((act=="all" || act=="right") && therStr==" "){
			rIdx--
        }
      }
	  str=str.slice(lIdx,rIdx)
      return str
}

// 基本信息提示，得到焦点并选定
function BaseAlert(theText,notice){
	alert(notice);
	theText.focus();
	theText.select();
	return false;
}

// 只允许输入数字
function IsDigit(){
	if (window.opener){
		return true;
	}else{
		return ((event.keyCode >= 48) && (event.keyCode <= 57));
	}
}

// 选颜色
var oInputColor;
var oImgColor;
function selColor(obj, oImg)
{
	oInputColor=obj;
	oImgColor=oImg;
	if (document.all){
		var arr = showModalDialog("selcolor.html", "", "dialogWidth:290px; dialogHeight:280px; status:0; help:0");
		if (arr != null && arr != "")
		{
			obj.value=arr;
			oImg.style.backgroundColor=arr;
		}
	}else{
			var fcolorWin = window.open("","fcolorWin","width=290,height=280");
			if (fcolorWin) {
				fcolorWin.focus();
			}else{
				alert("Please turn off your PopUp blocking software");
				return;
			}
		
			launchParameters = new Object();
			launchParameters['wbtb'] = window;
			launchParameters['commandName'] = 'setColor';
			fcolorWin.launchParameters = launchParameters;
			fcolorWin.location.href="selcolor.html";
	}
}

function setColor(color)
{
	oInputColor.value=color;
	oImgColor.style.backgroundColor=color;
}



// 搜索下拉框值与指定值匹配，并选择匹配项
function SearchSelectValue(o_Select, s_Value){
	for (var i=0;i<o_Select.length;i++){
		if (o_Select.options[i].value == s_Value){
			o_Select.selectedIndex = i;
			return true;
		}
	}
	return false;
}

// 转为数字型，并无前导0，不能转则返回""
function ToInt(str){
	str=BaseTrim(str);
	if (str!=""){
		var sTemp=parseFloat(str);
		if (isNaN(sTemp)){
			str="";
		}else{
			str=sTemp;
		}
	}
	return str;
}

// 是否有效的链接
function IsURL(url){
	var sTemp;
	var b=true;
	sTemp=url.substring(0,7);
	sTemp=sTemp.toUpperCase();
	if ((sTemp!="HTTP://")||(url.length<10)){
		b=false;
	}
	return b;
}

// 是否有效的扩展名
function IsExt(url, opt){
	var sTemp;
	var b=false;
	var s=opt.toUpperCase().split("|");
	for (var i=0;i<s.length ;i++ ){
		sTemp=url.substr(url.length-s[i].length-1);
		sTemp=sTemp.toUpperCase();
		s[i]="."+s[i];
		if (s[i]==sTemp){
			b=true;
			break;
		}
	}
	return b;
}

// 取完整链接
function GetHttpUrl(url){
	var sURL=document.URL;
	return sURL.substring(0,sURL.lastIndexOf("/wbTextBox/")+1)+url;
}

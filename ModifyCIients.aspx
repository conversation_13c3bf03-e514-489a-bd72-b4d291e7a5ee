﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="ModifyCIients.aspx.cs" Inherits="ModifyCIients" %>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html xmlns="http://www.w3.org/1999/xhtml">
<head id="Head1" runat="server">
    
    <title>修改页面</title>
    <meta name="viewport" content="width=device-width,height=device-height,initial-scale=0.65">
    <style type="text/css">
        body, table, tr, td {
            font-size: 12px;
            font-family: 宋体;
        }
        #modifysTable {
            width:80%;
        }

        @media (max-width: 768px) {
            body, table, tr, td {
                font-size: 25px;
                font-family: 宋体;
                max-width: 100%;
                overflow-x: auto;
                margin-top:-1%;
                /*            width:350px;
*/ 
                position: relative;
                margin-left:-5%;
            }
            #modifysTable {
                width:100%;
            }
        }
    </style>

</head>

<script src="js/time.js" type="text/javascript"></script>

<body>
    <form id="form1" method="post" runat="server">
        <table id="modifysTable"<%-- width="80%" --%>border="1" style="background: #e6e8fa; position: absolute; left: 10%; background-position: center; border-color: #666666">
            <tr>
                <td align="center">
                    <br />
                    <asp:LinkButton ID="hlkReturn" ForeColor="Blue" Text="返回首页" runat="server" OnClick="hlkReturn_Click" />
                    <br />
                    <br />
                    <div id="div1" runat="server">
                    </div>
                </td>
            </tr>
            <tr>
                <td align="center" style="border: 0px">
                    <br />
                    <asp:Button ID="btnModify" Width="100px" runat="server" Text="确定修改" OnClick="btnModify_Click" /><br />
                    <br />
                    <asp:LinkButton ID="LinkButton1" ForeColor="Blue" Text="返回首页" runat="server" OnClick="hlkReturn_Click" />
                    <br />
                    <br />
                </td>
            </tr>
        </table>
    </form>
</body>
</html>

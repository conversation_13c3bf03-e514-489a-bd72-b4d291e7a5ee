﻿<%@ Page Language="C#" AutoEventWireup="true" ValidateRequest="false" CodeFile="AddUsers.aspx.cs" Inherits="AddUsers" %>

<html xmlns="http://www.w3.org/1999/xhtml">
<head id="Head1" runat="server">
    <title>批量创建用户</title>
    <meta name="viewport" content="width=device-width,height=device-height,initial-scale=0.65">
    <style type="text/css">
        body, table, tr, td {
            font-size: 12px;
            font-family: 宋体;
        }
        #addUsersTable{
            width:80%;
        }
        @media (max-width: 768px) {
        body, table, tr, td {
            font-size: 25px;
            font-family: 宋体;
            max-width: 100%;
            overflow-x: auto;
            margin-top:1%;
            /*            width:350px;
*/ 
            position: relative;
            margin-left:-5%;
        }
        #addUsersTable{
            width:100%;
        }

        }
    </style>
</head>
<body>
    <form id="form1" method="post" runat="server">
        <table id="addUsersTable"  border="1" style="background: #e6e8fa; position: absolute; left: 10%; background-position: center; border-color: #666666">
            <tr>
                <td style="border: 0px; height: 27px;">
                    <asp:Label ID="lblInfo" runat="server" />请输入添加用户的个数：<asp:TextBox ID="TextBox1" runat="server"
                        OnTextChanged="TextBox1_TextChanged"></asp:TextBox>
                    <asp:Button ID="Button1" runat="server" OnClick="Button1_Click" Text="确定" Width="86px" /></td>
            </tr>
            <tr align="center">
                <td align="center" style="height: 42px">
                    <asp:HyperLink runat="server" ID="hklBak1" ForeColor="blue" Text="返回首页" NavigateUrl="Default.aspx" />
                    <br />
                    <br />
                    <div id="div1" runat="server">
                    </div>
                </td>
            </tr>
            <tr>
                <td align="center" style="border: 0px">
                    <br />
                    <asp:Button ID="btnAdd" Width="80px" runat="server" Text="添加" OnClick="btnAdd_Click" /><br />
                    <br />
                    <br />
                    <asp:HyperLink runat="server" ID="hklBak2" ForeColor="blue" Text="返回首页" NavigateUrl="Default.aspx" />
                </td>
            </tr>
        </table>
    </form>
</body>
</html>

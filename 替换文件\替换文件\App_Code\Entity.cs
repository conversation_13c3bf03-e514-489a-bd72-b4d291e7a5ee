﻿using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

/// <summary>
/// 数据类型
/// </summary>
public enum EnumFieldType
{
    charType = 11,//字符类型
    longcharType = 12,//长字符类型
    numberType = 13,//数字类型
    dateType = 14,//日期类型
    boolType = 15,//布尔类型
    picType = 16,//图片类型
    enumType = 17 //枚举类型
}

/// <summary>
/// 排序类型
/// </summary>
public enum EnumSortType
{
    NoSort = 0, //不排序
    ASC = 1, //升序
    DESC = 2  //降序
}

/// <summary>
/// 
/// </summary>
public struct EnumField
{
    public int enumItem;//对应的整型数值
    public string itemDetail;//具体的描述信息

    public EnumField(int eItem, string iDetail)
    {
        enumItem = eItem;
        itemDetail = iDetail;
    }
}


/// <summary>
///字段
/// </summary>

public struct Field
{
    public int position;//字段在页面中显示的次序
    public string fieldName;//字段在数据库中的名称
    public string fieldShowName;//字段显示的名称
    public int fieldTableShowLength;//字段显示在客户端表格中的宽度
    public EnumFieldType fieldType;//字段的数据类型，是枚举类型
    public EnumSortType sortType;//该字段的排序方式，是枚举类型
    public int enumTag;//如果某个字段类型是enumType（枚举类型），则此成员标志着该字段对应的枚举数组的索引
    public string linkPage;//如果需要对某个字段执行查找操作，可通过此项设置查找链接的页面
    public int sortOrder;//该字段排序的次序
    public string attentionMessage;//添加记录时，如果该成员不为空，则该字段为必填项

    public Field(int pos, string fldName, string fldShowName, int fldTableShowLength, EnumFieldType fldType, EnumSortType sType, int eTag, string lkPage, int sOrder, string attMessage)
    {
        position = pos;
        fieldName = fldName;
        fieldShowName = fldShowName;
        fieldTableShowLength = fldTableShowLength;
        fieldType = fldType;
        sortType = sType;
        enumTag = eTag;
        linkPage = lkPage;
        sortOrder = sOrder;
        attentionMessage = attMessage;
    }
}


/// <summary>
///表之间的连接
/// </summary>
public struct Join
{
    public string joinField;//当前表的连接字段
    public string joinRTable;//当前表所要连接的表
    public string joinRField;//当前表要连接的表中被连接的字段

    public Join(string jonField, string jonRTable, string jonRField)
    {
        joinField = jonField;
        joinRTable = jonRTable;
        joinRField = jonRField;
    }
}



/// <summary>
/// 表
/// </summary>
public struct Table
{
    public string tableName;//表名
    public Join join;//该表对应的连接信息
    public Field[] fields;//该表的字段

    public Table(string tblName, Join jon, Field[] flds)
    {
        tableName = tblName;
        join = jon;
        fields = flds;
    }
}



/// <summary>
/// 主要的输入参数
/// </summary>
public class InputParams
{

    #region  定义多表查找，批量删除操作的连接参数

    //部署服务器上的数据库相关参数信息
    public const string DataBaseServer = "**************";
    public const string userName = "limainfo";
    public const string password = "yimasoft";
    public const string dbName = "5city";

    //调试服务器数据库相关参数信息
    //public const string DataBaseServer = "localhost";
    //public const string userName = "sa";
    //public const string password = "sa";
    //public const string dbName = "TMS";

    #endregion

    #region 显示样式
    //定义查找结果每页现实的记录数以及页码数
    public const int pageSize = 10;
    public const int itemSize = 10;

    //字段显示名称的字体颜色
    public const string lblHeadColor = "#42426f";
    public const string lblFieldShowNameColor = "#871f78";

    //单选按钮的标签（“是”“否”的前景色）
    public const string rbYNColor = "#97694f";

    //鼠标移动时文本框的颜色变化
    public const string tbxOnmouseoverColor = " #EFFFFF";
    public const string tbxOnmouseoutColor = "#FFFFFF";

    //查找结果输出表格的背景色
    public const string resultTableColor = "#adeaea";


    public const string rowColor1 = "#E0FFFF";
    public const string rowColor2 = "#E8EBFC";//查找结果表里面的相邻两行的颜色不同
    public const string rowColorLongChar = "#FFFFFF";//长字符串的背景色

    //不同数据类型对应的查找输入框的宽度，为适应不同页面的需要，可在相关页面进行局部调整
    public const int tbxCharLength = 250;
    public const int tbxLongCharLength = 500;
    public const int tbxPicLength = 160;
    public const int tbxNumberLength = 100;
    public const int tbxDateLength = 110;
    public const int ddlWidth = 140;
    public const int ddlHeight = 60;

    //“分”“妙”下拉列表框的宽度
    public const int ddlTimeWidth = 60;
    
    //复选框宽度
    public const int ckbLength = 30;
    //文本框的高度
    public const int tbxHeight = 22;
    public const int tbxLongCharHeight = 40;

    //按钮现实的宽度
    public const int btnLength = 100;
    //图片显示的高度
    public const int picTableHeight = 80;
    //查找结果表里长字符串显示的高度
    public const int longCharShowHeight = 20;


    //查找、修改等链接的宽度
    public const int lkbSearch = 7;
    public const int lkbModify = 7;
    public const int lkbReport = 7;
    public const int lkbExamine = 7;
    public const int lkbDelete = 7;
    public const int lkbClose = 7;
    public const int lkbSelect = 100;

    #endregion
}
